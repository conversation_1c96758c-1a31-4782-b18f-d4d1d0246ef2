# EvoSpace POS System

A modern Point of Sale (POS) system built with Next.js for managing resources, services, products, and session.

## Features

- **Dashboard**: View key metrics and sales data with interactive charts
- **POS Interface**: Intuitive interface for processing sales and transactions
- **Product Management**: Add, edit, and manage products with inventory tracking
- **Resource Management**: Manage rooms, desks, and equipment with real-time status
- **Service Management**: Configure and sell various services
- **Member Management**: Track member information and purchase history
- **Session Management**: Dynamically manage active resource usage sessions
- **Backend API Integration**: Connects to a backend API (default: `http://localhost:5000`) for live data, with mock data fallback.
- **Reports**: Generate sales and performance reports with visualizations

## Tech Stack

- **Framework**: [Next.js](https://nextjs.org) with App Router
- **UI Components**: [Material UI](https://mui.com)
- **State Management**: [Zustand](https://github.com/pmndrs/zustand)
- **Charts**: [Chart.js](https://www.chartjs.org) with React-Chartjs-2
- **Styling**: Combination of Material UI system and Tailwind CSS
- **Typography**: Geist font family

## Getting Started

First, ensure the backend API server is running (typically at `http://localhost:5000`).
Then, run the development server for the frontend:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Mock Data

The application primarily fetches data from a backend API. Mock data is used as a fallback when the API is unavailable or for demonstration purposes. The mock data includes:
- Sample products with categories
- Services with pricing
- Resources (rooms, desks, equipment)
- Member information
- Transaction history
- Dashboard statistics

## Authentication

The system includes a mock authentication system with the following credentials:
- **Username**: admin
- **Name**: Administrator
- **Email**: <EMAIL>

## Project Structure

- `/src/app`: Next.js application pages
- `/src/components`: Reusable UI components
- `/src/lib`: Utilities, Zustand store (`store.ts`), API client (`apiClient.ts`), type definitions (`types.ts`), and mock data (`mockData.ts`)

## Future Enhancements

- Full backend integration and data persistence for all features
- User role management
- Advanced reporting features
- Receipt printing
- Mobile responsiveness improvements
- Dark mode support
