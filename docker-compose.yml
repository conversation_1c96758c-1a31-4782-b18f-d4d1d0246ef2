version: '3.8'

services:
  backend:
    build:
      context: ./evospace-admin
      dockerfile: Dockerfile
    container_name: evospace-admin-backend
    ports:
      - "5000:5000"
    networks:
      - evospace-network
    environment:
      FLASK_APP: app.py
      FLASK_RUN_HOST: 0.0.0.0
    # Optional: Mount a volume for persistent database data if using SQLite or similar
    # volumes:
    #   - ./evospace-admin/pear.db:/app/pear.db

  frontend:
    build:
      context: ./evospace-pos
      dockerfile: Dockerfile
    container_name: evospace-pos-frontend
    ports:
      - "3000:3000"
    networks:
      - evospace-network
    environment:
      # This environment variable needs to be configured in evospace-pos to point to the backend service
      NEXT_PUBLIC_API_BASE_URL: http://backend:5000
    depends_on:
      - backend

networks:
  evospace-network:
    driver: bridge