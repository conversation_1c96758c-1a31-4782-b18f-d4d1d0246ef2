import React from 'react';
import { <PERSON>, <PERSON>po<PERSON>, Button, TextField, Tabs, Tab, SelectChangeEvent, Paper, ToggleButton, ToggleButtonGroup, InputAdornment } from '@mui/material';
import { Add as AddIcon, Search as SearchIcon, ViewList as ListIcon, ViewModule as GridIcon, Map as MapIcon } from '@mui/icons-material';

interface ResourceControlsProps {
  onAddResource: () => void;
  searchQuery: string;
  onSearchChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  viewMode: 'list' | 'grid' | 'floorPlan';
  onViewModeChange: (mode: 'list' | 'grid' | 'floorPlan') => void;
  selectedFloor: number | null;
  onFloorChange: (event: SelectChangeEvent<string | number>) => void;
  floors: number[];
  selectedZone: string;
  onZoneChange: (event: SelectChangeEvent<string>) => void;
  zones: string[];
  resourceTypes: string[];
  tabValue: number;
  onTabChange: (event: React.SyntheticEvent, newValue: number) => void;
}

const ResourceControls: React.FC<ResourceControlsProps> = ({
  onAddResource,
  searchQuery,
  onSearchChange,
  viewMode,
  onViewModeChange,
  // selectedFloor, // Not used directly in this component's JSX yet, but passed for future use or prop drilling
  // onFloorChange, // Not used directly
  // floors, // Not used directly
  // selectedZone, // Not used directly
  // onZoneChange, // Not used directly
  // zones, // Not used directly
  resourceTypes,
  tabValue,
  onTabChange,
}) => {
  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Resource Management
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={onAddResource}
        >
          Add Resource
        </Button>
      </Box>

      <Paper elevation={2} sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap', // Allow wrapping on smaller screens
        gap: 2, // Add some gap between items
        mb: 3,
        p: 2,
        // border: '1px solid', // Removed border
        // borderColor: 'divider', // Removed borderColor
        borderRadius: 2 // Slightly more rounded
      }}>
        <TextField
          placeholder="Search Resources..."
          value={searchQuery}
          onChange={onSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ flexGrow: 1 }}
        />
        <ToggleButtonGroup
          value={viewMode}
          exclusive
          onChange={(event, newViewMode) => {
            if (newViewMode !== null) {
              onViewModeChange(newViewMode);
            }
          }}
          aria-label="View mode"
        >
          <ToggleButton value="list" aria-label="List view">
            <ListIcon sx={{ mr: 0.5 }} />
            List
          </ToggleButton>
          <ToggleButton value="grid" aria-label="Grid view">
            <GridIcon sx={{ mr: 0.5 }} />
            Grid
          </ToggleButton>
          <ToggleButton value="floorPlan" aria-label="Floor plan view">
            <MapIcon sx={{ mr: 0.5 }} />
            Map
          </ToggleButton>
        </ToggleButtonGroup>
      </Paper>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Tabs value={tabValue} onChange={onTabChange} aria-label="resource type tabs" variant="scrollable" scrollButtons="auto">
          {resourceTypes.map((type, index) => (
            <Tab key={index} label={type.charAt(0).toUpperCase() + type.slice(1)} id={`resource-tab-${index}`} aria-controls={`resource-tabpanel-${index}`} />
          ))}
        </Tabs>
      </Box>
    </Box>
  );
};

export default ResourceControls;