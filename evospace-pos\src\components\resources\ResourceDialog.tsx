import React from 'react';
import {
  <PERSON>alog,
  DialogT<PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stack,
  Box,
  SelectChangeEvent,
  Alert,
  Typography,
} from '@mui/material';
import { ResourceStatus } from '@/lib/types';

// Define the ResourceFormData type locally or import if it's shared
export type ResourceFormData = {
  id: number;
  name: string;
  type: string;
  status: ResourceStatus;
  hourly_rate: number | string;
  capacity?: number | string;
  floor: string;
  zone?: string;
  x: number;
  y: number;
  width: number;
  height: number;
};

interface ResourceDialogProps {
  open: boolean;
  editMode: boolean;
  formData: ResourceFormData;
  onClose: () => void;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onSelectChange: (e: SelectChangeEvent<string>) => void;
  onSubmit: () => void;
  error?: string | null;
  loading?: boolean;
  formSubmitted?: boolean; // To show validation errors after first submit attempt
}

const ResourceDialog: React.FC<ResourceDialogProps> = ({
  open,
  editMode,
  formData,
  onClose,
  onInputChange,
  onSelectChange,
  onSubmit,
  error,
  loading,
  formSubmitted,
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      aria-labelledby="resource-dialog-title"
    >
      <DialogTitle id="resource-dialog-title">
        {editMode ? 'Edit Resource' : 'Add New Resource'}
      </DialogTitle>
      <DialogContent dividers>
        {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
        <Stack direction={{ xs: 'column', md: 'row' }} spacing={3}>
          <Box sx={{ width: { xs: '100%', md: '50%' } }}>
            <TextField
              autoFocus
              margin="dense"
              name="name"
              label="Resource Name"
              type="text"
              fullWidth
              value={formData.name}
              onChange={onInputChange}
              required
              error={formSubmitted && !formData.name}
              helperText={formSubmitted && !formData.name ? "Name is required" : ""}
              sx={{ mb: 2 }}
            />
            <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
              <InputLabel id="type-label">Type</InputLabel>
              <Select
                labelId="type-label"
                name="type"
                value={formData.type}
                onChange={onSelectChange}
                label="Type"
              >
                <MenuItem value="table">Table</MenuItem>
                <MenuItem value="room">Room</MenuItem>
                <MenuItem value="desk">Desk</MenuItem>
                <MenuItem value="equipment">Equipment</MenuItem>
              </Select>
            </FormControl>
            <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
              <InputLabel id="status-label">Status</InputLabel>
              <Select
                labelId="status-label"
                name="status"
                value={formData.status}
                onChange={onSelectChange}
                label="Status"
              >
                <MenuItem value="available">Available</MenuItem>
                <MenuItem value="booked">Booked</MenuItem>
                <MenuItem value="maintenance">Maintenance</MenuItem>
                <MenuItem value="in-use">In Use</MenuItem>
              </Select>
            </FormControl>
            <TextField
              margin="dense"
              name="hourly_rate"
              label="Hourly Rate"
              type="number"
              fullWidth
              value={formData.hourly_rate}
              onChange={onInputChange}
              sx={{ mb: 2 }}
            />
            {formData.type === 'room' && ( // Conditionally render capacity for rooms
                <TextField
                    margin="dense"
                    name="capacity"
                    label="Capacity"
                    type="number"
                    fullWidth
                    value={formData.capacity || ''}
                    onChange={onInputChange}
                />
            )}
          </Box>
          <Box sx={{ width: { xs: '100%', md: '50%' } }}>
            {/* Floor and Zone can be simple text fields or selects if predefined values exist */}
            <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
                <InputLabel id="floor-label">Floor</InputLabel>
                <Select
                    labelId="floor-label"
                    name="floor"
                    value={formData.floor || '1'} // Default to '1' if undefined
                    onChange={onSelectChange}
                    label="Floor"
                >
                    {/* Example floors, ideally these would come from props or a config */}
                    <MenuItem value="1">Floor 1</MenuItem>
                    <MenuItem value="2">Floor 2</MenuItem>
                    <MenuItem value="3">Floor 3</MenuItem>
                </Select>
            </FormControl>
            <TextField
              margin="dense"
              name="zone"
              label="Zone (Optional)"
              type="text"
              fullWidth
              value={formData.zone || ''}
              onChange={onInputChange}
              sx={{ mb: 2 }}
            />
            {/* Coordinates and Dimensions for Floor Plan View */}
            <Typography variant="subtitle2" sx={{ mt: 2, mb: 1 }}>Floor Plan Details (Optional)</Typography>
            <Stack direction="row" spacing={2}>
              <TextField name="x" label="X" type="number" value={formData.x} onChange={onInputChange} sx={{ width: '50%' }} />
              <TextField name="y" label="Y" type="number" value={formData.y} onChange={onInputChange} sx={{ width: '50%' }} />
            </Stack>
            <Stack direction="row" spacing={2} sx={{ mt: 1 }}>
              <TextField name="width" label="Width" type="number" value={formData.width} onChange={onInputChange} sx={{ width: '50%' }} />
              <TextField name="height" label="Height" type="number" value={formData.height} onChange={onInputChange} sx={{ width: '50%' }} />
            </Stack>
          </Box>
        </Stack>
      </DialogContent>
      <DialogActions sx={{ px: 3, py: 2 }}>
        <Button onClick={onClose} color="inherit" disabled={loading}>
          Cancel
        </Button>
        <Button onClick={onSubmit} variant="contained" color="primary" disabled={loading}>
          {loading ? 'Saving...' : (editMode ? 'Save Changes' : 'Add Resource')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ResourceDialog;