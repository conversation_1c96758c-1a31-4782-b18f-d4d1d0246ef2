// Mock data for the POS system

import { Resource, User, Session, SessionItem, Transaction } from './types';

// User data
export const users: User[] = [
  {
    id: 1,
    username: 'admin',
    name: 'Administrator',
    email: '<EMAIL>',
    role: 'admin'
  },
  {
    id: 2,
    username: 'staff',
    name: 'Staff User',
    email: '<EMAIL>',
    role: 'staff'
  }
];

// Products data
export const products = [
  { id: 1, name: 'Coffee', category: 'Beverages', price: 2.50, stock: 100, image: '' },
  { id: 2, name: 'Tea', category: 'Beverages', price: 1.80, stock: 120, image: '' },
  { id: 3, name: 'Sandwich', category: 'Food', price: 4.50, stock: 30, image: '' },
  { id: 4, name: 'Croissant', category: 'Bakery', price: 2.20, stock: 45, image: '' },
  { id: 5, name: 'Salad', category: 'Food', price: 5.50, stock: 25, image: '' },
  { id: 6, name: '<PERSON><PERSON>', category: 'Desserts', price: 3.80, stock: 20, image: '' },
  { id: 7, name: 'Water Bottle', category: 'Beverages', price: 1.00, stock: 150, image: '' },
  { id: 8, name: 'Muffin', category: 'Bakery', price: 2.00, stock: 40, image: '' }
];

// Categories
export const categories = [
  { id: 1, name: 'Beverages' },
  { id: 2, name: 'Food' },
  { id: 3, name: 'Bakery' },
  { id: 4, name: 'Desserts' }
];

// Services
export const services = [
  { id: 1, name: 'Room Rental', price: 25.00, duration: 60, description: 'Rent a meeting room for 1 hour', category: 'Workspace' },
  { id: 2, name: 'Workspace', price: 15.00, duration: 60, description: 'Access to workspace for 1 hour', category: 'Workspace' },
  { id: 3, name: 'Printing', price: 0.10, unit: 'per page', description: 'Black and white printing service', category: 'Office Services' },
  { id: 4, name: 'Color Printing', price: 0.25, unit: 'per page', description: 'Color printing service', category: 'Office Services' },
  { id: 5, name: 'Coffee Service', price: 10.00, description: 'Coffee service for meetings (serves 5)', category: 'Catering' }
];

// Resources
export const resources: Resource[] = [
  {
    id: 1,
    name: 'Conference Room A',
    type: 'room',
    capacity: 12,
    hourly_rate: 50.00,
    status: 'available',
    x: 0,
    y: 0,
    width: 3,
    height: 2,
    floor: 1,
    zone: 'North Wing'
  },
  {
    id: 2,
    name: 'Office Desk 1',
    type: 'desk',
    capacity: 1,
    hourly_rate: 10.00,
    status: 'booked',
    x: 3,
    y: 0,
    width: 1,
    height: 1,
    floor: 1,
    zone: 'North Wing'
  },
  {
    id: 3,
    name: 'Meeting Room B',
    type: 'room',
    capacity: 8,
    hourly_rate: 35.00,
    status: 'maintenance',
    x: 0,
    y: 3,
    width: 2,
    height: 2,
    floor: 1,
    zone: 'South Wing'
  },
  {
    id: 4,
    name: 'Office Desk 2',
    type: 'desk',
    capacity: 1,
    hourly_rate: 10.00,
    status: 'available',
    x: 3,
    y: 1,
    width: 1,
    height: 1,
    floor: 1,
    zone: 'North Wing'
  },
  {
    id: 5,
    name: 'Projector',
    type: 'equipment',
    capacity: 0,
    hourly_rate: 15.00,
    status: 'available',
    x: 5,
    y: 5,
    width: 1,
    height: 1,
    floor: 1,
    zone: 'Storage'
  },
  {
    id: 6,
    name: 'Conference Room C',
    type: 'room',
    capacity: 20,
    hourly_rate: 75.00,
    status: 'available',
    x: 6,
    y: 0,
    width: 3,
    height: 3,
    floor: 2,
    zone: 'Executive Wing'
  },
  {
    id: 7,
    name: 'Standing Desk 1',
    type: 'desk',
    capacity: 1,
    hourly_rate: 12.00,
    status: 'available',
    x: 1,
    y: 0,
    width: 1,
    height: 1,
    floor: 2,
    zone: 'Open Space'
  },
  {
    id: 8,
    name: 'Meeting Pod',
    type: 'room',
    capacity: 4,
    hourly_rate: 25.00,
    status: 'booked',
    x: 2,
    y: 0,
    width: 2,
    height: 2,
    floor: 2,
    zone: 'Open Space'
  },
  {
    id: 9,
    name: 'Laptop',
    type: 'equipment',
    capacity: 0,
    hourly_rate: 20.00,
    status: 'available',
    x: 5,
    y: 6,
    width: 1,
    height: 1,
    floor: 1,
    zone: 'Storage'
  },
  {
    id: 10,
    name: 'Focus Room',
    type: 'room',
    capacity: 1,
    hourly_rate: 15.00,
    status: 'available',
    x: 9,
    y: 0,
    width: 1,
    height: 1,
    floor: 1,
    zone: 'Quiet Zone'
  }
];

// Members
export const members = [
  { id: 1, name: 'John Doe', email: '<EMAIL>', phone: '555-1234', visits: 10, totalSpent: 250.50 },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>', phone: '555-5678', visits: 5, totalSpent: 120.75 },
  { id: 3, name: 'Bob Johnson', email: '<EMAIL>', phone: '555-9012', visits: 8, totalSpent: 180.25 },
  { id: 4, name: 'Alice Brown', email: '<EMAIL>', phone: '555-3456', visits: 12, totalSpent: 320.00 },
  { id: 5, name: 'Charlie Wilson', email: '<EMAIL>', phone: '555-7890', visits: 3, totalSpent: 75.50 }
];

// Sessions data
export const sessions: Session[] = [];

// Transactions
export const transactions: Transaction[] = [
  { 
    id: 1, 
    memberId: 1, 
    sessions: [
      {
        id: 101, // This should be a session ID
        resource_id: 1, // Example resource_id for a mock session
        start_time: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        end_time: new Date().toISOString(),
        status: 'completed',
        products: [{ id: 1, type: 'product', name: 'Coffee', price: 2.50, quantity: 2 } as SessionItem],
        services: []
      } as Session // Cast to Session for type compatibility
    ] as Session[], 
    totalAmount: 5.00, 
    createdAt: new Date().toISOString(), 
    paymentMethod: 'Credit Card', 
    status: 'completed' 
  },
  {
    id: 2, 
    memberId: 2, 
    sessions: [
      {
        id: 102,
        resource_id: 2,
        start_time: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
        end_time: new Date(Date.now() - 3600000).toISOString(), // ended 1 hour ago
        status: 'completed',
        products: [
          { id: 3, type: 'product', name: 'Sandwich', price: 4.50, quantity: 1 },
          { id: 2, type: 'product', name: 'Tea', price: 1.80, quantity: 1 }
        ] as SessionItem[],
        services: []
      } as Session
    ] as Session[],
    totalAmount: 6.30, 
    createdAt: new Date().toISOString(), 
    paymentMethod: 'Cash', 
    status: 'completed' 
  }
];

// Dashboard stats
export const dashboardStats = {
  dailySales: 1250,
  monthlyRevenue: 32500,
  memberCount: 145,
  resourceUtilization: 78,
  salesGrowth: 12.5,
  ordersToday: 24,
  membersToday: 8,
  salesByCategory: [
    { category: 'Beverages', amount: 12500 },
    { category: 'Food', amount: 8750 },
    { category: 'Bakery', amount: 6250 },
    { category: 'Desserts', amount: 5000 }
  ],
  popularProducts: [
    { name: 'Coffee', sales: 350 },
    { name: 'Sandwich', sales: 210 },
    { name: 'Tea', sales: 180 },
    { name: 'Croissant', sales: 150 },
    { name: 'Water Bottle', sales: 120 }
  ],
  recentTransactions: [
    { id: 1, member: 'John Doe', amount: 15.50, time: '09:45 AM' },
    { id: 2, member: 'Jane Smith', amount: 8.20, time: '10:15 AM' },
    { id: 3, member: 'Robert Johnson', amount: 22.75, time: '11:30 AM' },
    { id: 4, member: 'Emily Davis', amount: 12.40, time: '01:20 PM' },
    { id: 5, member: 'Michael Wilson', amount: 18.90, time: '02:45 PM' }
  ]
};
