const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5000/api';

export interface ApiCallOptions {
  method?: string;
  token?: string | null;
  body?: unknown;
  params?: Record<string, string | number | boolean | undefined>;
}

export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data: T;
  errors?: Record<string, string[]>;
}

export interface PaginatedApiResponse<T> {
  success: boolean;
  data: {
    items: T[];
    meta: {
      page: number;
      per_page: number;
      total_pages: number;
      total_items: number;
    };
    links: {
      first?: string;
      next?: string;
      prev?: string;
      last?: string;
    };
  };
}

export class ApiError extends Error {
  status?: number;
  errors?: Record<string, string[]>;

  constructor(message: string, status?: number, errors?: Record<string, string[]>) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.errors = errors;
  }
}

export async function apiCall<T>(
  endpoint: string,
  options: ApiCallOptions = {}
): Promise<T> {
  const { method = 'GET', token = null, body = null, params = {} } = options;

  const headers: HeadersInit = {
    'Accept': 'application/json',
  };

  if (body) {
    headers['Content-Type'] = 'application/json';
  }

  // First check if token is provided in options
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  // If not, try to get it from localStorage
  else if (typeof window !== 'undefined') {
    const storedToken = localStorage.getItem('authToken');
    if (storedToken) {
      headers['Authorization'] = `Bearer ${storedToken}`;
    }
  }

  // Build query string from params
  const queryParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined) {
      queryParams.append(key, String(value));
    }
  });

  const queryString = queryParams.toString();
  const url = `${API_BASE_URL}${endpoint}${queryString ? `?${queryString}` : ''}`;

  const response = await fetch(url, {
    method,
    headers,
    body: body ? JSON.stringify(body) : undefined,
  });

  if (!response.ok) {
    // Handle unauthorized access
    if (response.status === 401 && typeof window !== 'undefined') {
      console.error('Unauthorized (401). Redirecting to login.');
      localStorage.removeItem('authToken');
      window.location.href = '/auth/login';
    }

    // Parse error response
    let errorMessage = `API Error (${response.status}): `;
    let errors: Record<string, string[]> | undefined;

    try {
      const errorResult = await response.json();
      errorMessage += errorResult.message || response.statusText || 'Unknown error';
      errors = errorResult.errors;
    } catch {
      errorMessage += response.statusText || 'Unknown error (failed to parse error response)';
    }

    console.error(`${method} ${url} failed:`, errorMessage);
    throw new ApiError(errorMessage, response.status, errors);
  }

  // Handle No Content response
  if (response.status === 204) {
    return undefined as T;
  }

  // Parse response data
  const responseData = await response.json();

  // Check if the response follows the API format with success flag
  if ('success' in responseData && !responseData.success) {
    const errorMessage = responseData.message || 'API request failed';
    console.error(`${method} ${url} failed:`, errorMessage);
    throw new ApiError(errorMessage, response.status, responseData.errors);
  }

  return responseData as T;
}