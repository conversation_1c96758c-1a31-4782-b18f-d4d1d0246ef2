from applications.extensions import db
from sqlalchemy.dialects.mysql import INTEGER
import datetime

class PosMember(db.Model):
    __tablename__ = 'pos_member'

    id = db.Column(INTEGER(unsigned=True), primary_key=True, autoincrement=True)
    name = db.Column(db.String(255), nullable=False)
    email = db.Column(db.String(255), nullable=True, unique=True) # Email should ideally be unique
    phone = db.Column(db.String(50), nullable=True, unique=True) # Phone also ideally unique
    
    # Store isolation - Multi-store support
    store_id = db.Column(db.Integer, db.<PERSON>Key('admin_dept.id'), nullable=False)
    store = db.relationship('Dept', backref='pos_members')
    
    # Enhanced personal information
    birthday = db.Column(db.Date, nullable=True)
    address = db.Column(db.String(255), nullable=True)
    city = db.Column(db.String(100), nullable=True)
    state = db.Column(db.String(100), nullable=True)
    postal_code = db.Column(db.String(20), nullable=True)
    country = db.Column(db.String(100), nullable=True, default='China')
    
    # Membership details
    membership_level = db.Column(db.String(50), nullable=True, default='regular')  # e.g., 'regular', 'silver', 'gold', 'platinum'
    join_date = db.Column(db.Date, nullable=True, default=datetime.date.today)
    expiry_date = db.Column(db.Date, nullable=True)  # For membership expiration
    
    # Activity tracking
    total_spent = db.Column(db.Numeric(10, 2), nullable=False, default=0.00)
    visits = db.Column(db.Integer, nullable=False, default=0)
    last_visit_date = db.Column(db.DateTime, nullable=True)
    
    # Additional information
    notes = db.Column(db.Text, nullable=True)  # Staff notes about the member
    preferences = db.Column(db.Text, nullable=True)  # Member preferences
    referral_source = db.Column(db.String(100), nullable=True)  # How they heard about the business
    
    # System fields
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)
    status = db.Column(db.String(50), nullable=False, default='active')  # e.g., 'active', 'inactive', 'suspended'
