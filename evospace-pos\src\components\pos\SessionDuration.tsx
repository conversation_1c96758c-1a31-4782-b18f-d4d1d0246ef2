"use client";

import React, { useState, useEffect } from 'react';
import { Typography } from '@mui/material';
import usePosStore from '@/lib/store';

interface SessionDurationProps {
  resource_id: number;
}

const SessionDuration: React.FC<SessionDurationProps> = ({ resource_id }) => {
  const [duration, setDuration] = useState(0);
  const { getActiveSession } = usePosStore();

  useEffect(() => {
    const session = getActiveSession(resource_id);
    if (!session) {
      setDuration(0); // Reset or ensure duration is 0 if no session
      return;
    }

    const updateDuration = () => {
      const durationInMinutes = (Date.now() - new Date(session.start_time).getTime()) / (1000 * 60);
      setDuration(Math.round(durationInMinutes));
    };

    updateDuration(); // Initial call
    const interval = setInterval(updateDuration, 1000); // Update every second

    return () => clearInterval(interval); // Cleanup interval on unmount
  }, [resource_id, getActiveSession]);

  // Calculate billable duration (rounded up to nearest 10 minutes)
  const billableDuration = Math.ceil(duration / 10) * 10;
  
  // Only render if there is a session and duration is positive
  if (duration <= 0) {
    const session = getActiveSession(resource_id);
    if (!session) return null; 
  }

  return (
    <>
      {duration} min
      {billableDuration > duration && (
        <Typography component="span" variant="caption" sx={{ ml: 0.5, opacity: 0.7 }}>
          (Billed: {billableDuration} min)
        </Typography>
      )}
    </>
  );
};

export default SessionDuration;
