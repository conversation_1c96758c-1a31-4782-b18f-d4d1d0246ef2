from flask import Blueprint, request, jsonify, g, url_for
from applications.extensions import db, ma
from applications.models.pos_session import PosSession, PosSessionService, PosSessionProduct
from applications.schemas.pos_session_schema import (
    PosSessionSchema, PosSessionCreateSchema, PosSessionUpdateSchema,
    AddServiceSchema, AddProductSchema
)
from applications.api.auth_api import token_required, store_context_required
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import joinedload
from marshmallow import ValidationError
from datetime import datetime
import logging

# Set up logger
logger = logging.getLogger(__name__)

session_api_bp = Blueprint('session_api', __name__, url_prefix='/api/sessions')

# Create schemas
session_schema = PosSessionSchema()
sessions_schema = PosSessionSchema(many=True)
session_create_schema = PosSessionCreateSchema()
session_update_schema = PosSessionUpdateSchema()
add_service_schema = AddServiceSchema()
add_product_schema = AddProductSchema()

@session_api_bp.route('', methods=['POST'])
@token_required
@store_context_required
def create_session():
    """Create a new session
    
    Request Body:
    - user_id: Optional, defaults to current user's ID
    - member_id: Optional, ID of the member associated with the session
    - resource_id: Optional, ID of the resource associated with the session
    - notes: Optional, any notes about the session
    """
    try:
        # Validate request data
        json_data = request.get_json()
        if not json_data:
            return jsonify({
                'success': False,
                'message': 'No input data provided'
            }), 400
        
        # Use the current user's ID if not provided
        if 'user_id' not in json_data:
            json_data['user_id'] = g.current_user.id
        
        # Validate and deserialize input
        try:
            data = session_create_schema.load(json_data)
        except ValidationError as err:
            return jsonify({
                'success': False,
                'message': 'Validation error',
                'errors': err.messages
            }), 400

        
        # Create new session and assign store_id
        session = PosSession(**data)
        session.store_id = g.store_id  # Set store context from JWT token
        db.session.add(session)
        db.session.commit()
        
        # Log the session creation
        logger.info(f"Session created: ID={session.id}")
        
        return jsonify({
            'success': True,
            'message': 'Session created successfully',
            'data': {
                'session': session_schema.dump(session)
            }
        }), 201
        
    except ValidationError as err:
        return jsonify({'message': 'Validation error', 'errors': err.messages}), 400
    except SQLAlchemyError as e:
        db.session.rollback()
        return jsonify({'message': 'Database error', 'error': str(e)}), 500

@session_api_bp.route('', methods=['GET'])
@token_required
@store_context_required
def get_sessions():
    """Get all active sessions filtered by store
    
    Returns all sessions with status='open' for the current store.
    """
    try:
        # Get all active sessions for the current store
        query = PosSession.query.filter(
            PosSession.status == 'open',
            PosSession.store_id == g.store_id
        )
        
        # Apply default sorting by start_time descending (newest first)
        query = query.order_by(PosSession.start_time.desc())
        
        # Get all sessions without pagination
        sessions = query.all()
        
        # Log the query for monitoring
        logger.info(f"Retrieved active sessions for store {g.store_id}, count: {len(sessions)}")
        
        return jsonify({
            'success': True,
            'data': {
                'items': sessions_schema.dump(sessions),
                'count': len(sessions)
            }
        }), 200
        
    except SQLAlchemyError as e:
        return jsonify({'message': 'Database error', 'error': str(e)}), 500

@session_api_bp.route('/<int:id>/close', methods=['PUT'])
@token_required
@store_context_required
def close_session(id):
    """Close a specific session
    
    Request Body:
    - status: Required, should be 'closed'
    - notes: Optional, any closing notes
    """
    try:
        # Find the session within store context
        session = PosSession.query.filter(
            PosSession.id == id,
            PosSession.store_id == g.store_id
        ).first()
        
        if not session:
            return jsonify({
                'success': False,
                'message': 'Session not found'
            }), 404
        
        if session.status == 'closed':
            return jsonify({
                'success': False,
                'message': 'Session is already closed'
            }), 400
        
        # Get request data for status and notes
        json_data = request.get_json() or {}
        status = json_data.get('status')
        
        if status != 'closed':
            return jsonify({
                'success': False,
                'message': 'status must be set to "closed" to close a session'
            }), 400
        
        # Close the session
        session.status = 'closed'
        session.end_time = datetime.now()
        
        if 'notes' in json_data:
            session.notes = json_data['notes']
        
        db.session.commit()
        
        # Log the session closure
        logger.info(f"Session closed: ID={session.id}, User ID={session.user_id}")
        
        return jsonify({
            'success': True,
            'message': 'Session closed successfully',
            'data': {
                'session': session_schema.dump(session)
            }
        }), 200
        
    except SQLAlchemyError as e:
        db.session.rollback()
        return jsonify({'message': 'Database error', 'error': str(e)}), 500

@session_api_bp.route('/<int:id>/services', methods=['POST'])
@token_required
@store_context_required
def add_service_to_session(id):
    """Add a service to a session
    
    Request Body:
    - service_id: Required, ID of the service to add
    - quantity: Optional, quantity of the service (default: 1)
    - price: Optional, override price (if different from service's default price)
    - notes: Optional, any notes about this service
    """
    try:
        # Find the session within store context
        session = PosSession.query.filter(
            PosSession.id == id,
            PosSession.store_id == g.store_id
        ).first()
        
        if not session:
            return jsonify({
                'success': False,
                'message': 'Session not found'
            }), 404
        
        if session.status == 'closed':
            return jsonify({
                'success': False,
                'message': 'Cannot add service to a closed session'
            }), 400
        
        # Validate request data
        json_data = request.get_json()
        if not json_data:
            return jsonify({
                'success': False,
                'message': 'No input data provided'
            }), 400
        
        # Validate and deserialize input
        try:
            data = add_service_schema.load(json_data)
        except ValidationError as err:
            return jsonify({
                'success': False,
                'message': 'Validation error',
                'errors': err.messages
            }), 400
            
        # Check if this service already exists in the session
        existing_service = PosSessionService.query.filter_by(
            session_id=id,
            service_id=data['service_id']
        ).first()
        
        if existing_service:
            # Update the existing service quantity
            existing_service.quantity += data.get('quantity', 1)
            session_service = existing_service
            db.session.commit()
            logger.info(f"Updated existing service in session: Session ID={id}, Service ID={session_service.service_id}, New Quantity={session_service.quantity}")
        else:
            # Create a new session service
            data['session_id'] = id
            session_service = PosSessionService(**data)
            db.session.add(session_service)
            db.session.commit()
            logger.info(f"Added new service to session: Session ID={id}, Service ID={session_service.service_id}, Quantity={session_service.quantity}")
        
        return jsonify({
            'success': True,
            'message': 'Service added to session successfully',
            'data': {
                'session_service_id': session_service.id,
                'session_id': id,
                'service_id': session_service.service_id
            }
        }), 201
        
    except ValidationError as err:
        return jsonify({'message': 'Validation error', 'errors': err.messages}), 400
    except SQLAlchemyError as e:
        db.session.rollback()
        return jsonify({'message': 'Database error', 'error': str(e)}), 500

@session_api_bp.route('/<int:id>/products', methods=['POST'])
@token_required
@store_context_required
def add_product_to_session(id):
    """Add a product to a session
    
    Request Body:
    - product_id: Required, ID of the product to add
    - quantity: Optional, quantity of the product (default: 1)
    - price: Optional, override price (if different from product's default price)
    - notes: Optional, any notes about this product
    """
    try:
        # Find the session within store context
        session = PosSession.query.filter(
            PosSession.id == id,
            PosSession.store_id == g.store_id
        ).first()
        
        if not session:
            return jsonify({
                'success': False,
                'message': 'Session not found'
            }), 404
        
        if session.status == 'closed':
            return jsonify({
                'success': False,
                'message': 'Cannot add product to a closed session'
            }), 400
        
        # Validate request data
        json_data = request.get_json()
        if not json_data:
            return jsonify({
                'success': False,
                'message': 'No input data provided'
            }), 400
        
        # Validate and deserialize input
        try:
            data = add_product_schema.load(json_data)
        except ValidationError as err:
            return jsonify({
                'success': False,
                'message': 'Validation error',
                'errors': err.messages
            }), 400
            
        # Check if this product already exists in the session
        existing_product = PosSessionProduct.query.filter_by(
            session_id=id,
            product_id=data['product_id']
        ).first()
        
        if existing_product:
            # Update the existing product quantity
            existing_product.quantity += data.get('quantity', 1)
            session_product = existing_product
            db.session.commit()
            logger.info(f"Updated existing product in session: Session ID={id}, Product ID={session_product.product_id}, New Quantity={session_product.quantity}")
        else:
            # Create a new session product
            data['session_id'] = id
            session_product = PosSessionProduct(**data)
            db.session.add(session_product)
            db.session.commit()
            logger.info(f"Added new product to session: Session ID={id}, Product ID={session_product.product_id}, Quantity={session_product.quantity}")
        
        return jsonify({
            'success': True,
            'message': 'Product added to session successfully',
            'data': {
                'session_product_id': session_product.id,
                'session_id': id,
                'product_id': session_product.product_id
            }
        }), 201
        
    except ValidationError as err:
        return jsonify({'message': 'Validation error', 'errors': err.messages}), 400
    except SQLAlchemyError as e:
        db.session.rollback()
        return jsonify({'message': 'Database error', 'error': str(e)}), 500
