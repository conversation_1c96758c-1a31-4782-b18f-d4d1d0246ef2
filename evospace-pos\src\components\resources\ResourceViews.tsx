import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Stack,
  Paper,
  Tooltip,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  // DragIndicator as DragIndicatorIcon, // Removed as not used in 'original' design
} from '@mui/icons-material';
import { Resource, ResourceStatus } from '@/lib/types';
import { useTheme as useMuiTheme } from '@mui/material/styles'; // Alias to avoid conflict with custom useTheme

// Props for individual view components
interface ResourceCardItemProps {
  resource: Resource;
  onEdit: (resource: Resource) => void;
  onDelete: (resource_id: number) => void;
  onStatusClick: (resource_id: number, currentStatus: ResourceStatus) => void;
  isDarkMode: boolean;
  getResourceIcon: (type: string) => React.ReactElement;
  getStatusChipColor: (status: ResourceStatus) => "default" | "primary" | "secondary" | "error" | "info" | "success" | "warning";
}

const ResourceCardItem: React.FC<ResourceCardItemProps> = ({
  resource,
  onEdit,
  onDelete,
  onStatusClick,
  isDarkMode,
  getResourceIcon,
  getStatusChipColor,
}) => (
  <Card
    elevation={3}
    sx={{
      display: 'flex',
      flexDirection: 'column',
      height: '100%', // Ensure card takes full height of grid item
      border: '1px solid',
      borderColor: 'divider',
      transition: 'box-shadow 0.3s ease-in-out, transform 0.2s ease-in-out',
      '&:hover': {
        boxShadow: isDarkMode ? '0 8px 16px rgba(0,0,0,0.5)' : '0 8px 16px rgba(0,0,0,0.2)',
        transform: 'translateY(-4px)',
      }
    }}
  >
    <CardContent sx={{ flexGrow: 1, pb: 1 }}> {/* Reduced padding bottom */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1.5 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box
            sx={{
              mr: 1.5,
              p: 0.5,
              borderRadius: '50%',
              backgroundColor: isDarkMode ? 'grey.700' : 'grey.200',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {getResourceIcon(resource.type)}
          </Box>
          <Box sx={{ maxWidth: 140, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
            <Tooltip title={resource.name} placement="top-start">
              <Typography variant="h6" component="div" sx={{ fontWeight: 'medium', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                {resource.name}
              </Typography>
            </Tooltip>
          </Box>
        </Box>
        <Chip
          label={resource.status.charAt(0).toUpperCase() + resource.status.slice(1)}
          color={getStatusChipColor(resource.status)}
          size="small"
          onClick={() => onStatusClick(resource.id, resource.status)}
          sx={{ cursor: 'pointer', fontWeight: 'medium' }}
        />
      </Box>
      <Box sx={{ mb: 1.5, flex: 1 }}> {/* Ensure this box can grow */}
        <Stack spacing={0.5}> {/* Reduced spacing */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Typography variant="body2" color="text.secondary">Rate:</Typography>
            <Typography variant="body2" sx={{ fontWeight: 'medium' }}>${resource.hourly_rate}/hr</Typography>
          </Box>
          {(resource.type === 'room' || resource.type === 'desk') && resource.capacity && (
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="body2" color="text.secondary">Capacity:</Typography>
              <Typography variant="body2" sx={{ fontWeight: 'medium' }}>{resource.capacity}</Typography>
            </Box>
          )}
          {resource.floor && (
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="body2" color="text.secondary">Floor:</Typography>
              <Typography variant="body2" sx={{ fontWeight: 'medium' }}>{resource.floor}</Typography>
            </Box>
          )}
          {resource.zone && (
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="body2" color="text.secondary">Zone:</Typography>
              <Typography variant="body2" sx={{ fontWeight: 'medium' }}>{resource.zone}</Typography>
            </Box>
          )}
        </Stack>
      </Box>
    </CardContent>
    <CardActions sx={{ justifyContent: 'flex-end', pt: 0, pb: 1.5, px: 2 }}> {/* Adjusted padding */}
      <Button size="small" startIcon={<EditIcon />} onClick={() => onEdit(resource)} color="primary">
        Edit
      </Button>
      <Button size="small" startIcon={<DeleteIcon />} onClick={() => onDelete(resource.id)} color="error">
        Delete
      </Button>
    </CardActions>
  </Card>
);

interface ResourceGridProps {
  resources: Resource[];
  onEdit: (resource: Resource) => void;
  onDelete: (resource_id: number) => void;
  onStatusClick: (resource_id: number, currentStatus: ResourceStatus) => void;
  isDarkMode: boolean;
  getResourceIcon: (type: string) => React.ReactElement;
  getStatusChipColor: (status: ResourceStatus) => "default" | "primary" | "secondary" | "error" | "info" | "success" | "warning";
}

const ResourceGrid: React.FC<ResourceGridProps> = ({ resources, ...props }) => {
  if (resources.length === 0) {
    return (
      <Box sx={{ width: '100%', p: 4, textAlign: 'center' }}>
        <Paper sx={{ p: 4, borderRadius: 2, backgroundColor: props.isDarkMode ? 'grey.800' : 'grey.100' }}>
          <Typography variant="h6" color="text.secondary">No resources found.</Typography>
          <Typography variant="body1" color="text.secondary">Try adjusting your search or filters.</Typography>
        </Paper>
      </Box>
    );
  }
  return (
    <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)', md: 'repeat(3, 1fr)', lg: 'repeat(4, 1fr)' }, gap: 3 }}>
      {resources.map((resource) => (
        <ResourceCardItem key={resource.id} resource={resource} {...props} />
      ))}
    </Box>
  );
};


interface ResourceListProps {
  resources: Resource[];
  onEdit: (resource: Resource) => void;
  onDelete: (resource_id: number) => void;
  onStatusClick: (resource_id: number, currentStatus: ResourceStatus) => void;
  getResourceIcon: (type: string) => React.ReactElement;
  getStatusChipColor: (status: ResourceStatus) => "default" | "primary" | "secondary" | "error" | "info" | "success" | "warning";
}

const ResourceList: React.FC<ResourceListProps> = ({ resources, onEdit, onDelete, onStatusClick, getResourceIcon, getStatusChipColor }) => {
  if (resources.length === 0) {
    return (
      <Box sx={{ width: '100%', textAlign: 'center', py: 5 }}>
        <Typography variant="subtitle1" color="textSecondary">No resources to display.</Typography>
      </Box>
    );
  }
  return (
    <Stack spacing={2}>
      {resources.map(resource => (
        <Card key={resource.id} elevation={2} sx={{ display: 'flex', alignItems: 'center', p: 2 }}>
          <Box sx={{ mr: 2, display: 'flex', alignItems: 'center' }}>{getResourceIcon(resource.type)}</Box>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h6">{resource.name}</Typography>
            <Typography variant="body2" color="text.secondary">
              Type: {resource.type} | Rate: ${resource.hourly_rate}/hr
              {resource.capacity && ` | Capacity: ${resource.capacity}`}
              {resource.floor && ` | Floor: ${resource.floor}`}
              {resource.zone && ` | Zone: ${resource.zone}`}
            </Typography>
          </Box>
          <Stack direction="row" spacing={1} alignItems="center">
            <Chip
              label={resource.status.charAt(0).toUpperCase() + resource.status.slice(1)}
              color={getStatusChipColor(resource.status)}
              size="small"
              onClick={() => onStatusClick(resource.id, resource.status)}
              sx={{ cursor: 'pointer' }}
            />
            <Button size="small" startIcon={<EditIcon />} onClick={() => onEdit(resource)}>Edit</Button>
            <Button size="small" startIcon={<DeleteIcon />} onClick={() => onDelete(resource.id)} color="error">Delete</Button>
          </Stack>
        </Card>
      ))}
    </Stack>
  );
};

interface FloorPlanViewProps {
  resources: Resource[];
  onEdit: (resource: Resource) => void;
  onDelete: (resource_id: number) => void;
  onStatusClick: (resource_id: number, currentStatus: ResourceStatus) => void;
  onResourcePositionUpdate: (resource_id: number, x: number, y: number) => void;
  selectedFloor: number | null;
  isDarkMode: boolean;
  getResourceColor: (status: ResourceStatus) => { bg: string; border: string };
  getStatusChipColor: (status: ResourceStatus) => "default" | "primary" | "secondary" | "error" | "info" | "success" | "warning"; // Added for consistency
  // getResourceIcon: (type: string) => React.ReactElement; // Not used in original floor plan item design
}

const FloorPlanView: React.FC<FloorPlanViewProps> = ({
  resources,
  onEdit,
  // onDelete, // Not directly used in draggable items, but could be added to a context menu
  onStatusClick,
  onResourcePositionUpdate,
  selectedFloor,
  isDarkMode,
  getResourceColor,
  getStatusChipColor, // Added
  // getResourceIcon, // Removed
}) => {
  const muiTheme = useMuiTheme();
  // Filter resources for the selected floor. Original code didn't check for r.x/r.y here,
  // but it's safer to assume they might be null if not set for the floor plan.
  // The original getResourcePosition defaults x/y to 0 if null.
  const floorResources = resources.filter(r => r.floor === selectedFloor);

  // Grid settings from original
  const cellSize = 60; // px
  const gridGap = 4; // px
  const defaultGridWidthCells = 12; // Default number of cells wide
  const defaultGridHeightCells = 8; // Default number of cells tall

  // Calculate the maximum x and y positions (in grid units) from resources
  const maxXCell = Math.max(...floorResources.map(r => (r.x ?? 0) + (r.width ?? 1)), defaultGridWidthCells);
  const maxYCell = Math.max(...floorResources.map(r => (r.y ?? 0) + (r.height ?? 1)), defaultGridHeightCells);

  // Adjust grid dimensions to fit all resources with some padding (in grid units)
  const effectiveGridWidthCells = Math.max(defaultGridWidthCells, maxXCell + 2); // Add 2 cells padding
  const effectiveGridHeightCells = Math.max(defaultGridHeightCells, maxYCell + 2); // Add 2 cells padding

  // Canvas dimensions in pixels
  const canvasWidth = effectiveGridWidthCells * (cellSize + gridGap);
  const canvasHeight = effectiveGridHeightCells * (cellSize + gridGap);
  const outerContainerHeight = canvasHeight + 100; // For title and legend

  // Function to calculate resource size in pixels based on grid units
  const getResourceSize = (resource: Resource) => {
    const widthInCells = resource.width || 1; // Default to 1 cell wide
    const heightInCells = resource.height || 1; // Default to 1 cell high

    return {
      width: widthInCells * cellSize + (widthInCells - 1) * gridGap,
      height: heightInCells * cellSize + (heightInCells - 1) * gridGap
    };
  };

  // Function to get position in pixels based on grid cell indices
  const getResourcePosition = (resource: Resource) => {
    const xCell = resource.x ?? 0; // Default to cell 0
    const yCell = resource.y ?? 0; // Default to cell 0

    return {
      left: xCell * (cellSize + gridGap),
      top: yCell * (cellSize + gridGap)
    };
  };

  if (selectedFloor === null) {
    return <Typography>Please select a floor to view the plan.</Typography>;
  }

  return (
    <Box sx={{ // Outer container
      position: 'relative',
      width: '100%',
      height: `${outerContainerHeight}px`, // Set height based on original logic
      border: `1px solid ${isDarkMode ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)'}`,
      borderRadius: 1,
      overflow: 'auto',
      p: 2,
      bgcolor: isDarkMode ? 'rgba(0, 0, 0, 0.05)' : 'rgba(0, 0, 0, 0.02)' // Outer background
    }}>
      {/* Legend - Moved to top */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        flexWrap: 'wrap', // Allow legend items to wrap on smaller screens
        gap: 2, // Spacing between legend items
        mb: 2,  // Margin bottom from the grid canvas
        p: 1,   // Padding around the legend area
      }}>
        {/* Fixed legend items from original snippet */}
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box sx={{ width: 16, height: 16, backgroundColor: 'rgba(76, 175, 80, 0.7)', borderRadius: 0.5, mr: 0.5 }} />
          <Typography variant="caption">Available</Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box sx={{ width: 16, height: 16, backgroundColor: 'rgba(244, 67, 54, 0.7)', borderRadius: 0.5, mr: 0.5 }} />
          <Typography variant="caption">Booked</Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box sx={{ width: 16, height: 16, backgroundColor: 'rgba(255, 152, 0, 0.7)', borderRadius: 0.5, mr: 0.5 }} />
          <Typography variant="caption">Maintenance</Typography>
        </Box>
        {/* Dynamically add 'in-use' if it's a possible status and not covered, or adjust as needed */}
        {(['in-use'] as ResourceStatus[]).map(status => {
          // This logic ensures 'in-use' is added if not already hardcoded
          // You might want to make this more robust based on all possible statuses
          if (status === 'in-use') { // Example: only add 'in-use'
            const color = getResourceColor(status); // Assuming getResourceColor can provide a color for 'in-use'
            return (
                <Box key={status} sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box sx={{ width: 16, height: 16, backgroundColor: color.bg /* Fallback if needed */, borderRadius: 0.5, mr: 0.5 }} />
                  <Typography variant="caption">{status.charAt(0).toUpperCase() + status.slice(1)}</Typography>
                </Box>
            );
          }
          return null;
        })}
      </Box>
      {/* Floor Plan Grid Canvas */}
      <Box
        className="floor-plan-grid"
        sx={{ 
          position: 'relative', 
          width: `${canvasWidth}px`, 
          height: `${canvasHeight}px`,
          margin: '0 auto',
          background: isDarkMode 
            ? 'linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px), linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px)'
            : 'linear-gradient(to right, rgba(0, 0, 0, 0.05) 1px, transparent 1px), linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px)',
          backgroundSize: `${cellSize}px ${cellSize}px`
        }}
        onDragOver={(e) => {
          e.preventDefault();
        }}
      >
        {/* Grid lines for reference */}
        <Box sx={{ 
          position: 'absolute', 
          top: 0, 
          left: 0, 
          width: '100%', 
          height: '100%', 
          backgroundImage: 'linear-gradient(#ddd 1px, transparent 1px), linear-gradient(90deg, #ddd 1px, transparent 1px)',
          backgroundSize: `${cellSize}px ${cellSize}px`,
          opacity: 0.5
        }} />

        {/* Resources will be mapped here, absolutely positioned onto this grid canvas */}
        {floorResources.length === 0 && (
          <Box sx={{ width: '100%', height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', position: 'absolute' }}>
            <Typography variant="body1" color="text.secondary">
              No resources found on Floor {selectedFloor}. Try adjusting your filters or adding resources with position data.
            </Typography>
          </Box>
        )}
        {floorResources.map(resource => {
          const size = getResourceSize(resource); // Now returns pixel dimensions based on cell size
          const position = getResourcePosition(resource); // Now returns pixel top/left based on cell indices
          const colors = getResourceColor(resource.status);
          // const resourceIdText = `T-${resource.id.toString().padStart(2, '0')}`; // From original

          // TODO: Refactor draggable item to <Box> and apply original's styling and D&D logic
          const resourceIdText = `T-${resource.id.toString().padStart(2, '0')}`;

          return (
            <Box // Changed from Paper to Box to match original structure
              key={resource.id}
              data-resource-id={resource.id} // From original
              sx={{
                position: 'absolute',
                left: `${position.left}px`,
                top: `${position.top}px`,
                width: `${size.width}px`,
                height: `${size.height}px`,
                backgroundColor: colors.bg,
                border: '2px solid',
                borderColor: colors.border, // Use resolved color
                borderRadius: 2, // From original
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                padding: 1, // From original
                cursor: 'move', // From original
                transition: 'all 0.2s ease', // From original
                '&:hover': { // From original
                  boxShadow: muiTheme.shadows[3], // From original
                  transform: 'scale(1.02)' // From original
                },
                userSelect: 'none', // From original
                touchAction: 'none' // From original
              }}
              draggable={true} // Standard HTML draggable attribute
              onDragStart={(e) => {
                e.currentTarget.setAttribute('dragging', 'true'); // Mark as dragging
                e.dataTransfer.setData('text/plain', resource.id.toString()); // Original uses text/plain
                e.dataTransfer.effectAllowed = 'move';
                
                const targetRect = e.currentTarget.getBoundingClientRect();
                const offsetX = e.clientX - targetRect.left;
                const offsetY = e.clientY - targetRect.top;
                e.currentTarget.setAttribute('data-drag-offset-x', offsetX.toString());
                e.currentTarget.setAttribute('data-drag-offset-y', offsetY.toString());

                // Custom drag image logic from original snippet
                const dragImg = e.currentTarget.cloneNode(true) as HTMLElement;
                dragImg.style.position = 'absolute';
                dragImg.style.left = '-9999px'; // Position off-screen
                // Use currentTarget's dimensions for the drag image
                dragImg.style.width = `${e.currentTarget.offsetWidth}px`;
                dragImg.style.height = `${e.currentTarget.offsetHeight}px`;
                dragImg.style.opacity = '0.7'; // Make it slightly transparent
                dragImg.style.zIndex = '10000'; // Ensure it's on top
                document.body.appendChild(dragImg);
                e.dataTransfer.setDragImage(dragImg, offsetX, offsetY);

                // Clean up the cloned element after the drag operation
                setTimeout(() => {
                  if (document.body.contains(dragImg)) {
                    document.body.removeChild(dragImg);
                  }
                }, 0);
              }}
              onDrag={(e) => { // From original, ensures dragging attribute stays if needed
                if (!e.currentTarget.hasAttribute('dragging')) {
                  e.currentTarget.setAttribute('dragging', 'true');
                }
              }}
              onDragEnd={(e) => {
                if (!e.currentTarget.hasAttribute('dragging')) return; // Only process if it was dragging
                e.currentTarget.removeAttribute('dragging');

                const gridContainer = (e.currentTarget as HTMLElement).closest('.floor-plan-grid');
                if (!gridContainer) return;

                const gridRect = gridContainer.getBoundingClientRect();
                const offsetX = parseInt(e.currentTarget.getAttribute('data-drag-offset-x') || '0');
                const offsetY = parseInt(e.currentTarget.getAttribute('data-drag-offset-y') || '0');

                const dropXpx = e.clientX - gridRect.left - offsetX;
                const dropYpx = e.clientY - gridRect.top - offsetY;

                let newXCell = Math.round(dropXpx / (cellSize + gridGap));
                let newYCell = Math.round(dropYpx / (cellSize + gridGap));

                const resourceWidthCells = resource.width || 1;
                const resourceHeightCells = resource.height || 1;
                newXCell = Math.max(0, Math.min(newXCell, effectiveGridWidthCells - resourceWidthCells));
                newYCell = Math.max(0, Math.min(newYCell, effectiveGridHeightCells - resourceHeightCells));
                
                if (newXCell !== (resource.x ?? 0) || newYCell !== (resource.y ?? 0)) {
                    onResourcePositionUpdate(resource.id, newXCell, newYCell);
                }
                e.currentTarget.removeAttribute('data-drag-offset-x');
                e.currentTarget.removeAttribute('data-drag-offset-y');
              }}
              onClick={(e) => { // Original: single click if not dragging
                if (!e.currentTarget.hasAttribute('dragging')) {
                  onEdit(resource); // Assuming onEdit is the correct prop (vs onEditClick from original snippet)
                }
              }}
            >
              <Typography variant="subtitle2" sx={{
                fontWeight: 'bold',
                color: muiTheme.palette.getContrastText(colors.bg), // Original: 'white', adapt
                textAlign: 'center',
                fontSize: '0.8rem' // From original
              }}>
                {resourceIdText}
              </Typography>
              
              {/* Conditional rendering of name based on size, from original */}
              {size.width >= 120 && ( // Threshold from original
                <Typography variant="body2" sx={{
                  color: muiTheme.palette.getContrastText(colors.bg), // Original: 'white', adapt
                  textAlign: 'center',
                  fontSize: '0.7rem', // From original
                  mt: 0.5 // From original
                }}>
                  {resource.name}
                </Typography>
              )}
              
              {resource.status !== 'available' && ( // From original
                <Chip
                  label={resource.status.charAt(0).toUpperCase() + resource.status.slice(1)}
                  size="small"
                  color={getStatusChipColor(resource.status)} // Use consistent chip color
                  sx={{
                    fontSize: '0.6rem', // From original
                    height: '20px', // From original
                    mt: 0.5, // From original
                    // backgroundColor: colors.border, // Removed, using Chip's color prop
                    // color: muiTheme.palette.getContrastText(colors.border), // Removed, Chip handles contrast text with color prop
                    '& .MuiChip-label': { // From original
                      padding: '0 6px'
                    }
                  }}
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent triggering item's onClick for edit
                    onStatusClick(resource.id, resource.status);
                  }}
                />
              )}
            </Box>
          );
        })}
      </Box>
    </Box>
  );
};

interface ResourceViewsProps {
  viewMode: 'list' | 'grid' | 'floorPlan';
  resources: Resource[];
  onEditResource: (resource: Resource) => void;
  onDeleteResource: (resource_id: number) => void;
  onStatusClick: (resource_id: number, currentStatus: ResourceStatus) => void;
  onResourcePositionUpdate: (resource_id: number, x: number, y: number) => void;
  selectedFloor: number | null;
  isDarkMode: boolean;
  // Helper functions to be passed down or defined within ResourcesPage and passed
  getResourceIcon: (type: string) => React.ReactElement;
  getStatusChipColor: (status: ResourceStatus) => "default" | "primary" | "secondary" | "error" | "info" | "success" | "warning";
  getResourceColor: (status: ResourceStatus) => { bg: string; border: string };
  loading?: boolean;
  error?: string | null;
}

const ResourceViews: React.FC<ResourceViewsProps> = ({
  viewMode,
  resources,
  onEditResource,
  onDeleteResource,
  onStatusClick,
  onResourcePositionUpdate,
  selectedFloor,
  isDarkMode,
  getResourceIcon,
  getStatusChipColor,
  getResourceColor,
  loading,
  error,
}) => {

  if (loading) {
    return <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}><Typography>Loading resources...</Typography></Box>;
  }

  if (error) {
    // This error is for the main content area if resources fail to load for views
    // It's different from dialog-specific errors.
    // return <Alert severity="error" sx={{ m: 2 }}>{error}</Alert>;
    // Decided to let the page handle the main error display for now.
  }


  if (resources.length === 0 && !loading && !error) {
     // This message is shown when filters result in no resources, not for loading/error states
     return (
      <Box sx={{ width: '100%', p: 4, textAlign: 'center' }}>
        <Paper sx={{ p: 4, borderRadius: 2, backgroundColor: isDarkMode ? 'grey.800' : 'grey.100' }}>
          <Typography variant="h6" color="text.secondary">No Resources Found</Typography>
          <Typography variant="body1" color="text.secondary">
            There are no resources matching your current filters. Try adjusting your search or filter criteria.
          </Typography>
        </Paper>
      </Box>
    );
  }


  switch (viewMode) {
    case 'list':
      return <ResourceList
                resources={resources}
                onEdit={onEditResource}
                onDelete={onDeleteResource}
                onStatusClick={onStatusClick}
                getResourceIcon={getResourceIcon}
                getStatusChipColor={getStatusChipColor}
              />;
    case 'grid':
      return <ResourceGrid
                resources={resources}
                onEdit={onEditResource}
                onDelete={onDeleteResource}
                onStatusClick={onStatusClick}
                isDarkMode={isDarkMode}
                getResourceIcon={getResourceIcon}
                getStatusChipColor={getStatusChipColor}
              />;
    case 'floorPlan':
      return <FloorPlanView
                resources={resources}
                onEdit={onEditResource}
                onDelete={onDeleteResource} // onDelete might be used differently here, e.g. context menu
                onStatusClick={onStatusClick}
                onResourcePositionUpdate={onResourcePositionUpdate}
                selectedFloor={selectedFloor}
                isDarkMode={isDarkMode}
                getResourceColor={getResourceColor}
                getStatusChipColor={getStatusChipColor} // Added
                // getResourceIcon is removed as it's not part of FloorPlanViewProps
              />;
    default:
      return <Typography>Unknown view mode.</Typography>;
  }
};

export default ResourceViews;
export { ResourceGrid, ResourceList, FloorPlanView, ResourceCardItem };