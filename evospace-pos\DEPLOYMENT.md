# EvoSpace POS 部署指南

本指南说明如何使用 Docker 部署 EvoSpace POS 应用程序。

## 前提条件

- 部署服务器上安装了 Docker
- Git（可选，用于克隆仓库）

## 部署步骤

### 1. 克隆或复制应用程序

```bash
git clone <仓库URL>
cd evospace-pos
```

或者将应用程序文件复制到服务器。

### 2. 使用 Docker 构建和运行

```bash
# 构建 Docker 镜像
docker build -t evospace-pos .

# 启动容器
docker run -d -p 3000:3000 --name evospace-pos evospace-pos

# 查看日志
docker logs -f evospace-pos
```

### 3. 验证部署

在浏览器中打开：
```
http://<服务器IP>:3000
```

## 配置

### 环境变量

您可以通过在 Docker 运行命令中添加环境变量来自定义应用程序：

```bash
docker run -d -p 3000:3000 \
  -e NODE_ENV=production \
  --name evospace-pos evospace-pos
```

### 端口

默认情况下，应用程序在端口 3000 上运行。您可以在 Docker 运行命令中更改此设置：

```bash
docker run -d -p 8080:3000 --name evospace-pos evospace-pos
```

这会将主机端口 8080 映射到容器端口 3000。

## 更新应用程序

要更新应用程序：

```bash
# 拉取最新代码（如果使用 git）
git pull

# 停止并删除旧容器
docker stop evospace-pos
docker rm evospace-pos

# 重新构建镜像
docker build -t evospace-pos .

# 启动新容器
docker run -d -p 3000:3000 --name evospace-pos evospace-pos
```

## 故障排除

### 容器无法启动

检查日志：
```bash
docker logs evospace-pos
```

### 应用程序无法访问

验证容器是否正在运行：
```bash
docker ps
```

检查端口是否正确映射：
```bash
docker port evospace-pos
```

## 数据备份

要备份您的数据，您可以在 Docker 运行命令中创建卷挂载：

```bash
docker run -d -p 3000:3000 \
  -v ./data:/app/data \
  --name evospace-pos evospace-pos
```

这将在主机机器上的 `./data` 目录中保存数据。

## 生产环境注意事项

对于生产环境，建议：

1. 使用 HTTPS 保护您的应用程序
2. 设置自动重启策略
3. 实施健康检查

```bash
docker run -d -p 3000:3000 \
  --restart unless-stopped \
  --health-cmd "curl -f http://localhost:3000 || exit 1" \
  --health-interval 30s \
  --health-timeout 10s \
  --health-retries 3 \
  --name evospace-pos evospace-pos
