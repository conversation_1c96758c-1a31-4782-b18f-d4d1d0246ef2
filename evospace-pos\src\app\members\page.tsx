"use client";

import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  InputAdornment,
  Stack,
  Avatar,
  IconButton,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  History as HistoryIcon
} from '@mui/icons-material';
import usePosStore from '@/lib/store';
import ConfirmDialog from '@/components/ConfirmDialog';

interface MemberFormData {
  id?: number;
  name: string;
  email: string;
  phone: string;
}

export default function MembersPage() {
  const { members, addMember, updateMember, deleteMember, fetchMembers, authUser } = usePosStore();
  const [searchQuery, setSearchQuery] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [formData, setFormData] = useState<MemberFormData>({
    name: '',
    email: '',
    phone: ''
  });
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [memberToDelete, setMemberToDelete] = useState<number | null>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [historyDialogOpen, setHistoryDialogOpen] = useState(false);
  const [selectedMember, setSelectedMember] = useState<typeof members[0] | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const filteredMembers = (Array.isArray(members) ? members : []).filter(member =>
    (member.name ?? '').toLowerCase().includes(searchQuery.toLowerCase()) ||
    (member.email ?? '').toLowerCase().includes(searchQuery.toLowerCase()) ||
    (member.phone ?? '').includes(searchQuery)
  );

  useEffect(() => {
    if (authUser.token) {
      fetchMembers().catch(err => {
        // Optionally set an error state here to display to the user
        console.error("Failed to fetch members on mount/auth change:", err);
        setError("Could not load members. Please try again later.");
      });
    }
  }, [fetchMembers, authUser.token]);

  const handleOpenDialog = (edit = false, member?: typeof members[0]) => {
    if (edit && member) {
      setFormData({
        id: member.id,
        name: member.name,
        email: member.email,
        phone: member.phone
      });
      setEditMode(true);
    } else {
      setFormData({
        name: '',
        email: '',
        phone: ''
      });
      setEditMode(false);
    }
    setOpenDialog(true);
    setError(null);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setError(null);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async () => {
    setLoading(true);
    setError(null);
    try {
      if (!editMode) {
        await addMember({
          name: formData.name,
          email: formData.email,
          phone: formData.phone
        });
      } else if (formData.id && editMode) { // Ensure editMode is also true
        await updateMember(formData.id, {
          name: formData.name,
          email: formData.email,
          phone: formData.phone,
        });
      }
      handleCloseDialog();
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An unexpected error occurred.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClick = (memberId: number) => {
    setMemberToDelete(memberId);
    setDeleteConfirmOpen(true);
  };

  const handleConfirmDelete = () => {
    if (memberToDelete !== null) {
      deleteMember(memberToDelete);
    }
    setDeleteConfirmOpen(false);
    setMemberToDelete(null); 
  };

  const handleCancelDelete = () => {
    setDeleteConfirmOpen(false);
    setMemberToDelete(null);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleOpenHistoryDialog = (member: typeof members[0]) => {
    setSelectedMember(member);
    setHistoryDialogOpen(true);
  };

  const handleCloseHistoryDialog = () => {
    setHistoryDialogOpen(false);
    setSelectedMember(null);
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Members</Typography>
        <Button 
          variant="contained" 
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Add Member
        </Button>
      </Box>

      <Paper sx={{ p: 2, mb: 3 }}>
        <TextField
          fullWidth
          placeholder="Search members by name, email, or phone..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Paper>

      <Paper sx={{ width: '100%', mb: 2 }}>
        <TableContainer>
          <Table sx={{ minWidth: 650 }} aria-label="members table">
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Phone</TableCell>
                <TableCell>Visits</TableCell>
                <TableCell>Total Spent</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredMembers
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((member) => (
                  <TableRow key={member.id}>
                    <TableCell component="th" scope="row">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                          {(member.name && member.name.length > 0) ? member.name.charAt(0) : '?'}
                        </Avatar>
                        {member.name}
                      </Box>
                    </TableCell>
                    <TableCell>{member.email}</TableCell>
                    <TableCell>{member.phone}</TableCell>
                    <TableCell>{member.visits}</TableCell>
                    <TableCell>${Number(member.totalSpent).toFixed(2)}</TableCell>
                    <TableCell align="right">
                      <IconButton 
                        size="small" 
                        onClick={() => handleOpenHistoryDialog(member)}
                        title="View History"
                      >
                        <HistoryIcon />
                      </IconButton>
                      <IconButton 
                        size="small" 
                        onClick={() => handleOpenDialog(true, member)}
                        title="Edit Member"
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton 
                        size="small" 
                        color="error"
                        onClick={() => handleDeleteClick(member.id)}
                        title="Delete Member"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              {filteredMembers.length === 0 && (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <Typography variant="body1" sx={{ py: 2 }}>
                      No members found
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={filteredMembers.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>{editMode ? 'Edit Member' : 'Add New Member'}</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            {error && (
              <Typography color="error" textAlign="center">
                {error}
              </Typography>
            )}
            <TextField
              autoFocus
              label="Full Name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              fullWidth
              required
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PersonIcon />
                  </InputAdornment>
                ),
              }}
            />
            <TextField
              label="Email Address"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              fullWidth
              required
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <EmailIcon />
                  </InputAdornment>
                ),
              }}
            />
            <TextField
              label="Phone Number"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              fullWidth
              required
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PhoneIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained"
            disabled={!formData.name || !formData.email || !formData.phone || loading}
          >
            {loading ? (editMode ? 'Updating...' : 'Adding...') : (editMode ? 'Update' : 'Add')}
          </Button>
        </DialogActions>
      </Dialog>

      <ConfirmDialog
        open={deleteConfirmOpen}
        title="Confirm Delete"
        message="Are you sure you want to delete this member? This action cannot be undone."
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        confirmText="Delete"
        confirmButtonColor="error"
      />

      <Dialog open={historyDialogOpen} onClose={handleCloseHistoryDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          Member History
          {selectedMember && (
            <Typography variant="subtitle1" color="text.secondary">
              {selectedMember.name}
            </Typography>
          )}
        </DialogTitle>
        <DialogContent dividers>
          {selectedMember && (
            <Box>
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Member Details
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                  <Box sx={{ flexBasis: { xs: '100%', sm: '30%' } }}>
                    <Typography variant="body2" color="text.secondary">
                      Email
                    </Typography>
                    <Typography variant="body1">
                      {selectedMember.email}
                    </Typography>
                  </Box>
                  <Box sx={{ flexBasis: { xs: '100%', sm: '30%' } }}>
                    <Typography variant="body2" color="text.secondary">
                      Phone
                    </Typography>
                    <Typography variant="body1">
                      {selectedMember.phone}
                    </Typography>
                  </Box>
                  <Box sx={{ flexBasis: { xs: '100%', sm: '30%' } }}>
                    <Typography variant="body2" color="text.secondary">
                      Total Visits
                    </Typography>
                    <Typography variant="body1">
                      {selectedMember.visits}
                    </Typography>
                  </Box>
                </Box>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Typography variant="h6" gutterBottom>
                Purchase History
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                Total Spent: ${Number(selectedMember.totalSpent).toFixed(2)}
              </Typography>

              <Paper variant="outlined" sx={{ p: 2, bgcolor: 'background.default' }}>
                <Typography variant="body2" color="text.secondary" align="center">
                  Transaction history will be displayed here when connected to a backend.
                </Typography>
              </Paper>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseHistoryDialog}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
