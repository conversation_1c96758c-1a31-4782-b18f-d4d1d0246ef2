<!DOCTYPE html>
<html>
<head>
    <title>权限编辑</title>
    {% include 'system/common/header.html' %}
</head>
<body>
<form class="layui-form" action="">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item layui-hide">
                    <label class="layui-form-label">编号</label>
                    <div class="layui-input-block">
                        <input type="text" value="{{ power.id }}" name="powerId" lay-verify="required"
                               autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">父级</label>
                    <div class="layui-input-block">
                        <ul id="selectParent" name="parentId" class="dtree" data-id="-1"></ul>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">名称</label>
                    <div class="layui-input-block">
                        <input type="text" value="{{ power.name }}" name="powerName" lay-verify="required"
                               autocomplete="off" placeholder="权限名称" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item" id="powerCodeItem">
                    <label class="layui-form-label">标识</label>
                    <div class="layui-input-block">
                        <input type="text" id="powerCode" value="{{ power.code }}" name="powerCode"
                               autocomplete="off" placeholder="权限标识" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-block">
                        <input type="radio" lay-filter="powerType" {% if power.type == "0" %} checked{% endif %}
                               name="powerType" value="0" title="目录">
                        <input type="radio" lay-filter="powerType"  {% if power.type == "1" %} checked{% endif %}
                               name="powerType" value="1" title="菜单">
                        <input type="radio" lay-filter="powerType"  {% if power.type == "2" %} checked{% endif %}
                               name="powerType" value="2" title="按钮">
                    </div>
                </div>
                <div class="layui-form-item" id="powerUrlItem">
                    <label class="layui-form-label">路径</label>
                    <div class="layui-input-block">
                        <input type="text" id="powerUrl" value="{{ power.url }}" name="powerUrl"
                               autocomplete="off" placeholder="菜单路径" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item" id="openTypeItem">
                    <label class="layui-form-label">打开</label>
                    <div class="layui-input-block">
                        <select name="openType" id="openType">
                            <option value=""></option>
                            <option value="_iframe"
                                    {% if power.open_type == "_iframe" %} selected{% endif %}>框架
                            </option>
                            <option value="_component"
                                    {% if power.open_type == "_component" %} selected{% endif %}>内置
                            </option>
                            <option value="_blank"
                                    {% if power.open_type == "_blank" %} selected{% endif %}>签页
                            </option>

                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">图标</label>
                    <div class="layui-input-block">
                        <input type="text" id="icon" value="{{ power.icon }}" name="icon" lay-filter="icon"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">排序</label>
                    <div class="layui-input-inline" style="width: 100px !important;">
                        <input type="text" name="sort" value="{{ power.sort }}" lay-verify="required" autocomplete="off"
                               placeholder="排序" min="0" step="1" lay-affix="number" class="layui-input">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button type="submit" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="power-save">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
</form>
{% include 'system/common/footer.html' %}
<script>
    layui.use(['form', 'jquery', 'iconPicker', 'dtree'], function () {
        let form = layui.form
        let $ = layui.jquery
        let icon = layui.iconPicker
        let dtree = layui.dtree

        dtree.renderSelect({
            elem: '#selectParent',
            url: '/system/power/selectParent',
            method: 'get',
            selectInputName: {nodeId: 'parentId', context: 'powerName'},
            skin: 'layui',
            dataFormat: 'list',
            response: {treeId: 'powerId', parentId: 'parentId', title: 'powerName'},
            selectInitVal: "{{ power.parent_id }}"
        })

        form.on('radio(powerType)', function (data) {
            if (this.value == '0') {
                $('#powerUrlItem').hide()
                $('#powerCodeItem').hide()
                $('#openTypeItem').hide()
                $('#powerUrl').val('')
                $('#powerCode').val('')
                $('#openType').val('')
            } else if (this.value == '1') {
                $('#powerUrlItem').show()
                $('#powerCodeItem').show()
                $('#openTypeItem').show()
            } else if (this.value == '2') {
                $('#powerUrlItem').hide()
                $('#openTypeItem').hide()
                $('#powerCodeItem').show()
                $('#powerUrl').val('')
                $('#openType').val('')
            }
        })

        form.on('submit(power-save)', function (data) {
            data.field.icon = 'layui-icon ' + data.field.icon
            $.ajax({
                url: '/system/power/update',
                data: JSON.stringify(data.field),
                dataType: 'json',
                contentType: 'application/json',
                type: 'put',
                success: function (result) {
                    if (result.success) {
                        layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                            parent.layer.close(parent.layer.getFrameIndex(window.name))//关闭当前页
                            parent.render()
                        })
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1000})
                    }
                }
            })
            return false
        })

        icon.render({
            elem: '#icon',
            type: 'fontClass',
            search: true,
            click: function (data) {
                console.log(data);
            }
        });


        window.init = function (type) {
            if (type == '0') {
                $('#powerUrlItem').hide()
                $('#powerCodeItem').hide()
                $('#openTypeItem').hide()
                $('#powerUrl').val('')
                $('#powerCode').val('')
                $('#openType').val('')
            } else if (type == '1') {
                $('#powerUrlItem').show()
                $('#powerCodeItem').show()
                $('#openTypeItem').show()
            } else if (type == '2') {
                $('#powerUrlItem').hide()
                $('#openTypeItem').hide()
                $('#powerCodeItem').show()
                $('#powerUrl').val('')
                $('#openType').val('')
            }
        }

        window.init("{{ power.type}}")
    })
</script>
<script>
</script>
</body>
</html>