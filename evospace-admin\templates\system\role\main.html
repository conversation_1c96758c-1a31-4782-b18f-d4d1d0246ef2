<!DOCTYPE html>
<html>
<head>
    <title>用户管理</title>
    {% include 'system/common/header.html' %}
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form" action="">
            <div class="layui-form-item" style="margin-bottom: unset;">
                <label class="layui-form-label">角色名</label>
                <div class="layui-input-inline">
                    <input type="text" name="roleName" placeholder="" class="layui-input">
                </div>
                <label class="layui-form-label">角色标识</label>
                <div class="layui-input-inline">
                    <input type="text" name="roleCode" placeholder="" class="layui-input">
                </div>
                <button class="layui-btn layui-btn-md" lay-submit lay-filter="role-query">
                    <i class="layui-icon layui-icon-search"></i>
                    查询
                </button>
                <button type="reset" class="layui-btn layui-btn-primary layui-btn-md">
                    <i class="layui-icon layui-icon-refresh"></i>
                    重置
                </button>
            </div>
        </form>
    </div>
</div>
<div class="layui-card">
    <div class="layui-card-body">
        <table id="role-table" lay-filter="role-table"></table>
    </div>
</div>
</body>
{% include 'system/common/footer.html' %}
<script type="text/html" id="role-toolbar">
    {% if authorize("system:role:add") %}
        <button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="add">
            <i class="pear-icon pear-icon-add"></i>
            新增
        </button>
    {% endif %}
</script>

<script type="text/html" id="role-bar">
    {% if authorize("system:role:edit") %}
        <button class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit"><i
                class="layui-icon layui-icon-edit"> 编辑</i>
        </button>
    {% endif %}
    {% if authorize("system:role:power") %}
        <button class="layui-btn layui-btn-warming layui-btn-xs" lay-event="power"><i
                class="layui-icon layui-icon-vercode"> 授权</i>
        </button>
    {% endif %}
    {% if authorize("system:role:remove") %}
        <button class="layui-btn layui-btn-danger layui-btn-xs" lay-event="remove"><i
                class="layui-icon layui-icon-delete"> 删除</i>
        </button>
    {% endif %}
</script>

<script type="text/html" id="role-enable">
    <input type="checkbox" name="enable" value="{{ "{{d.id}}" }}" lay-skin="switch" lay-text="启用|禁用"
           lay-filter="role-enable" {{ "{{# if(d.enable==1){ }} checked {{# } }}" }}>
</script>

<script>
    layui.use(['table', 'form', 'jquery', 'popup', 'common'], function () {
        let table = layui.table
        let form = layui.form
        let $ = layui.jquery
        let popup = layui.popup

        let MODULE_PATH = '/system/role/'

        let cols = [
            [
                {title: '编号', field: 'id', align: 'center'},
                {title: '角色名', field: 'name', align: 'center', width: 100},
                {title: '标识', field: 'code', align: 'center'},
                {title: '描述', field: 'details', align: 'center'},
                {title: '是否可用', field: 'enable', align: 'center', templet: '#role-enable'},
                {title: '排序', field: 'sort', align: 'center'},
                {title: '操作', toolbar: '#role-bar', align: 'center', width: 240}
            ]
        ]

        table.render({
            elem: '#role-table',
            url: MODULE_PATH + 'data',
            page: true,
            cols: cols,
            skin: 'line',
            toolbar: '#role-toolbar',
            defaultToolbar: [{
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print', 'exports']
        })

        table.on('tool(role-table)', function (obj) {
            if (obj.event === 'remove') {
                window.remove(obj)
            } else if (obj.event === 'edit') {
                window.edit(obj)
            } else if (obj.event === 'power') {
                window.power(obj)
            }
        })

        table.on('toolbar(role-table)', function (obj) {
            if (obj.event === 'add') {
                window.add()
            } else if (obj.event === 'refresh') {
                window.refresh()
            } else if (obj.event === 'batchRemove') {
                window.batchRemove(obj)
            }
        })

        form.on('submit(role-query)', function (data) {
            table.reload('role-table', {where: data.field})
            return false
        })

        form.on('switch(role-enable)', function (obj) {
            let operate
            if (obj.elem.checked) {
                operate = 'enable'
            } else {
                operate = 'disable'
            }
            let loading = layer.load()
            $.ajax({
                url: '/system/role/' + operate,
                data: JSON.stringify({roleId: this.value}),
                dataType: 'json',
                contentType: 'application/json',
                type: 'put',
                success: function (result) {
                    layer.close(loading)
                    if (result.success) {
                        layer.msg(result.msg, {icon: 1, time: 1000})
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1000})
                    }
                }
            })
        })

        window.add = function () {
            layer.open({
                type: 2,
                title: '新增',
                shade: 0.1,
                area: ['500px', '500px'],
                content: MODULE_PATH + 'add'
            })
        }

        window.power = function (obj) {
            layer.open({
                type: 2,
                title: '授权',
                shade: 0.1,
                area: ['320px', '400px'],
                content: MODULE_PATH + 'power/' + obj.data['id']
            })
        }

        window.edit = function (obj) {
            layer.open({
                type: 2,
                title: '修改',
                shade: 0.1,
                area: ['500px', '500px'],
                content: MODULE_PATH + 'edit/' + obj.data['id']
            })
        }

        window.remove = function (obj) {
            layer.confirm('确定要删除该角色', {icon: 3, title: '提示'}, function (index) {
                layer.close(index)
                let loading = layer.load()
                $.ajax({
                    url: MODULE_PATH + 'remove/' + obj.data['id'],
                    dataType: 'json',
                    type: 'delete',
                    success: function (result) {
                        layer.close(loading)
                        if (result.success) {
                            layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                                obj.del()
                            })
                        } else {
                            layer.msg(result.msg, {icon: 2, time: 1000})
                        }
                    }
                })
            })
        }

        window.batchRemove = function (obj) {
            let data = table.checkStatus(obj.config.id).data
            if (data.length === 0) {
                layer.msg('未选中数据', {
                    icon: 3,
                    time: 1000
                })
                return false
            }
            var ids = []
            var hasCheck = table.checkStatus('role-table')
            var hasCheckData = hasCheck.data
            if (hasCheckData.length > 0) {
                $.each(hasCheckData, function (index, element) {
                    ids.push(element.id)
                })
            }
            console.log(ids)
            layer.confirm('确定要删除选中角色', {
                icon: 3,
                title: '提示'
            }, function (index) {
                layer.close(index)
                let loading = layer.load()
                $.ajax({

                    url: MODULE_PATH + 'batchRemove',
                    data: {ids: ids},
                    dataType: 'json',
                    type: 'delete',
                    success: function (result) {
                        layer.close(loading)
                        if (result.success) {
                            popup.success(result.msg, function () {
                                table.reload('role-table')
                            })
                        } else {
                            popup.failure(result.msg)
                        }
                    }
                })
            })
        }

        window.refresh = function () {
            table.reload('role-table')
        }
    })
</script>
</html>