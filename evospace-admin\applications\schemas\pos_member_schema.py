from applications.extensions import ma
from applications.models.pos_member import PosMember

class PosMemberSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = PosMember
        load_instance = True
        # fields = ('id', 'name', 'email', 'phone', 'join_date', 'total_spent', 'visits', 'created_at', 'updated_at')
        # dump_only = ('id', 'join_date', 'total_spent', 'visits', 'created_at', 'updated_at')

    id = ma.auto_field(dump_only=True)
    name = ma.auto_field(required=True)
    email = ma.auto_field(required=True)
    phone = ma.auto_field(required=True)
    join_date = ma.auto_field(dump_only=True) # Set by default in model
    total_spent = ma.auto_field(dump_only=True) # Calculated field, not for direct input on creation
    visits = ma.auto_field(dump_only=True)      # Calculated field
    created_at = ma.auto_field(dump_only=True)
    updated_at = ma.auto_field(dump_only=True)
