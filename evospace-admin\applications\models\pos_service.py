from applications.extensions import db
from sqlalchemy.dialects.mysql import INTEGER
import datetime

class PosService(db.Model):
    __tablename__ = 'pos_service'

    id = db.Column(INTEGER(unsigned=True), primary_key=True, autoincrement=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text, nullable=False)
    
    # Store isolation - Multi-store support
    store_id = db.Column(db.Integer, db.ForeignKey('admin_dept.id'), nullable=False)
    store = db.relationship('Dept', backref='pos_services')
    
    # Categorization
    category = db.Column(db.String(100), nullable=True)
    subcategory = db.Column(db.String(100), nullable=True)
    
    # Pricing
    price = db.Column(db.Numeric(10, 2), nullable=False)
    cost_price = db.Column(db.Numeric(10, 2), nullable=True)  # Cost to provide the service
    discount_price = db.Column(db.Numeric(10, 2), nullable=True)  # Promotional price
    tax_rate = db.Column(db.Numeric(5, 2), nullable=True)  # Percentage
    
    # Time management
    duration = db.Column(db.Integer, nullable=True)  # Duration in minutes
    prep_time = db.Column(db.Integer, nullable=True)  # Preparation time in minutes
    cleanup_time = db.Column(db.Integer, nullable=True)  # Cleanup time in minutes
    unit = db.Column(db.String(50), nullable=True)  # e.g., "minute", "hour", "session"
    
    # Resource requirements
    staff_required = db.Column(db.Integer, nullable=True, default=1)  # Number of staff needed
    resources_required = db.Column(db.Text, nullable=True)  # JSON array of required resources
    equipment_required = db.Column(db.Text, nullable=True)  # JSON array of required equipment
    
    # Scheduling constraints
    min_booking_notice = db.Column(db.Integer, nullable=True)  # Minimum notice in hours
    max_booking_advance = db.Column(db.Integer, nullable=True)  # Maximum advance booking in days
    availability_days = db.Column(db.String(100), nullable=True)  # e.g., "1,2,3,4,5" for weekdays
    
    # Media
    image_url = db.Column(db.String(512), nullable=True)
    
    # Additional information
    is_featured = db.Column(db.Boolean, default=False)  # Featured on homepage/promotions
    popularity_score = db.Column(db.Integer, default=0)  # For sorting/recommendations
    notes = db.Column(db.Text, nullable=True)  # Internal notes
    
    # System fields
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)
    status = db.Column(db.String(50), nullable=False, default='active')  # e.g., 'active', 'inactive', 'discontinued'
