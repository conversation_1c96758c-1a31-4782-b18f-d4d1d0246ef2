"use client";

import React, { useState, useCallback, useEffect } from 'react';
import {
  Search as SearchIcon,
  ShoppingCart as ShoppingCartIcon,
  Payment as PaymentIcon,
  Person as PersonIcon,
  Close as CloseIcon,
  Delete as DeleteIcon,
  PlayArrow
} from '@mui/icons-material';
import { 
  Box, 
  Button, 
  Card, 
  CardContent, 
  IconButton, 
  Paper, 
  Tab, 
  Tabs, 
  TextField, 
  Typography,
  InputAdornment,
  Stack,
  MenuItem,
  FormControl,
  InputLabel,
  Select
} from '@mui/material';
import usePosStore from '@/lib/store';
import { Service, Session, Member, Product, ResourceStatus, Resource, CartItem, SessionItem } from '@/lib/types';
import CartTotal from '@/components/pos/CartTotal';
import ResourceFloorPlanView from '@/components/pos/ResourceFloorPlanView';
import ConfirmDialog from '@/components/ConfirmDialog';
import PaymentDialog from '@/components/pos/PaymentDialog';
import MemberSelectionDialog from '@/components/pos/MemberSelectionDialog';
import ServiceSelectionDialog from '@/components/pos/ServiceSelectionDialog';
import ProductSelectionDialog from '@/components/pos/ProductSelectionDialog';

interface ResourceCartItemProps {
  item: CartItem;
  resourceDetails: Resource | undefined;
  session: Session | null;
  formatCurrency: (value: number) => string;
  onRemoveItem: (itemId: number) => void;
  onAddServiceToSession: (resource_id: number) => void;
  onAddProductToSession: (resource_id: number) => void;
}

const ResourceCartItem = React.memo<ResourceCartItemProps>(({ 
  item, 
  resourceDetails, 
  session, 
  formatCurrency, 
  onRemoveItem, 
  onAddServiceToSession, 
  onAddProductToSession 
}) => {
  return (
    <Card sx={{ mb: 2, bgcolor: 'primary.light', color: 'primary.contrastText' }}>
      <CardContent sx={{ px: 1, pt: 1, pb: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box sx={{ flexGrow: 1, mr: 1 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
              {item.name}
            </Typography>
            <Typography variant="body2" sx={{ color: 'primary.contrastText' }}>
              ${Number(item.price).toFixed(2)}/hr • {resourceDetails?.floor} • {resourceDetails?.zone}
            </Typography>
          </Box>
          <IconButton
            edge="end"
            aria-label="delete"
            onClick={() => onRemoveItem(item.id)}
            sx={{ color: 'primary.contrastText', mr: 0.1 }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        {session && (
          <>
            <Typography variant="body2" component="span" sx={{ display: 'block', color: 'primary.contrastText', opacity: 0.7 }}>
              Duration: {Math.round((Date.now() - new Date(session.start_time).getTime()) / (1000 * 60))} min
            </Typography>
            {(() => {
              const durationInMinutes = Math.round((Date.now() - new Date(session.start_time).getTime()) / (1000 * 60));
              const roundedMinutes = Math.ceil(durationInMinutes / 10) * 10;
              const billableHours = roundedMinutes / 60;
              const total = billableHours * item.price;
              return (
                <>
                  <Typography variant="body2" component="span" sx={{ display: 'block', color: 'primary.contrastText', opacity: 0.7 }}>
                    Billed: {roundedMinutes} min ({billableHours.toFixed(2)} hrs)
                  </Typography>
                  <Typography variant="body2" component="span" sx={{ display: 'block', fontWeight: 'bold', mt: 0.5 }}>
                    Resource Total: ${total.toFixed(2)}
                  </Typography>
                </>
              );
            })()}
          </>
        )}
        {/* Services Section */}
          <Box sx={{ mt: 1, pt: session ? 1 : 0, px: 1, borderTop: session ? '1px solid rgba(255,255,255,0.2)' : 'none' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: session && session.services.length > 0 ? 0.5 : 0 }}>
              <Typography variant="subtitle2" sx={{ color: 'rgba(255,255,255,0.8)' }}>Services:</Typography>
              <Button 
                size="small"
                variant="text"
                onClick={() => onAddServiceToSession(item.id)}
                sx={{ 
                  color: 'primary.contrastText', 
                  textTransform: 'none', 
                  p:0, 
                  minWidth: 'auto', 
                  '&:hover': { backgroundColor: 'transparent', textDecoration: 'underline', color: 'secondary.light' } 
                }}
              >
                Add
              </Button>
            </Box>
            {session && session.services.length > 0 ? (
              session.services.map((serviceItem, serviceIndex) => (
                <Box key={`session-${session.id}-service-${serviceItem.id}-${serviceIndex}`} sx={{ py: 0.5, px: 0, display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" sx={{ color: 'primary.contrastText' }}>{serviceItem.name} (x{serviceItem.quantity})</Typography>
                  <Typography variant="body2" sx={{ color: 'primary.contrastText' }}>{formatCurrency(serviceItem.price * serviceItem.quantity)}</Typography>
                </Box>
              ))
            ) : (
              <Typography variant="caption" sx={{ fontStyle: 'italic', color: 'rgba(255,255,255,0.6)', display: 'block', pl:0 }}>
                No services added to this session.
              </Typography>
            )}
          </Box>

          {/* Products Section */}
          <Box sx={{ mt: 2, pt: 1, px: 1, borderTop: '1px solid rgba(255,255,255,0.2)' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: session && session.products.length > 0 ? 0.5 : 0 }}>
              <Typography variant="subtitle2" sx={{ color: 'rgba(255,255,255,0.8)' }}>Products:</Typography>
              <Button 
                size="small"
                variant="text"
                onClick={() => onAddProductToSession(item.id)}
                sx={{ 
                  color: 'primary.contrastText', 
                  textTransform: 'none', 
                  p:0, 
                  minWidth: 'auto', 
                  '&:hover': { backgroundColor: 'transparent', textDecoration: 'underline', color: 'secondary.light' } 
                }}
              >
                Add
              </Button>
            </Box>
            {session && session.products.length > 0 ? (
              session.products.map((productItem, productIndex) => (
                <Box key={`session-${session.id}-product-${productItem.id}-${productIndex}`} sx={{ py: 0.5, px: 0, display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" sx={{ color: 'primary.contrastText' }}>{productItem.name} (x{productItem.quantity})</Typography>
                  <Typography variant="body2" sx={{ color: 'primary.contrastText' }}>{formatCurrency(productItem.price * productItem.quantity)}</Typography>
                </Box>
              ))
            ) : (
              <Typography variant="caption" sx={{ fontStyle: 'italic', color: 'rgba(255,255,255,0.6)', display: 'block', pl:0 }}>
                No products added to this session.
              </Typography>
            )}
          </Box>
      </CardContent>
    </Card>
  );
});
ResourceCartItem.displayName = 'ResourceCartItem';

export default function PosPage() {
  const [confirmEndSession, setConfirmEndSession] = useState(false);
  const [sessionToManage, setSessionToManage] = useState<Session | null>(null);
  const [resourceStatusFilter, setResourceStatusFilter] = useState<ResourceStatus | 'all'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [memberDialogOpen, setMemberDialogOpen] = useState(false);
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [serviceDialogOpen, setServiceDialogOpen] = useState(false);
  const [productDialogOpen, setProductDialogOpen] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card' | 'mobile'>('cash');
  const [paymentAmount, setPaymentAmount] = useState('');
  const [selectedFloor, setSelectedFloor] = useState<number>(1);
  const [selectedZone, setSelectedZone] = useState<string | null>(null);
  const [selectedResource, setSelectedResource] = useState<number | null>(null);
  const [targetResourceForDialogId, setTargetResourceForDialogId] = useState<number | null>(null);

  // State for informational dialog
  const [infoDialogOpen, setInfoDialogOpen] = useState(false);
  const [infoDialogTitle, setInfoDialogTitle] = useState('');
  const [infoDialogMessage, setInfoDialogMessage] = useState('');

  const {
    products, 
    services, 
    resources,
    members,
    cart, 
    addToCart, 
    removeFromCart, 
    clearCart, 
    cartTotal,
    selectedMember,
    setSelectedMember,
    updateResourceStatus,
    startSession,
    endSession,
    getActiveSession,
    addServiceToActiveSession,
    addProductToActiveSession,
    fetchActiveSessions,
    fetchResources,
    fetchProducts,
    fetchServices,
    authUser
  } = usePosStore();
  
  // Fetch resources and active sessions when component mounts or page becomes visible
  useEffect(() => {
    const loadInitialData = async () => {
      if (authUser.token) {
        console.log("[PosPage] Auth token found. Fetching initial data...");
        await fetchResources(); // Fetch resources first
        console.log("[PosPage] Resources fetched. Now fetching active sessions...");
        await fetchActiveSessions(); // Then fetch active sessions
        console.log("[PosPage] Active sessions fetched.");
      } else {
        console.log("[PosPage] No auth token. Skipping initial data fetch.");
      }
    };

    loadInitialData(); // Call on mount

    const handleVisibilityChange = async () => {
      if (document.visibilityState === 'visible' && authUser.token) {
        console.log("[PosPage] Page visible. Re-fetching active sessions...");
        await fetchActiveSessions();
        console.log("[PosPage] Active sessions re-fetched on visibility change.");
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [authUser.token, fetchResources, fetchActiveSessions]);

  // Fetch products when product dialog opens
  useEffect(() => {
    if (productDialogOpen && authUser.token) {
      console.log("[PosPage] Product dialog opened. Fetching products...");
      fetchProducts();
    }
  }, [productDialogOpen, fetchProducts, authUser.token]);

  // Fetch services when service dialog opens
  useEffect(() => {
    if (serviceDialogOpen && authUser.token) {
      console.log("[PosPage] Service dialog opened. Fetching services...");
      fetchServices();
    }
  }, [serviceDialogOpen, fetchServices, authUser.token]);
  

  const handleStatusFilterChange = (event: React.SyntheticEvent, newStatus: ResourceStatus | 'all') => {
    if (newStatus) {
      setResourceStatusFilter(newStatus);
      setSearchQuery('');
    }
  };

  const handleAddToCart = (item: typeof resources[0]) => {
    addToCart({
      id: item.id,
      name: item.name,
      price: item.hourly_rate
    });
  };

  const handleOpenMemberDialog = () => {
    setMemberDialogOpen(true);
  };

  const handleCloseMemberDialog = () => {
    setMemberDialogOpen(false);
  };

  const handleSelectMember = (member: Member) => {
    setSelectedMember(member);
    handleCloseMemberDialog();
  };

  const handleOpenPaymentDialog = () => {
    setPaymentAmount(cartTotal().toFixed(2));
    setPaymentDialogOpen(true);
  };

  const handleClosePaymentDialog = () => {
    setPaymentDialogOpen(false);
  };

  const handleCompleteTransaction = () => {
    const {
      cart,
      activeSessions,
      addTransaction,
      clearCart,
      selectedMember,
    } = usePosStore.getState();

    const now = new Date().toISOString();
    const finalizedSessionsForTransaction: Session[] = [];
    let newTransactionTotalAmount = 0;

    cart.forEach(cartResourceItem => {
      // Find the active session directly from the activeSessions array
      const liveSessionFromStore = activeSessions.find(s => s.resource_id === cartResourceItem.id);

      if (liveSessionFromStore) {
        const startTimeMs = new Date(liveSessionFromStore.start_time).getTime();
        const endTimeMs = new Date(now).getTime();
        const durationMs = endTimeMs - startTimeMs;
        
        const durationInMinutes = Math.max(0, durationMs / (1000 * 60)); 
        const roundedBilledMinutes = durationInMinutes > 0 ? Math.ceil(durationInMinutes / 10) * 10 : 0;
        const billableHours = roundedBilledMinutes / 60;
        
        const resourceCost = billableHours * cartResourceItem.price;
        newTransactionTotalAmount += resourceCost;

        let itemsInSessionCost = 0;
        // Use proper type annotations to avoid 'any' type errors
        liveSessionFromStore.products.forEach((product: SessionItem) => {
          itemsInSessionCost += product.price * product.quantity;
        });
        liveSessionFromStore.services.forEach((service: SessionItem) => {
          itemsInSessionCost += service.price * service.quantity;
        });
        newTransactionTotalAmount += itemsInSessionCost;

        const finalizedSession: Session = {
          ...liveSessionFromStore, 
          end_time: now,
          status: 'completed',
          // Add proper type annotations to avoid 'any' type errors
          products: liveSessionFromStore.products.map((p: SessionItem) => ({ ...p })),
          services: liveSessionFromStore.services.map((s: SessionItem) => ({ ...s })),
        };
        finalizedSessionsForTransaction.push(finalizedSession);
      }
    });

    if (finalizedSessionsForTransaction.length > 0) {
      addTransaction({
        memberId: selectedMember ? selectedMember.id : null,
        memberName: selectedMember ? selectedMember.name : 'Walk-in', 
        sessions: finalizedSessionsForTransaction, 
        totalAmount: newTransactionTotalAmount,
        paymentMethod: paymentMethod, 
        status: 'completed',
        createdAt: now,
      });
    } else {
      console.log('[Transaction] No active resource sessions found in cart to complete a resource-based transaction.');
    }

    clearCart();
    setPaymentDialogOpen(false); 
  };

  const handleOpenServiceDialog = useCallback(() => setServiceDialogOpen(true), []);

  const handleCloseServiceDialog = useCallback(() => {
    setServiceDialogOpen(false);
    setTargetResourceForDialogId(null); 
  }, []);

  const handleSelectService = async (service: Service) => {
    if (targetResourceForDialogId !== null) {
      // Check if there's an active session for this resource
      const currentSession = getActiveSession(targetResourceForDialogId);
      
      // If no active session exists, create one first
      if (!currentSession) {
        try {
          await startSession(targetResourceForDialogId);
        } catch (error) {
          console.error(`[Session] Failed to create session for resource ${targetResourceForDialogId}:`, error);
          openInfoDialog('Session Error', 'Failed to create a new session. Please try again.');
          handleCloseServiceDialog();
          return;
        }
      }
      
      // Now add the service to the session
      await addServiceToActiveSession(targetResourceForDialogId, { id: service.id, name: service.name, price: service.price });
    } else {
      // No resource selected, add to general cart
      console.log('[Cart] Added service to general cart:', service.name);
    }
    handleCloseServiceDialog(); 
  };

  const handleOpenProductDialog = useCallback(() => setProductDialogOpen(true), []);

  const handleCloseProductDialog = useCallback(() => {
    setProductDialogOpen(false);
    setTargetResourceForDialogId(null); 
  }, []);

  const handleSelectProduct = async (product: Product) => {
    if (targetResourceForDialogId !== null) {
      // Check if there's an active session for this resource
      const currentSession = getActiveSession(targetResourceForDialogId);
      
      // If no active session exists, create one first
      if (!currentSession) {
        try {
          await startSession(targetResourceForDialogId);
        } catch (error) {
          console.error(`[Session] Failed to create session for resource ${targetResourceForDialogId}:`, error);
          openInfoDialog('Session Error', 'Failed to create a new session. Please try again.');
          handleCloseProductDialog();
          return;
        }
      }
      
      // Now add the product to the session
      await addProductToActiveSession(targetResourceForDialogId, { id: product.id, name: product.name, price: product.price });
    } else {
      // No resource selected, add to general cart
      console.log('[Cart] Added product to general cart:', product.name);
    }
    handleCloseProductDialog(); 
  };

  const formatCurrency = (value: number) => {
    return `$${value.toFixed(2)}`;
  };

  const openInfoDialog = useCallback((title: string, message: string) => {
    setInfoDialogTitle(title);
    setInfoDialogMessage(message);
    setInfoDialogOpen(true);
  }, []);

  const closeInfoDialog = useCallback(() => {
    setInfoDialogOpen(false);
    setInfoDialogTitle('');
    setInfoDialogMessage('');
  }, []);

  const handleRemoveResourceFromCartCb = useCallback((itemId: number) => {
    removeFromCart(itemId);
    updateResourceStatus(itemId, 'available'); 
  }, [removeFromCart, updateResourceStatus]);

  const handleAddServiceToSessionCb = useCallback((resource_id: number) => {
    // Just store the resource ID and open the dialog
    // We'll create the session only when a service is actually selected
    setTargetResourceForDialogId(resource_id);
    handleOpenServiceDialog();
  }, [setTargetResourceForDialogId, handleOpenServiceDialog]); 

  const handleAddProductToSessionCb = useCallback((resource_id: number) => {
    // Just store the resource ID and open the dialog
    // We'll create the session only when a product is actually selected
    setTargetResourceForDialogId(resource_id);
    handleOpenProductDialog();
  }, [setTargetResourceForDialogId, handleOpenProductDialog]); 

  return (
    <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', height: 'calc(100vh - 64px)' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={resourceStatusFilter} onChange={handleStatusFilterChange} aria-label="Resource Status Filter Tabs" sx={{ bgcolor: 'background.paper', color: 'text.primary' }}>
          <Tab label="All Resources" value="all" />
          <Tab label="Available" value="available" />
          <Tab label="In Use" value="in-use" />
          <Tab label="Booked" value="booked" />
          <Tab label="Maintenance" value="maintenance" />
        </Tabs>
      </Box>

      <Box sx={{ flexGrow: 1, display: 'flex', overflow: 'hidden' }}>
        <Box sx={{ width: '70%', height: '100%', overflow: 'hidden', p: 2, display: 'flex', flexDirection: 'column' }}>
          <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
              <TextField
                placeholder="Search Resources..."
                variant="outlined"
                size="small"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                sx={{ minWidth: '200px', flexGrow: 1, maxWidth: '300px' }}
              />
              <FormControl sx={{ minWidth: 120 }} size="small">
                <InputLabel>Floor</InputLabel>
                <Select
                  value={selectedFloor}
                  label="Floor"
                  onChange={(e) => setSelectedFloor(Number(e.target.value))}
                >
                  {Array.from(new Set(resources.map(r => r.floor))).sort((a, b) => a - b).map(floor => (
                    <MenuItem key={floor} value={floor}>{`Floor ${floor}`}</MenuItem>
                  ))}
                </Select>
              </FormControl>
              <FormControl sx={{ minWidth: 120 }} size="small">
                <InputLabel>Zone</InputLabel>
                <Select
                  value={selectedZone || 'all'}
                  label="Zone"
                  onChange={(e) => setSelectedZone(e.target.value === 'all' ? null : e.target.value as string)}
                >
                  <MenuItem value="all">All Zones</MenuItem>
                  {Array.from(new Set(resources.map(r => r.zone).filter(Boolean))).sort().map(zone => (
                    <MenuItem key={zone} value={zone}>{zone}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>

            <Box 
              sx={{
                flexGrow: 1, 
                overflowY: 'auto', 
                p: 0, 
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 1,
                position: 'relative'
              }}
            >
              <ResourceFloorPlanView
                resources={resources}
                selectedResource={selectedResource}
                getActiveSession={getActiveSession}
                currentResourceStatusFilter={resourceStatusFilter}
                currentSearchQuery={searchQuery}
                currentSelectedFloor={selectedFloor}
                currentSelectedZone={selectedZone}
                onShowInfoAlert={openInfoDialog} 
                onResourceSelect={(resource_id) => {
                  const resource = resources.find(r => r.id === resource_id);
                  const existingSession = getActiveSession(resource_id);
                  const existingInCart = cart.some(item => item.id === resource_id);
                  if (resource) {
                    if (existingInCart) {
                      return;
                    }
                    if (existingSession) {
                      setSelectedResource(resource_id);
                      handleAddToCart(resource);
                    } else if (resource.status === 'available') {
                      setSelectedResource(resource_id);
                      handleAddToCart(resource);
                    } else {
                      console.log('[Resource] Resource not available:', { status: resource.status });
                    }
                  }
                }}
                onBackgroundClick={() => {
                  setSelectedResource(null); 
                }}
              />
            </Box>
          </Box>
        </Box>

        <Box sx={{ width: '30%', display: 'flex', flexDirection: 'column', borderLeft: 1, borderColor: 'divider' }}>
          <Paper sx={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column', bgcolor: 'background.paper' }}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6" gutterBottom>
                Current Order
              </Typography>

              <Button 
                variant="outlined" 
                startIcon={<PersonIcon />}
                onClick={handleOpenMemberDialog}
                fullWidth
                sx={{ mb: 1 }}
              >
                {selectedMember ? selectedMember.name : 'Select Member'}
              </Button>
            </Box>

            <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
              {cart.length === 0 ? (
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
                  <ShoppingCartIcon sx={{ fontSize: 60, color: 'text.disabled', mb: 2 }} />
                  <Typography variant="body1" color="text.secondary">
                    Cart is empty
                  </Typography>
                  <Typography variant="body2" color="text.disabled">
                    Add products or services to get started
                  </Typography>
                </Box>
              ) : (
                cart.map((item) => {
                  const resourceDetails = resources.find(r => r.id === item.id);
                  const session = getActiveSession(item.id);
                  
                  return (
                    <ResourceCartItem
                      key={`${item.id}`}
                      item={item}
                      resourceDetails={resourceDetails}
                      session={session || null}
                      formatCurrency={formatCurrency}
                      onRemoveItem={handleRemoveResourceFromCartCb}
                      onAddServiceToSession={handleAddServiceToSessionCb}
                      onAddProductToSession={handleAddProductToSessionCb}
                    />
                  );
                })
              )}
            </Box>

            <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="h6">Total:</Typography>
                <CartTotal />
              </Box>

              <Stack direction="row" spacing={1} sx={{ mb: 2 }}>
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<DeleteIcon />}
                  onClick={() => {
                    const activeSessionResources = cart.filter(item => getActiveSession(item.id));
                    
                    if (activeSessionResources.length > 0) {
                      clearCart();
                      activeSessionResources.forEach(item => {
                        addToCart({
                          id: item.id,
                          name: item.name,
                          price: item.price
                        });
                      });
                    } else {
                      cart.forEach(item => {
                        updateResourceStatus(item.id, 'available');
                      });
                      clearCart();
                    }
                  }}
                  disabled={cart.length === 0}
                  fullWidth
                >
                  Clear
                </Button>
                {cart.length > 0 ? (
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => {
                      const hasActiveSessions = cart.some(item => getActiveSession(item.id));

                      if (hasActiveSessions) {
                        cart.forEach(item => {
                          const session = getActiveSession(item.id);
                          if (session) {
                            endSession(session.id);
                          }
                        });
                        handleOpenPaymentDialog();
                      } else {
                        cart.forEach(item => {
                          startSession(item.id);
                          updateResourceStatus(item.id, 'in-use');
                        });
                        clearCart();
                      }
                    }}
                    startIcon={cart.some(item => getActiveSession(item.id)) ? <PaymentIcon /> : <PlayArrow />}
                    fullWidth
                  >
                    {cart.some(item => getActiveSession(item.id)) ? 'End Session & Pay' : 'Start Sessions'}
                  </Button>
                ) : (
                  <Button
                    variant="contained"
                    startIcon={<PaymentIcon />}
                    onClick={handleOpenPaymentDialog}
                    disabled={cart.length === 0}
                    fullWidth
                  >
                    Pay
                  </Button>
                )}
              </Stack>
            </Box>
          </Paper>

          <MemberSelectionDialog 
            open={memberDialogOpen}
            onClose={handleCloseMemberDialog}
            members={members} 
            onSelectMember={handleSelectMember}
            currentMemberId={selectedMember ? selectedMember.id : null}
            onSelectWalkIn={() => {
              setSelectedMember(null);
              handleCloseMemberDialog(); 
            }}
          />

          <ServiceSelectionDialog
            open={serviceDialogOpen}
            onClose={handleCloseServiceDialog}
            services={services} 
            onSelectService={handleSelectService}
          />

          <ProductSelectionDialog
            open={productDialogOpen}
            onClose={handleCloseProductDialog}
            products={products} 
            onSelectProduct={handleSelectProduct}
          />

          <PaymentDialog
            open={paymentDialogOpen}
            onClose={handleClosePaymentDialog}
            cart={cart}
            cartTotal={cartTotal}
            getActiveSession={getActiveSession}
            paymentMethod={paymentMethod}
            setPaymentMethod={setPaymentMethod}
            paymentAmount={paymentAmount}
            setPaymentAmount={setPaymentAmount}
            onCompleteTransaction={handleCompleteTransaction}
          />

          <ConfirmDialog
            open={confirmEndSession}
            title="End Session"
            message="Are you sure you want to end this session? This will finalize the resource usage and proceed to payment."
            onConfirm={() => {
              if (sessionToManage) {
                endSession(sessionToManage.id);
                setSessionToManage(null);
                handleOpenPaymentDialog();
              }
              setConfirmEndSession(false);
            }}
            onCancel={() => {
              setConfirmEndSession(false);
              setSessionToManage(null);
            }}
          />

          <ConfirmDialog
            open={infoDialogOpen}
            title={infoDialogTitle}
            message={infoDialogMessage}
            onConfirm={closeInfoDialog} 
            onCancel={closeInfoDialog}  
            infoMode={true}
            confirmText="OK"
          />
        </Box>
      </Box>
    </Box>
  );
}
