<!DOCTYPE html>
<html lang="en">
<head>
    <title>邮件管理</title>
    {% include 'system/common/header.html' %}
    <link rel="stylesheet" href="{{ url_for('static', filename='system/admin/css/other/user.css') }}"/>
</head>
<body class="pear-container">
{# 查询表单 #}
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form" action="" lay-filter="mail-query-form">
            <div class="layui-form-item" style="margin-bottom: unset;">
                <label class="layui-form-label">收件人邮箱</label>
                <div class="layui-input-inline">
                    <input type="text" name="receiver" placeholder="" class="layui-input">
                </div>
                <label class="layui-form-label">主题</label>
                <div class="layui-input-inline">
                    <input type="text" name="subject" placeholder="" class="layui-input">
                </div>
                <label class="layui-form-label">正文</label>
                <div class="layui-input-inline">
                    <input type="text" name="content" placeholder="" class="layui-input">
                </div>
                <button class="layui-btn layui-btn-md" lay-submit lay-filter="mail-query">
                    <i class="layui-icon layui-icon-search"></i>
                    查询
                </button>
                <button type="reset" class="layui-btn layui-btn-primary layui-btn-md">
                    <i class="layui-icon layui-icon-refresh"></i>
                    重置
                </button>
            </div>
        </form>
    </div>
</div>
{# 用户表格 #}
<div class="user-main user-collasped">
    <div class="layui-card">
        <div class="layui-card-body">
            <table id="mail-table" lay-filter="mail-table"></table>
        </div>
    </div>
</div>
</body>
{# 表格操作 #}
<script type="text/html" id="mail-toolbar">
    {% if authorize("system:mail:add") %}
        <button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="add">
            <i class="pear-icon pear-icon-add"></i>
            新增
        </button>
    {% endif %}
    {% if authorize("system:mail:remove") %}
        <button class="layui-btn layui-btn-sm" lay-event="batchRemove">
            <i class="pear-icon pear-icon-ashbin"></i>
            删除
        </button>
    {% endif %}
</script>

{# 用户修改操作 #}
<script type="text/html" id="mail-bar">
    {% if authorize("system:mail:remove") %}
        <button class="layui-btn layui-btn-danger layui-btn-xs" lay-event="remove"><i
                class="layui-icon layui-icon-delete"> 删除</i>
        </button>
    {% endif %}
</script>

{# 启动与禁用 #}
<script type="text/html" id="mail-enable">
    <input type="checkbox" name="enable" value="{{ "{{ d.id }}" }}" lay-skin="switch" lay-text="启用|禁用"
           lay-filter="mail-enable"
            {{ "{{# if(d.enable==1){ }} checked {{# } }}" }} />
</script>

{# 用户注册时间 #}
<script type="text/html" id="mail-createTime">
    {{ '  {{layui.util.toDateString(d.create_at,  "yyyy-MM-dd HH:mm:ss")}' |safe }}}
</script>

{% include 'system/common/footer.html' %}

<script>
  layui.use(['table', 'form', 'jquery', 'popup', 'common'], function () {
    let table = layui.table
    let form = layui.form
    let $ = layui.jquery
    let dtree = layui.dtree
    let popup = layui.popup
    let common = layui.common
    let MODULE_PATH = '/system/mail/'

    // 表格数据
    let cols = [
      [
        {% if authorize("system:mail:remove") %}
          { type: 'checkbox' },
        {% endif %}
        { title: '邮件编号', field: 'id', align: 'center', width: 110 },
        { title: '收件人邮箱', field: 'receiver', align: 'center' },
        { title: '主题', field: 'subject', align: 'center' },
        { title: '正文', field: 'content', align: 'center' },
        { title: '创建人', field: 'realname', align: 'center' },
        { title: '创建时间', field: 'create_at', templet: '#mail-createTime', align: 'center' },
        { title: '操作', toolbar: '#mail-bar', align: 'center', width: 130 }
      ]
    ]

    // 渲染表格数据
    table.render({
      elem: '#mail-table',
      url: MODULE_PATH + 'data',
      page: true,
      cols: cols,
      skin: 'line',
      height: 'full-148',
      toolbar: '#mail-toolbar', /*工具栏*/
      text: { none: '暂无邮件信息' },
      defaultToolbar: [{ layEvent: 'refresh', icon: 'layui-icon-refresh' }, 'filter', 'print', 'exports'] /*默认工具栏*/
    })



    table.on('tool(mail-table)', function (obj) {
      if (obj.event === 'remove') {
        window.remove(obj)
      }
    })

    table.on('toolbar(mail-table)', function (obj) {
      if (obj.event === 'add') {
        window.add()
      } else if (obj.event === 'refresh') {
        window.refresh()
      } else if (obj.event === 'batchRemove') {
        window.batchRemove(obj)
      }
    })

    form.on('submit(mail-query)', function (data) {
      window.refresh(data.field)
      return false
    })

    window.add = function () {
      layer.open({
        type: 2,
        title: '新增',
        shade: 0.1,
        area: ['550px', '550px'],
        content: MODULE_PATH + 'add'
      })
    }


    window.remove = function (obj) {
      layer.confirm('确定要删除', { icon: 3, title: '提示' }, function (index) {
        layer.close(index)
        let loading = layer.load()
        $.ajax({
          url: MODULE_PATH + 'remove/' + obj.data['id'],
          dataType: 'json',
          type: 'delete',
          success: function (result) {
            layer.close(loading)
            if (result.success) {
              popup.success(result.msg, function () {
                obj.del()
              })
            } else {
              popup.failure(result.msg)
            }
          }
        })
      })
    }

    window.batchRemove = function (obj) {
      let data = table.checkStatus(obj.config.id).data
      if (data.length === 0) {
        layer.msg('未选中数据', {
          icon: 3,
          time: 1000
        })
        return false
      }
      var ids = []
      var hasCheck = table.checkStatus('mail-table')
      var hasCheckData = hasCheck.data
      if (hasCheckData.length > 0) {
        $.each(hasCheckData, function (index, element) {
          ids.push(element.id)
        })
      }
      {#console.log(ids);#}
      layer.confirm('确定要删除选中数据', {
        icon: 3,
        title: '提示'
      }, function (index) {
        layer.close(index)
        let loading = layer.load()
        $.ajax({

          url: MODULE_PATH + 'batchRemove',
          data: { ids: ids },
          dataType: 'json',
          type: 'delete',
          success: function (result) {
            layer.close(loading)
            if (result.success) {
              popup.success(result.msg, function () {
                table.reload('mail-table')
              })
            } else {
              popup.failure(result.msg)
            }
          }
        })
      })
    }

    window.refresh = function (param) {
      table.reload('mail-table', { where: param })
    }
  })
</script>
</html>