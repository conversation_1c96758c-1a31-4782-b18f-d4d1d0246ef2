
/**************** 主题换肤  ****************/
/* 默认风格*/
.dtree-theme-item-this{background-color: #d2d2d2!important;}
.dtree-theme-item:hover{background-color: #eaeceb!important;}
.dtree-theme-item cite{font-size:12px!important;}
.dtree-theme-item:hover cite{color:#fe7786!important;}

.dtree-theme-dtreefont{font-size: 16px!important;}
.dtree-theme-ficon{color:#000!important;}
.dtree-theme-icon{color:orange!important;}
.dtree-theme-checkbox:hover{color:#fe7786!important;}
.dtree-theme-choose{color:#fe7786!important;}

/* layui主题风格*/
.dtree-layui-item-this{background: none!important;}
.dtree-layui-item:hover{background: none!important;}
.dtree-layui-item cite{font-size:14px!important;}
.dtree-layui-item:hover cite{opacity:0.5;filter:Alpha(opacity=50);text-decoration: underline;}

.dtree-layui-dtreefont{font-size: 18px!important;}
.dtree-layui-ficon{font-size: 18px!important;color:#393D49!important;}
.dtree-layui-icon{color:#666!important;}
.dtree-layui-checkbox:hover{color:#5FB878!important;}
.dtree-layui-choose{color:#5FB878!important;}

/* laySimple主题风格*/
.dtree-laySimple-item-this{background-color: #d2d2d2!important;}
.dtree-laySimple-item:hover{background-color: #eaeceb!important;}
.dtree-laySimple-item cite{font-size:12px!important;}
.dtree-laySimple-item:hover cite{color:#01AAED!important;}

.dtree-laySimple-dtreefont{font-size: 16px!important;}
.dtree-laySimple-ficon{font-size: 14px!important;color:#393D49!important;}
.dtree-laySimple-icon{color:#393D49!important;}
.dtree-laySimple-checkbox:hover{color:#01AAED!important;}
.dtree-laySimple-choose{color:#01AAED!important;}
/**************** 树基础  ****************/
/* 菜单栏*/
.dtree-menubar {padding-left: 10px;}
.dtree-menubar .layui-btn-group .layui-btn-sm{height: 20px;line-height: 20px;padding: 0 5px;font-size: 12px;}
.dtree-menubar .layui-btn-group .layui-btn-sm i{font-size: 12px!important;}

/* 工具栏*/
.dtree-toolbar{position: absolute;z-index: 940520;padding: 0;background-color: #eceaeb;}
.dtree-toolbar .layui-nav-child{position: static;}
.dtree-toolbar .layui-nav-child dd{cursor: pointer;}
.dtree-toolbar .layui-nav-child dd a i{font-size:16px;display: inline-block;margin: 0px 1px;color:#fe7786;}
.dtree-toolbar .layui-nav-child dd a i:hover{font-weight: bold;}
.dtree-toolbar .layui-nav-bar{display: none!important;}
.dtree-toolbar-tool{padding: 30px;}

.dtree-toolbar-fixed{position: absolute;right: 5px;top: 2px;font-style: normal;transition: all .3s;-webkit-transition: all .3s;}
/*.dtree-toolbar-fixed a{border:1px solid #fe7786;}*/
.dtree-toolbar-fixed a i{font-size:14px;display: inline-block;margin: 0px 1px;color:#fe7786;}
.dtree-toolbar-fixed a i:hover{opacity:0.8;filter:Alpha(opacity=80);}

/* 树基本*/
.dtree{width:260px;}
.dtree-nav-item{line-height:33px;padding-left:16px;}
.dtree-nav-ul-sid{display: none;}
.dtree-none-text{font-size: 12px;text-align: center;color: gray;}

/* 树线*/
.dtree-nav-first-line,.dtree-nav-line,.dtree-nav-last-line{position: relative;}
.dtree-nav-first-line:before{content:"";position: absolute;height: 0;border-left: 1px dotted #c0c4cc;}
.dtree-nav-first-line:after{content:"";position: absolute;height: 0;border-top: 1px dotted #c0c4cc;}

.dtree-nav-line:before{content:"";position: absolute;top: 0;left: 7px;width: 0;height: 100%;border-left: 1px dotted #c0c4cc;}
.dtree-nav-line:after{content:"";position: absolute;top: 16px;left: 8px;width: 9px;height: 0;border-top: 1px dotted #c0c4cc;}

.dtree-nav-last-line:before{content:"";position: absolute;top: 0;left: 7px;width: 0;height: 17px;border-left: 1px dotted #c0c4cc;}
.dtree-nav-last-line:after{content:"";position: absolute;top: 16px;left: 8px;width: 9px;height: 0;border-top: 1px dotted #c0c4cc;}


/* 图标及复选框*/
.dtreefont{cursor: pointer;}
.dtreefont-special{margin: 0 4px;}
.dtree-nav-checkbox-div{display: inline-block;}
.dtree-nav-checkbox-div>i{display: inline-block;margin: 0px 1px;}
.dtree-nav-checkbox-div>i:last-child{margin-right: 4px;}
.dtree-nav-checkbox-div>i:hover{opacity:0.8;filter:Alpha(opacity=80);}

/* 行 文字*/
.dtree-nav-div{display:block;vertical-align:top;position:relative;}
.dtree-nav-div cite{font-style: normal;cursor: pointer;}
.dtree-nav-div:hover cite{opacity:0.7;filter:Alpha(opacity=70);transition: all .3s;-webkit-transition: all .3s;}

/* 规则属性*/
.dtree-nav-show {display: block!important;}
.dtree-nav-hide {display: none!important;}
.dtree-nav-this {}
.dtree-icon-hide {opacity:0;filter:Alpha(opacity=0);}
.dtree-icon-null-open,.dtree-icon-null-close,.dtree-icon-null{margin: 0 2px;}
.dtree-disabled{cursor: not-allowed; color:#c2c2c2!important;}
.dtree-disabled:hover{color:#c2c2c2!important;}
.dtree-nav-div cite.dtree-disabled{font-style: normal; cursor: not-allowed; color:#c2c2c2!important;}
.dtree-nav-div>cite.dtree-disabled:hover{color:#c2c2c2!important;}


/** 下拉树属性*/
.dtree-select{position: absolute;max-height: 500px;height: 350px;overflow: auto;width: 99%;z-index: 123;display: none;border:1px solid silver;top: 42px;}
.dtree-select-show{display: block!important;}

/* 简单适配*/
@media screen and (max-width:1700px) and (min-width:1300px){	
	.dtree-nav-item {padding-left: 15px;}
}




