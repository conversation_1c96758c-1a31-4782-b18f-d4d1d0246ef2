# This file makes the 'api' directory a Python package.
from flask import Flask


def init_api_bps(app: Flask):
    from .product_api import product_api_bp
    app.register_blueprint(product_api_bp)

    from .service_api import service_api_bp
    app.register_blueprint(service_api_bp)

    # Future POS API blueprints will be imported and registered here
    from .resource_api import resource_api_bp
    app.register_blueprint(resource_api_bp)
    from .member_api import member_api_bp
    app.register_blueprint(member_api_bp)
    from .transaction_api import transaction_api_bp
    app.register_blueprint(transaction_api_bp)
    from .session_api import session_api_bp
    app.register_blueprint(session_api_bp)
    from .reports_api import reports_api_bp
    app.register_blueprint(reports_api_bp)
    from .auth_api import auth_api_bp
    app.register_blueprint(auth_api_bp)
    # etc.
