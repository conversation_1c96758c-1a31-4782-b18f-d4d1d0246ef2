{"code": 0, "msg": "...", "count": null, "data": [{"powerId": "1", "powerName": "系统管理", "powerType": "0", "powerCode": "", "powerUrl": "", "openType": null, "parentId": "0", "icon": "layui-icon-set-fill", "sort": 1, "enable": 1, "checkArr": "0"}, {"powerId": "2", "powerName": "用户管理", "powerType": "1", "powerCode": "sys:user:main", "powerUrl": "/system/user/main", "openType": null, "parentId": "1", "icon": "layui-icon-username", "sort": null, "enable": 1, "checkArr": "0"}, {"powerId": "3", "powerName": "角色管理", "powerType": "1", "powerCode": "sys:role:main", "powerUrl": "/system/role/main", "openType": null, "parentId": "1", "icon": "layui-icon-user", "sort": null, "enable": 1, "checkArr": "0"}, {"powerId": "4", "powerName": "权限管理", "powerType": "1", "powerCode": "sys:power:main", "powerUrl": "/system/power/main", "openType": null, "parentId": "1", "icon": "layui-icon-vercode", "sort": null, "checkArr": "0"}, {"powerId": "442359447487123456", "powerName": "角色列表", "powerType": "2", "powerCode": "sys:role:data", "powerUrl": "", "openType": null, "parentId": "3", "icon": "layui-icon-rate", "sort": 1, "checkArr": "0"}, {"powerId": "442417411065516032", "powerName": "敏捷开发", "powerType": "0", "powerCode": "", "powerUrl": "", "openType": null, "parentId": "0", "icon": "layui-icon-senior", "sort": 2, "checkArr": "0"}, {"powerId": "442418188639145984", "powerName": "模板管理", "powerType": "1", "powerCode": "exp:template:main", "powerUrl": "/system/user/main", "openType": null, "parentId": "442417411065516032", "icon": "layui-icon-template-1", "sort": null, "checkArr": "0"}, {"powerId": "442520236248403968", "powerName": "数据监控", "powerType": "1", "powerCode": "/druid/index.html", "powerUrl": "/druid/index.html", "openType": null, "parentId": "694203021537574912", "icon": "layui-icon-chart", "sort": 1, "checkArr": "0"}, {"powerId": "442650387514789888", "powerName": "定时任务", "powerType": "0", "powerCode": "", "powerUrl": "", "openType": null, "parentId": "0", "icon": "layui-icon-log", "sort": 5, "checkArr": "0"}, {"powerId": "442650770626711552", "powerName": "任务管理", "powerType": "1", "powerCode": "qrt:task:main", "powerUrl": "/qrt/task/main", "openType": null, "parentId": "442650387514789888", "icon": "layui-icon-chat", "sort": 1, "checkArr": "0"}, {"powerId": "442651158935375872", "powerName": "任务日志", "powerType": "1", "powerCode": "qrt:log:main", "powerUrl": "/qrt/log/main", "openType": null, "parentId": "442650387514789888", "icon": "layui-icon-file", "sort": 2, "checkArr": "0"}, {"powerId": "442722702474743808", "powerName": "数据字典", "powerType": "1", "powerCode": "system:dictType:main", "powerUrl": "/system/dictType/main", "openType": null, "parentId": "1", "icon": "layui-icon-form", "sort": 1, "checkArr": "0"}, {"powerId": "5", "powerName": "工作流程", "powerType": "0", "powerCode": "", "powerUrl": "", "openType": null, "parentId": "0", "icon": "layui-icon-util", "sort": 3, "checkArr": "0"}, {"powerId": "6", "powerName": "模型管理", "powerType": "1", "powerCode": null, "powerUrl": null, "openType": null, "parentId": "5", "icon": "layui-icon layui-icon-edit", "sort": null, "checkArr": "0"}, {"powerId": "694203021537574912", "powerName": "系统监控", "powerType": "0", "powerCode": "", "powerUrl": "", "openType": null, "parentId": "0", "icon": "layui-icon-console", "sort": 4, "checkArr": "0"}, {"powerId": "694203311615639552", "powerName": "接口文档", "powerType": "1", "powerCode": "", "powerUrl": "/swagger-ui.html", "openType": null, "parentId": "694203021537574912", "icon": "layui-icon-chart", "sort": 1, "checkArr": "0"}]}