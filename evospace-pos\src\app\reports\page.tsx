"use client";

import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Card,
  CardContent,
  CardHeader,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  InputAdornment,
  Chip
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import {
  AttachMoney,
  TrendingUp,
  People,
  ShoppingCart,
  DateRange as DateRangeIcon
} from '@mui/icons-material';
import { 
  Line, 
  Pie, 
  Doughnut 
} from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
} from 'chart.js';
import { subDays, startOfMonth, endOfMonth } from 'date-fns';
import usePosStore from '@/lib/store';
import { dashboardStats } from '@/lib/mockData';
import { useTheme } from '@/lib/theme';
import { Transaction } from '../../lib/types';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`report-tabpanel-${index}`}
      aria-labelledby={`report-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

export default function ReportsPage() {
  const [tabValue, setTabValue] = useState(0);
  const [timeRange, setTimeRange] = useState('week');
  const [startDate, setStartDate] = useState<Date | null>(subDays(new Date(), 7));
  const [endDate, setEndDate] = useState<Date | null>(new Date());
  
  const { products, members, transactions } = usePosStore();
  const { theme } = useTheme();
  
  const isDarkMode = theme === 'dark';

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleTimeRangeChange = (event: SelectChangeEvent) => {
    const value = event.target.value;
    setTimeRange(value);
    
    const today = new Date();
    
    switch (value) {
      case 'today':
        setStartDate(today);
        setEndDate(today);
        break;
      case 'week':
        setStartDate(subDays(today, 7));
        setEndDate(today);
        break;
      case 'month':
        setStartDate(startOfMonth(today));
        setEndDate(endOfMonth(today));
        break;
      case 'year':
        setStartDate(new Date(today.getFullYear(), 0, 1));
        setEndDate(new Date(today.getFullYear(), 11, 31));
        break;
      case 'custom':
        // Keep current custom dates
        break;
    }
  };

  // Generate sales data for the selected time range
  const generateSalesData = () => {
    // In a real app, this would filter transactions based on date range
    // For now, we'll use mock data
    
    const labels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    const salesData = [1250, 1420, 980, 1600, 2100, 1800, 1350];
    
    return {
      labels,
      datasets: [
        {
          label: 'Sales',
          data: salesData,
          backgroundColor: isDarkMode ? 'rgba(75, 192, 192, 0.5)' : 'rgba(54, 162, 235, 0.5)',
          borderColor: isDarkMode ? 'rgba(75, 192, 192, 1)' : 'rgba(54, 162, 235, 1)',
          borderWidth: 1,
        },
      ],
    };
  };

  // Generate product sales data
  const generateProductSalesData = () => {
    const productSales: { [productName: string]: { quantity: number; revenue: number } } = {};

    transactions.forEach(transaction => {
      transaction.sessions.forEach(session => { // session is of type Session
        session.products.forEach(sessionProduct => { // sessionProduct is of type SessionItem
          const productName = sessionProduct.name;
          if (!productSales[productName]) {
            productSales[productName] = { quantity: 0, revenue: 0 };
          }
          productSales[productName].quantity += sessionProduct.quantity;
          productSales[productName].revenue += sessionProduct.price * sessionProduct.quantity;
        });
      });
    });

    const labels = Object.keys(productSales);
    const quantitiesSold = labels.map(name => productSales[name].quantity);
    // const revenues = labels.map(name => productSales[name].revenue); // If revenue is needed for a separate dataset

    // Default colors, can be expanded if more than 4 products are common
    const defaultBackgroundColors = isDarkMode ? [
      'rgba(75, 192, 192, 0.6)',
      'rgba(153, 102, 255, 0.6)',
      'rgba(255, 159, 64, 0.6)',
      'rgba(255, 99, 132, 0.6)'
    ] : [
      'rgba(255, 99, 132, 0.6)',
      'rgba(54, 162, 235, 0.6)',
      'rgba(255, 206, 86, 0.6)',
      'rgba(75, 192, 192, 0.6)'
    ];
    const defaultBorderColors = isDarkMode ? [
      'rgba(75, 192, 192, 1)',
      'rgba(153, 102, 255, 1)',
      'rgba(255, 159, 64, 1)',
      'rgba(255, 99, 132, 1)'
    ] : [
      'rgba(255, 99, 132, 1)',
      'rgba(54, 162, 235, 1)',
      'rgba(255, 206, 86, 1)',
      'rgba(75, 192, 192, 1)'
    ];

    return {
      labels,
      datasets: [
        {
          label: 'Units Sold',
          data: quantitiesSold,
          backgroundColor: labels.map((_, i) => defaultBackgroundColors[i % defaultBackgroundColors.length]),
          borderColor: labels.map((_, i) => defaultBorderColors[i % defaultBorderColors.length]),
          borderWidth: 1,
        },
      ],
    };
  };

  // Generate category sales data
  const generateCategorySalesData = () => {
    const categorySales: { [categoryName: string]: { quantity: number; revenue: number } } = {};

    transactions.forEach(transaction => {
      transaction.sessions.forEach(session => { // session is of type Session
        session.products.forEach(sessionProduct => { // sessionProduct is of type SessionItem
          // Find the full product details to get its category
          const fullProduct = products.find(p => p.id === sessionProduct.id);
          if (fullProduct && fullProduct.category) {
            const categoryName = fullProduct.category;
            if (!categorySales[categoryName]) {
              categorySales[categoryName] = { quantity: 0, revenue: 0 };
            }
            categorySales[categoryName].quantity += sessionProduct.quantity;
            categorySales[categoryName].revenue += sessionProduct.price * sessionProduct.quantity;
          } else if (fullProduct) {
            // Handle products without a category, e.g., group them as 'Uncategorized'
            const categoryName = 'Uncategorized';
            if (!categorySales[categoryName]) {
              categorySales[categoryName] = { quantity: 0, revenue: 0 };
            }
            categorySales[categoryName].quantity += sessionProduct.quantity;
            categorySales[categoryName].revenue += sessionProduct.price * sessionProduct.quantity;
          }
        });
      });
    });

    const labels = Object.keys(categorySales);
    // Using revenue for the chart data as an example, could also be quantity
    const dataForChart = labels.map(name => categorySales[name].revenue);

    // Default colors, can be expanded if more categories are common
    const defaultBackgroundColors = isDarkMode ? [
      'rgba(75, 192, 192, 0.6)',
      'rgba(153, 102, 255, 0.6)',
      'rgba(255, 159, 64, 0.6)',
      'rgba(255, 99, 132, 0.6)',
      'rgba(54, 162, 235, 0.6)',
      'rgba(255, 206, 86, 0.6)',
      'rgba(128, 0, 128, 0.6)', // Purple
      'rgba(0, 128, 0, 0.6)'    // Green
    ] : [
      'rgba(255, 99, 132, 0.6)',
      'rgba(54, 162, 235, 0.6)',
      'rgba(255, 206, 86, 0.6)',
      'rgba(75, 192, 192, 0.6)',
      'rgba(153, 102, 255, 0.6)',
      'rgba(255, 159, 64, 0.6)',
      'rgba(255, 0, 255, 0.6)', // Magenta
      'rgba(0, 255, 0, 0.6)'    // Lime
    ];
    const defaultBorderColors = defaultBackgroundColors.map(color => color.replace('0.6', '1'));

    return {
      labels,
      datasets: [
        {
          label: 'Sales by Category (Revenue)', // Added label for clarity
          data: dataForChart,
          backgroundColor: labels.map((_, i) => defaultBackgroundColors[i % defaultBackgroundColors.length]),
          borderColor: labels.map((_, i) => defaultBorderColors[i % defaultBorderColors.length]),
          borderWidth: 1,
        },
      ],
    };
  };

  // Generate payment method data
  const generatePaymentMethodData = () => {
    // Count transactions by payment method
    const paymentMethods = transactions.reduce((acc, transaction) => {
      const method = transaction.paymentMethod;
      if (!acc[method]) {
        acc[method] = 0;
      }
      acc[method] += 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      labels: Object.keys(paymentMethods).map(method => 
        method.charAt(0).toUpperCase() + method.slice(1)
      ),
      datasets: [
        {
          data: Object.values(paymentMethods),
          backgroundColor: isDarkMode ? [
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)',
            'rgba(255, 159, 64, 0.6)'
          ] : [
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 206, 86, 0.6)'
          ],
          borderColor: isDarkMode ? [
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)'
          ] : [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)'
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Reports & Analytics</Typography>
        
        <Box sx={{ display: 'flex', gap: 2 }}>
          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={timeRange}
              label="Time Range"
              onChange={handleTimeRangeChange}
              startAdornment={
                <InputAdornment position="start">
                  <DateRangeIcon fontSize="small" />
                </InputAdornment>
              }
            >
              <MenuItem value="today">Today</MenuItem>
              <MenuItem value="week">Last 7 Days</MenuItem>
              <MenuItem value="month">This Month</MenuItem>
              <MenuItem value="year">This Year</MenuItem>
              <MenuItem value="custom">Custom Range</MenuItem>
            </Select>
          </FormControl>
          
          {timeRange === 'custom' && (
            <Box sx={{ display: 'flex', gap: 1 }}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="Start Date"
                  value={startDate}
                  onChange={(newValue) => setStartDate(newValue)}
                  slotProps={{ textField: { size: 'small' } }}
                />
                <DatePicker
                  label="End Date"
                  value={endDate}
                  onChange={(newValue) => setEndDate(newValue)}
                  slotProps={{ textField: { size: 'small' } }}
                />
              </LocalizationProvider>
            </Box>
          )}
        </Box>
      </Box>

      {/* Overview Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" sx={{ mb: 2 }}>Overview</Typography>
        <Box sx={{ 
          display: 'flex', 
          flexWrap: 'wrap', 
          gap: 2,
          justifyContent: 'space-between' 
        }}>
          <Card sx={{ 
            flex: '1 1 200px', 
            minWidth: '200px',
            bgcolor: isDarkMode ? 'rgba(0, 123, 255, 0.1)' : 'rgba(0, 123, 255, 0.05)',
            borderLeft: '4px solid',
            borderColor: 'primary.main'
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{ 
                  bgcolor: 'primary.main', 
                  color: 'primary.contrastText',
                  p: 1,
                  borderRadius: 1,
                  mr: 2
                }}>
                  <AttachMoney />
                </Box>
                <Box>
                  <Typography color="textSecondary" variant="body2">
                    Total Sales
                  </Typography>
                  <Typography variant="h5">
                    ${dashboardStats.monthlyRevenue.toFixed(2)}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>

          <Card sx={{ 
            flex: '1 1 200px', 
            minWidth: '200px',
            bgcolor: isDarkMode ? 'rgba(40, 167, 69, 0.1)' : 'rgba(40, 167, 69, 0.05)',
            borderLeft: '4px solid',
            borderColor: 'success.main'
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{ 
                  bgcolor: 'success.main', 
                  color: 'success.contrastText',
                  p: 1,
                  borderRadius: 1,
                  mr: 2
                }}>
                  <TrendingUp />
                </Box>
                <Box>
                  <Typography color="textSecondary" variant="body2">
                    Growth
                  </Typography>
                  <Typography variant="h5">
                    {dashboardStats.salesGrowth > 0 ? '+' : ''}{dashboardStats.salesGrowth}%
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>

          <Card sx={{ 
            flex: '1 1 200px', 
            minWidth: '200px',
            bgcolor: isDarkMode ? 'rgba(220, 53, 69, 0.1)' : 'rgba(220, 53, 69, 0.05)',
            borderLeft: '4px solid',
            borderColor: 'error.main'
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{ 
                  bgcolor: 'error.main', 
                  color: 'error.contrastText',
                  p: 1,
                  borderRadius: 1,
                  mr: 2
                }}>
                  <ShoppingCart />
                </Box>
                <Box>
                  <Typography color="textSecondary" variant="body2">
                    Orders Today
                  </Typography>
                  <Typography variant="h5">
                    {dashboardStats.ordersToday}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>

          <Card sx={{ 
            flex: '1 1 200px', 
            minWidth: '200px',
            bgcolor: isDarkMode ? 'rgba(255, 193, 7, 0.1)' : 'rgba(255, 193, 7, 0.05)',
            borderLeft: '4px solid',
            borderColor: 'warning.main'
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{ 
                  bgcolor: 'warning.main', 
                  color: 'warning.contrastText',
                  p: 1,
                  borderRadius: 1,
                  mr: 2
                }}>
                  <People />
                </Box>
                <Box>
                  <Typography color="textSecondary" variant="body2">
                    New Members
                  </Typography>
                  <Typography variant="h5">
                    {dashboardStats.membersToday}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Box>
      </Box>

      <Paper sx={{ width: '100%', mb: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange} 
            aria-label="report tabs"
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab label="Sales" />
            <Tab label="Products" />
            <Tab label="Payment Methods" />
            <Tab label="Transactions" />
          </Tabs>
        </Box>

        {/* Sales Tab */}
        <TabPanel value={tabValue} index={0}>
          <Card>
            <CardHeader title="Sales Over Time" />
            <Divider />
            <CardContent>
              <Box sx={{ height: 400 }}>
                <Line 
                  data={generateSalesData()} 
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                      y: {
                        beginAtZero: true,
                        title: {
                          display: true,
                          text: 'Sales ($)'
                        }
                      },
                      x: {
                        title: {
                          display: true,
                          text: 'Day'
                        }
                      }
                    }
                  }}
                />
              </Box>
            </CardContent>
          </Card>
        </TabPanel>

        {/* Products Tab */}
        <TabPanel value={tabValue} index={1}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'space-between' }}>
            <Box sx={{ width: { xs: '100%', md: 'calc(50% - 10px)' }, mb: 3 }}>
              <Card>
                <CardHeader title="Top Selling Products" />
                <Divider />
                <CardContent>
                  <Box sx={{ height: 300 }}>
                    <Pie 
                      data={generateProductSalesData()} 
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                          legend: {
                            position: 'right',
                          }
                        }
                      }}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Box>
            <Box sx={{ width: { xs: '100%', md: 'calc(50% - 10px)' }, mb: 3 }}>
              <Card>
                <CardHeader title="Sales by Category" />
                <Divider />
                <CardContent>
                  <Box sx={{ height: 300 }}>
                    <Doughnut 
                      data={generateCategorySalesData()} 
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                          legend: {
                            position: 'right',
                          }
                        }
                      }}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Box>
            <Box sx={{ width: '100%', mb: 3 }}>
              <Card>
                <CardHeader title="Product Inventory Status" />
                <Divider />
                <CardContent>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Product</TableCell>
                          <TableCell>Category</TableCell>
                          <TableCell align="right">Price</TableCell>
                          <TableCell align="right">Stock</TableCell>
                          <TableCell align="right">Status</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {products.slice(0, 5).map((product) => (
                          <TableRow key={product.id}>
                            <TableCell>{product.name}</TableCell>
                            <TableCell>{product.category}</TableCell>
                            <TableCell align="right">${Number(product.price).toFixed(2)}</TableCell>
                            <TableCell align="right">{product.stock}</TableCell>
                            <TableCell align="right">
                              <Chip 
                                label={product.stock > 10 ? "In Stock" : product.stock > 0 ? "Low Stock" : "Out of Stock"} 
                                color={product.stock > 10 ? "success" : product.stock > 0 ? "warning" : "error"}
                                size="small"
                              />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </Box>
          </Box>
        </TabPanel>

        {/* Payment Methods Tab */}
        <TabPanel value={tabValue} index={2}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'space-between' }}>
            <Box sx={{ width: { xs: '100%', md: 'calc(50% - 10px)' }, mb: 3 }}>
              <Card>
                <CardHeader title="Payment Methods" />
                <Divider />
                <CardContent>
                  <Box sx={{ height: 300 }}>
                    <Pie 
                      data={generatePaymentMethodData()} 
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                          legend: {
                            position: 'right',
                          }
                        }
                      }}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Box>
            <Box sx={{ width: { xs: '100%', md: 'calc(50% - 10px)' }, mb: 3 }}>
              <Card>
                <CardHeader title="Payment Method Analysis" />
                <Divider />
                <CardContent>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Method</TableCell>
                          <TableCell align="right">Transactions</TableCell>
                          <TableCell align="right">Amount</TableCell>
                          <TableCell align="right">Avg. Transaction</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {['cash', 'card', 'mobile'].map((method) => {
                          const methodTransactions = transactions.filter(t => t.paymentMethod === method);
                          // Use optional chaining and nullish coalescing to safely handle potentially undefined properties
                          const totalAmount = methodTransactions.length > 0 ?       
                            methodTransactions.reduce((sum: number, t: Transaction) => sum + (t.totalAmount || 0), 0) : 0;
                          const avgTransaction = methodTransactions.length > 0      
                            ? totalAmount / methodTransactions.length
                            : 0;
                          
                          return (
                            <TableRow key={method}>
                              <TableCell>{method.charAt(0).toUpperCase() + method.slice(1)}</TableCell>
                              <TableCell align="right">{methodTransactions.length}</TableCell>
                              <TableCell align="right">${totalAmount.toFixed(2)}</TableCell>
                              <TableCell align="right">${avgTransaction.toFixed(2)}</TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </Box>
          </Box>
        </TabPanel>

        {/* Transactions Tab */}
        <TabPanel value={tabValue} index={3}>
          <Card>
            <CardHeader title="Recent Transactions" />
            <Divider />
            <CardContent>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>ID</TableCell>
                      <TableCell>Member</TableCell>
                      <TableCell>Date</TableCell>
                      <TableCell>Products</TableCell>
                      <TableCell>Services</TableCell>
                      <TableCell>Resources</TableCell>
                      <TableCell>Payment Method</TableCell>
                      <TableCell align="right">Total</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {transactions.map((transaction) => {
                      const member = members.find(c => c.id === transaction.memberId);
                      const date = new Date(transaction.createdAt);
                      
                      // New logic for counting items
                      let productCount = 0;
                      let serviceCount = 0;
                      const resourceCount = transaction.sessions.length; // Each session is a resource usage

                      transaction.sessions.forEach(session => {
                        session.products.forEach(p => productCount += p.quantity);
                        session.services.forEach(s => serviceCount += s.quantity);
                      });
                      
                      return (
                        <TableRow key={transaction.id}>
                          <TableCell>#{transaction.id}</TableCell>
                          <TableCell>{transaction.memberName || (member ? member.name : 'Walk-in Member')}</TableCell>
                          <TableCell>{date.toLocaleDateString()}</TableCell>
                          <TableCell align="right">{productCount}</TableCell>
                          <TableCell align="right">{serviceCount}</TableCell>
                          <TableCell align="right">{resourceCount}</TableCell>
                          <TableCell>
                            {transaction.paymentMethod.charAt(0).toUpperCase() + transaction.paymentMethod.slice(1)}
                          </TableCell>
                          <TableCell align="right">${transaction.totalAmount.toFixed(2)}</TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </TabPanel>
      </Paper>
    </Box>
  );
}
