<!DOCTYPE html>
<html>
<head>
    <title>字典增加</title>
    {% include 'system/common/header.html' %}
</head>
<body>
<form class="layui-form" action="">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item layui-hide">
                    <label class="layui-form-label">编号</label>
                    <div class="layui-input-block">
                        <input type="text" value="{{ dict_type.id }}" name="id" lay-verify="required" autocomplete="off"
                               placeholder="请输入标题" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">名称</label>
                    <div class="layui-input-block">
                        <input type="text" value="{{ dict_type.type_name }}" name="typeName" lay-verify="required"
                               autocomplete="off" placeholder="请输入名称" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标识</label>
                    <div class="layui-input-block">
                        <input type="text" value="{{ dict_type.type_code }}" readonly name="typeCode"
                               lay-verify="required" autocomplete="off" placeholder="请输入标识" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-block">
                        <input type="radio" {% if dict_type.enable == 1 %} } checked{% endif %} name="enable" value="1"
                               title="开启">
                        <input type="radio" {% if dict_type.enable == 0 %} } checked{% endif %} name="enable" value="0"
                               title="关闭">
                    </div>
                </div>
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">描述</label>
                    <div class="layui-input-block">
                        <textarea
                                placeholder="请输入描述"
                                name="description"
                                class="layui-textarea"
                        >{% if dict_type.description %}{{ dict_type.description }}{% endif %}</textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button type="submit" class="layui-btn layui-btn-primary layui-btn-sm" lay-submit=""
                    lay-filter="dict-type-update">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button type="reset" class="layui-btn layui-btn-sm">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
</form>
{% include 'system/common/footer.html' %}
<script>
    layui.use(['form', 'jquery'], function () {
        let form = layui.form
        let $ = layui.jquery

        form.on('submit(dict-type-update)', function (data) {
            $.ajax({
                url: '/system/dict/dictType/update',
                data: JSON.stringify(data.field),
                dataType: 'json',
                contentType: 'application/json',
                type: 'put',
                success: function (result) {
                    if (result.success) {
                        layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                            parent.layer.close(parent.layer.getFrameIndex(window.name))//关闭当前页
                            parent.layui.table.reload('dict-type-table')
                        })
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1000})
                    }
                }
            })
            return false
        })
    })
</script>
<script>
</script>
</body>
</html>