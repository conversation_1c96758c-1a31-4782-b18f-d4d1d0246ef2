"use client";

import React, { useEffect, useState } from 'react';
import { Typography } from '@mui/material';
import usePosStore from '@/lib/store';

const CartTotal = () => {
  const { cartTotal, cart } = usePosStore();
  const [total, setTotal] = useState(cartTotal());

  useEffect(() => {
    setTotal(cartTotal());
  }, [cart, cartTotal]); // Re-calculate total when cart or cartTotal function changes

  return (
    <Typography variant="h6">${total.toFixed(2)}</Typography>
  );
};

export default CartTotal;
