<!DOCTYPE html>
<html>
<head>
    <title>部门修改</title>
    {% include 'system/common/header.html' %}
</head>
<body>
<form class="layui-form" action="">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item layui-hide">
                    <label class="layui-form-label">编号</label>
                    <div class="layui-input-block">
                        <input type="text" value="{{ dept.id }}" name="deptId" lay-verify="required"
                               autocomplete="off" placeholder="请输入标题" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">名称</label>
                    <div class="layui-input-block">
                        <input type="text" value="{{ dept.dept_name }}" name="deptName" lay-verify="required"
                               autocomplete="off" placeholder="请输入标题" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">负责人</label>
                    <div class="layui-input-block">
                        <input type="text" value="{{ dept.leader }}" name="leader" lay-verify="required"
                               autocomplete="off"
                               placeholder="请输入负责人"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">邮箱</label>
                    <div class="layui-input-block">
                        <input type="text" value="{{ dept.email }}" name="email" lay-verify="required"
                               autocomplete="off"
                               placeholder="请输入邮箱"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">联系方式</label>
                    <div class="layui-input-block">
                        <input type="text" value="{{ dept.phone }}" name="phone" lay-verify="required"
                               autocomplete="off"
                               placeholder="请输入联系方式"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-block">

                        <input type="radio" name="status" {% if dept.status == 1 %}checked{% endif %} value="1"
                               title="开启">

                        <input type="radio" name="status" {% if dept.status == 0 %}checked{% endif %} value="0"
                               title="关闭"
                        >
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">排序</label>
                    <div class="layui-input-inline" style="width: 100px !important;">
                        <input type="text" name="sort" value="{{ dept.sort }}" lay-verify="required" autocomplete="off"
                               placeholder="排序" min="0" step="1" lay-affix="number" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">地址</label>
                    <div class="layui-input-block">
                        <textarea placeholder="请输入详细地址" name="address"
                                  class="layui-textarea">{% if dept.address %}{{ dept.address }}{% endif %}</textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button type="submit" class="layui-btn layui-btn-sm" lay-submit=""
                    lay-filter="dept-update">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
</form>
{% include 'system/common/footer.html' %}
<script>
    layui.use(['form', 'jquery'], function () {
        let form = layui.form
        let $ = layui.jquery

        form.on('submit(dept-update)', function (data) {
            $.ajax({
                url: '/system/dept/update',
                data: JSON.stringify(data.field),
                dataType: 'json',
                contentType: 'application/json',
                type: 'put',
                success: function (result) {
                    if (result.success) {
                        layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                            parent.layer.close(parent.layer.getFrameIndex(window.name))//关闭当前页
                            parent.render()
                        })
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1000})
                    }
                }
            })
            return false
        })
    })
</script>
<script>
</script>
</body>
</html>