"use client";

import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Switch,
  FormControlLabel,
  FormControl,
  Divider,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  Tab,
  Tabs,
  Alert,
  Snackbar,
  Stack,
  InputAdornment,
  MenuItem
} from '@mui/material';
import {
  Save as SaveIcon,
  Person as PersonIcon,
  Store as StoreIcon,
  Receipt as ReceiptIcon,
  Backup as BackupIcon,
  CloudUpload as CloudUploadIcon,
  LightMode as LightModeIcon,
  DarkMode as DarkModeIcon,
  Email as EmailIcon
} from '@mui/icons-material';
import { useTheme } from '@/lib/theme';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export default function SettingsPage() {
  const [tabValue, setTabValue] = useState(0);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const { theme, toggleTheme } = useTheme();
  
  // Store settings
  const [storeSettings, setStoreSettings] = useState({
    name: 'EvoSpace Cafe',
    address: '123 Innovation Street, Tech City',
    phone: '(*************',
    email: '<EMAIL>',
    taxRate: '7.5',
    currency: 'USD'
  });
  
  // Receipt settings
  const [receiptSettings, setReceiptSettings] = useState({
    showLogo: true,
    showTaxDetails: true,
    addFooterMessage: true,
    footerMessage: 'Thank you for your business!',
    printAutomatically: false,
    emailReceipt: true
  });
  
  // User settings
  const [userSettings, setUserSettings] = useState({
    name: 'Administrator',
    email: '<EMAIL>',
    role: 'Administrator',
    language: 'English',
    theme: theme === 'dark' ? 'Dark' : 'Light',
    notifications: true
  });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleStoreSettingsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setStoreSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleReceiptSettingsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked } = e.target;
    const newValue = e.target.type === 'checkbox' ? checked : value;
    
    setReceiptSettings(prev => ({
      ...prev,
      [name]: newValue
    }));
  };

  const handleUserSettingsChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    // Handle checkbox type separately since checked only exists on HTMLInputElement
    const newValue = e.target instanceof HTMLInputElement && e.target.type === 'checkbox' 
      ? e.target.checked 
      : value;
    
    setUserSettings(prev => ({
      ...prev,
      [name]: newValue
    }));

    // Toggle theme if the theme setting is changed
    if (name === 'theme') {
      if ((value === 'Dark' && theme === 'light') || (value === 'Light' && theme === 'dark')) {
        toggleTheme();
      }
    }
  };

  const handleSaveSettings = () => {
    // In a real app, this would save settings to a database or local storage
    setSnackbarOpen(true);
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Settings</Typography>
        <Button 
          variant="contained" 
          startIcon={<SaveIcon />}
          onClick={handleSaveSettings}
        >
          Save Settings
        </Button>
      </Box>

      <Paper sx={{ width: '100%' }}>
        <Tabs 
          value={tabValue} 
          onChange={handleTabChange} 
          aria-label="settings tabs"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab icon={<StoreIcon />} label="Store" />
          <Tab icon={<ReceiptIcon />} label="Receipt" />
          <Tab icon={<PersonIcon />} label="User" />
          <Tab icon={<BackupIcon />} label="Backup & Restore" />
        </Tabs>

        {/* Store Settings */}
        <TabPanel value={tabValue} index={0}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
            <Box sx={{ flexBasis: { xs: '100%', md: '48%' } }}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Store Information
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <TextField
                      label="Store Name"
                      name="name"
                      value={storeSettings.name}
                      onChange={handleStoreSettingsChange}
                      fullWidth
                    />
                    <TextField
                      label="Address"
                      name="address"
                      value={storeSettings.address}
                      onChange={handleStoreSettingsChange}
                      fullWidth
                      multiline
                      rows={2}
                    />
                    <TextField
                      label="Phone"
                      name="phone"
                      value={storeSettings.phone}
                      onChange={handleStoreSettingsChange}
                      fullWidth
                    />
                    <TextField
                      label="Email"
                      name="email"
                      type="email"
                      value={storeSettings.email}
                      onChange={handleStoreSettingsChange}
                      fullWidth
                    />
                  </Box>
                </CardContent>
              </Card>
            </Box>
            
            <Box sx={{ flexBasis: { xs: '100%', md: '48%' } }}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Financial Settings
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <TextField
                      label="Tax Rate (%)"
                      name="taxRate"
                      type="number"
                      value={storeSettings.taxRate}
                      onChange={handleStoreSettingsChange}
                      fullWidth
                    />
                    <TextField
                      label="Currency"
                      name="currency"
                      value={storeSettings.currency}
                      onChange={handleStoreSettingsChange}
                      fullWidth
                    />
                  </Box>
                </CardContent>
              </Card>
            </Box>
          </Box>
        </TabPanel>

        {/* Receipt Settings */}
        <TabPanel value={tabValue} index={1}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
            <Box sx={{ flexBasis: { xs: '100%', sm: '48%' } }}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Receipt Content
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={receiptSettings.showLogo}
                          onChange={handleReceiptSettingsChange}
                          name="showLogo"
                        />
                      }
                      label="Show Store Logo"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={receiptSettings.showTaxDetails}
                          onChange={handleReceiptSettingsChange}
                          name="showTaxDetails"
                        />
                      }
                      label="Show Tax Details"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={receiptSettings.addFooterMessage}
                          onChange={handleReceiptSettingsChange}
                          name="addFooterMessage"
                        />
                      }
                      label="Add Footer Message"
                    />
                    {receiptSettings.addFooterMessage && (
                      <TextField
                        label="Footer Message"
                        name="footerMessage"
                        value={receiptSettings.footerMessage}
                        onChange={handleReceiptSettingsChange}
                        fullWidth
                        multiline
                        rows={2}
                      />
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Box>
            
            <Box sx={{ flexBasis: { xs: '100%', sm: '48%' } }}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Receipt Delivery
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={receiptSettings.printAutomatically}
                          onChange={handleReceiptSettingsChange}
                          name="printAutomatically"
                        />
                      }
                      label="Print Automatically"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={receiptSettings.emailReceipt}
                          onChange={handleReceiptSettingsChange}
                          name="emailReceipt"
                        />
                      }
                      label="Email Receipt to Member"
                    />
                  </Box>
                </CardContent>
              </Card>
            </Box>
          </Box>
        </TabPanel>

        {/* User Settings */}
        <TabPanel value={tabValue} index={2}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                User Profile
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 3, mb: 4 }}>
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                  <Avatar 
                    sx={{ 
                      width: 100, 
                      height: 100, 
                      bgcolor: 'primary.main',
                      fontSize: '2rem'
                    }}
                  >
                    {userSettings.name.charAt(0)}
                  </Avatar>
                  <Button size="small">Change Photo</Button>
                </Box>
                
                <Box sx={{ flexGrow: 1 }}>
                  <Stack spacing={2}>
                    <TextField
                      label="Name"
                      name="name"
                      value={userSettings.name}
                      onChange={handleUserSettingsChange}
                      fullWidth
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PersonIcon />
                          </InputAdornment>
                        ),
                      }}
                    />
                    <TextField
                      label="Email"
                      name="email"
                      type="email"
                      value={userSettings.email}
                      onChange={handleUserSettingsChange}
                      fullWidth
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <EmailIcon />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Stack>
                </Box>
              </Box>
              
              <Typography variant="h6" gutterBottom>
                Preferences
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Card variant="outlined" sx={{ p: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box>
                      <Typography variant="subtitle1">Theme</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Choose between light and dark theme
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <LightModeIcon color={theme === 'light' ? 'primary' : 'disabled'} />
                      <Switch
                        checked={theme === 'dark'}
                        onChange={() => {
                          toggleTheme();
                          setUserSettings(prev => ({
                            ...prev,
                            theme: theme === 'light' ? 'Dark' : 'Light'
                          }));
                        }}
                        inputProps={{ 'aria-label': 'theme toggle' }}
                      />
                      <DarkModeIcon color={theme === 'dark' ? 'primary' : 'disabled'} />
                    </Box>
                  </Box>
                </Card>
                
                <FormControl fullWidth>
                  <TextField
                    select
                    label="Language"
                    name="language"
                    value={userSettings.language}
                    onChange={handleUserSettingsChange}
                    fullWidth
                  >
                    <MenuItem value="English">English</MenuItem>
                    <MenuItem value="Spanish">Spanish</MenuItem>
                    <MenuItem value="French">French</MenuItem>
                    <MenuItem value="German">German</MenuItem>
                    <MenuItem value="Chinese">Chinese</MenuItem>
                  </TextField>
                </FormControl>
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={userSettings.notifications === true}
                      onChange={(e) => 
                        handleUserSettingsChange({
                          target: {
                            name: 'notifications',
                            type: 'checkbox',
                            checked: e.target.checked
                          }
                        } as React.ChangeEvent<HTMLInputElement>)
                      }
                    />
                  }
                  label="Enable Notifications"
                />
              </Box>
            </CardContent>
          </Card>
        </TabPanel>

        {/* Backup & Restore */}
        <TabPanel value={tabValue} index={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Backup & Restore
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Alert severity="info" sx={{ mb: 3 }}>
                Regular backups help protect your data. We recommend backing up your data at least once a week.
              </Alert>
              
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
                <Box sx={{ flexBasis: { xs: '100%', md: '48%' } }}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Backup Data
                      </Typography>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        Create a backup of all your POS data including products, members, and transactions.
                      </Typography>
                      <Button 
                        variant="contained" 
                        startIcon={<BackupIcon />}
                        fullWidth
                      >
                        Create Backup
                      </Button>
                    </CardContent>
                  </Card>
                </Box>
                
                <Box sx={{ flexBasis: { xs: '100%', md: '48%' } }}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Restore Data
                      </Typography>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        Restore your POS data from a previous backup file.
                      </Typography>
                      <Button 
                        variant="outlined" 
                        startIcon={<CloudUploadIcon />}
                        fullWidth
                      >
                        Upload Backup File
                      </Button>
                    </CardContent>
                  </Card>
                </Box>
                
                <Box sx={{ flexBasis: { xs: '100%' } }}>
                  <Typography variant="h6" gutterBottom>
                    Backup History
                  </Typography>
                  <Paper variant="outlined">
                    <List>
                      <ListItem>
                        <ListItemIcon>
                          <BackupIcon />
                        </ListItemIcon>
                        <ListItemText 
                          primary="Full Backup" 
                          secondary="April 28, 2025 - 10:30 AM" 
                        />
                        <Button size="small">Download</Button>
                        <Button size="small" color="error">Delete</Button>
                      </ListItem>
                      <Divider />
                      <ListItem>
                        <ListItemIcon>
                          <BackupIcon />
                        </ListItemIcon>
                        <ListItemText 
                          primary="Full Backup" 
                          secondary="April 21, 2025 - 09:15 AM" 
                        />
                        <Button size="small">Download</Button>
                        <Button size="small" color="error">Delete</Button>
                      </ListItem>
                    </List>
                  </Paper>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </TabPanel>
      </Paper>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        message="Settings saved successfully"
      />
    </Box>
  );
}
