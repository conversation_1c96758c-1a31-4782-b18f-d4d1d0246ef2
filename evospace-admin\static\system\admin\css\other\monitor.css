

.pear-card {
    width: 100%;
    height: 66px;
    background-color: #F8F8F8;
    display: inline-block;
    border-radius: 5px;
    text-align: center;
    margin-bottom: 3px;
}

.pear-card:hover,
.pear-card2:hover {
    box-shadow: 2px 0 8px 0 lightgray !important;
}

.pear-card2 {
    width: 100%;
    height: 90px;
    background-color: #F8F8F8;
    display: inline-block;
    border-radius: 5px;
    text-align: center;
    margin-bottom: 3px;
}

.pear-admin-dark .pear-card2 {
    width: 100%;
    height: 90px;
    background-color: #353537;
    display: inline-block;
    border-radius: 5px;
    text-align: center;
    margin-bottom: 3px;
}

.pear-card2 i {
    font-size: 30px;
    height: 90px;
    line-height: 90px;
}

.pear-card i {
    font-size: 30px;
    height: 66px;
    line-height: 66px;
}

.layui-col-md3 {
    text-align: center;
}

.pear-card-title {
    margin-top: 3px;
}

.person img {
    width: 90px;
    height: 90px;
    border-radius: 4px;
    margin-top: 8px;
    margin-left: 8px;
}

.pear-card2 .count {
    color: var(--global-primary-color);
    font-size: 30px;
    margin-top: 12px;
}

.pear-card2 .title {
    color: gray;
    font-size: 14px;
    margin-top: 14px;
}

.pear-card-status {
    padding: 0 10px 10px;
}

.pear-card-status li {
    position: relative;
    padding: 10px 0;
    border-bottom: 1px solid #EEE;
}

.pear-card-status li h3 {
    padding-bottom: 5px;
    font-weight: 700;
}

.pear-card-status li p {
    padding-bottom: 10px;
    padding-top: 3px;
}

.pear-card-status li > span {
    color: #999;
    height: 24px;
    line-height: 24px;
}

.pear-reply {
    position: absolute;
    right: 20px;
    height: 24px;
    line-height: 24px;
}

.person .title {
    font-size: 17px;
    font-weight: 600;
    margin-left: 18px;
    margin-top: 16px;
    position: absolute;
    display: inline-block;
}

.person .desc {
    font-size: 16px;
    font-weight: 600;
    margin-left: 115px;
    margin-top: -30px;
    position: absolute;
    display: inline-block;
}

#tooltip {
    opacity: 0; /* 默认完全透明 */
    transition: opacity 0.3s ease-in-out; /* 添加淡入淡出动画 */
    position: absolute;
    border-radius: 5px;
    padding: 10px;
    z-index: 1000;
}

#tooltip.show {
    opacity: 1; /* 完全显示 */
}

#tooltip .layui-card-body ul {
    display: grid; /* 启用 Grid 布局 */
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr)); /* 自动填充列 */
    gap: 5px; /* 设置子元素之间的间距 */
    padding: 0; /* 移除默认的 ul 内边距 */
    margin: 0; /* 移除默认的 ul 外边距 */
    list-style: none; /* 移除列表项的默认样式 */
}

#tooltip .layui-card-body ul li span {
    width: 8em;
    background-color: #E7EEFC !important;
    color: #6197F8 !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.progress-ring {
  position: relative;
  display: inline-block;
  width: 120px;
  height: 120px;
  text-align: center;
}

.progress-ring .circle {
  transform: rotate(-90deg);
}

.progress-ring .circle-bg {
  fill: none;
  stroke: #e6e6e6;  /* 背景色 */
  stroke-width: 8;   /* 环的宽度（细一点） */
}

.progress-ring .circle-progress {
  fill: none;
  stroke: var(--global-primary-color);  /* 使用 CSS 变量 */
  stroke-width: 8;   /* 环的宽度（细一点） */
  stroke-dasharray: 339.292;
  stroke-dashoffset: calc(339.292 - (339.292 * var(--progress) / 100));
  transition: stroke-dashoffset 1s;
}

.progress-ring .progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.pear-admin-dark .progress-ring .progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 18px;
  font-weight: bold;
  color: #cfcfcf;
}


