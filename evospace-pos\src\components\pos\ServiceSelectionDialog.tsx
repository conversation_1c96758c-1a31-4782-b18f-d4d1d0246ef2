"use client";

import React, { useState, useMemo } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  InputAdornment,
  Typography,
  Box,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Pagination
} from '@mui/material';
import { Search as SearchIcon } from '@mui/icons-material';
import type { Service } from '@/lib/types';

interface ServiceSelectionDialogProps {
  open: boolean;
  onClose: () => void;
  services: Service[]; 
  onSelectService: (service: Service) => void;
  title?: string;
}

const ITEMS_PER_PAGE = 8; // Consistent with Product dialog

const ServiceSelectionDialog: React.FC<ServiceSelectionDialogProps> = ({
  open,
  onClose,
  services, 
  onSelectService,
  title = "Select Service"
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDialogServiceType, setSelectedDialogServiceType] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);

  const dialogServiceTypes = useMemo(() => {
    if (!services) return [];
    // Assuming service.category acts as its 'type' for filtering
    const types = new Set(services.map(s => s.category).filter(Boolean) as string[]); 
    return Array.from(types).sort();
  }, [services]);

  const locallyFilteredServices = useMemo(() => {
    return services
      .filter(service => {
        const searchMatch = 
          service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (service.description && service.description.toLowerCase().includes(searchTerm.toLowerCase()));
        
        const typeMatch = 
          selectedDialogServiceType === 'all' || service.category === selectedDialogServiceType;
        
        return searchMatch && typeMatch;
      });
  }, [services, searchTerm, selectedDialogServiceType]);

  const paginatedServices = useMemo(() => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    return locallyFilteredServices.slice(startIndex, startIndex + ITEMS_PER_PAGE);
  }, [locallyFilteredServices, currentPage]);

  const totalPages = Math.ceil(locallyFilteredServices.length / ITEMS_PER_PAGE);

  const handleSelect = (service: Service) => {
    onSelectService(service);
    setSearchTerm(''); 
    setSelectedDialogServiceType('all');
    setCurrentPage(1);
    onClose(); 
  };

  const handleCloseDialog = () => {
    setSearchTerm('');
    setSelectedDialogServiceType('all');
    setCurrentPage(1);
    onClose();
  }

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setCurrentPage(value);
  };

  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, selectedDialogServiceType]);

  return (
    <Dialog open={open} onClose={handleCloseDialog} maxWidth="lg" fullWidth scroll="paper">
      <DialogTitle>{title}</DialogTitle>
      <DialogContent sx={{ pb: 1, display: 'flex', flexDirection: 'column' }}>
        <Box sx={{ display: 'flex', gap: 2, mb: 2, position: 'sticky', top: 0, bgcolor: 'background.paper', zIndex: 1, pt:1, pb:1 }}>
          <TextField
            autoFocus
            margin="none"
            label="Search services..."
            type="text"
            fullWidth
            variant="outlined"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ flexGrow: 2 }}
          />
          <FormControl sx={{ minWidth: 200, flexGrow: 1 }}>
            <InputLabel>Service Type</InputLabel>
            <Select
              value={selectedDialogServiceType}
              label="Service Type"
              onChange={(e) => setSelectedDialogServiceType(e.target.value as string)}
            >
              <MenuItem value="all">All Types</MenuItem>
              {dialogServiceTypes.map((type) => (
                <MenuItem key={type} value={type}>{type}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>

        <Box sx={{ flexGrow: 1, overflowY: 'auto', pb: 1 }}>
          {paginatedServices.length > 0 ? (
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, justifyContent: 'flex-start', pt: 1 }}>
              {paginatedServices.map((service) => (
                <Card 
                  key={service.id} 
                  sx={{ 
                    width: 200, 
                    cursor: 'pointer',
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'scale(1.03)',
                      boxShadow: 3
                    }
                  }}
                  onClick={() => handleSelect(service)}
                >
                  <CardContent>
                    <Typography gutterBottom variant="h6" component="div">
                      {service.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {service.duration ? `${service.duration} minutes` : 
                       service.unit ? `Per ${service.unit}` : 'Fixed price'}
                    </Typography>
                    <Typography variant="h6" color="primary" sx={{ mt: 1 }}>
                      ${Number(service.price).toFixed(2)}
                    </Typography>
                  </CardContent>
                </Card>
              ))}
            </Box>
          ) : (
            <Typography sx={{ textAlign: 'center', mt: 2 }}>
              {searchTerm || selectedDialogServiceType !== 'all' ? 'No services match your search or filter.' : 'No services available.'}
            </Typography>
          )}
        </Box>
      </DialogContent>
      <DialogActions sx={{ justifyContent: 'flex-end', alignItems: 'center', pt:1, pb:1, gap: 2 }}>
        {totalPages > 1 && (
          <Pagination 
            count={totalPages} 
            page={currentPage} 
            onChange={handlePageChange} 
            color="primary" 
            size="small"
            sx={{ mr: 'auto' }} // Push pagination to the left
          />
        )}
        <Button onClick={handleCloseDialog}>Cancel</Button>
      </DialogActions>
    </Dialog>
  );
};

export default ServiceSelectionDialog;
