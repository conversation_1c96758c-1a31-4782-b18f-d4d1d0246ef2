// Common types for EvoSpace POS system

export interface User {
  id: number;
  username: string;
  name: string;
  email: string;
  role: string;
  store_id?: number;
  store_name?: string;
  password?: string; // Added optional password field
}

export interface Store {
  id: number;
  name: string;
  address?: string;
  phone?: string;
}

export interface Category { // Added Category interface
  id: number;
  name: string;
}

export interface Product {
  id: number;
  name: string;
  description?: string; // Added
  category: string;
  price: number;
  stock: number;
  image: string;
  sku?: string; // Added
  barcode?: string; // Added
}

export type ResourceStatus = 'available' | 'in-use' | 'booked' | 'maintenance';

export interface Resource {
  id: number;
  name: string;
  type: string;
  capacity: number;
  hourly_rate: number;
  status: ResourceStatus;
  x: number;
  y: number;
  width: number;
  height: number;
  floor: number;
  zone: string;
}

export interface Service {
  id: number;
  name: string;
  description: string;
  price: number;
  duration?: number;
  unit?: string;
  category?: string;
}

export interface Member {
  id: number;
  name: string;
  email: string;
  phone: string;
  joinDate?: string;
  totalSpent: number;
  visits: number;
}

export interface Transaction {
  id: number;
  transaction_number?: string;
  memberId: number | null;
  memberName?: string;
  user_id?: number;
  subtotal?: number;
  tax_amount?: number;
  tax_rate?: number;
  discount_amount?: number;
  discount_rate?: number;
  totalAmount: number;
  actual_amount?: number;
  createdAt: string;
  updatedAt?: string;
  paymentMethod: string;
  payment_reference?: string;
  status: 'pending' | 'completed' | 'cancelled' | 'refunded';
  source?: string;
  receipt_number?: string;
  notes?: string;
  sessions: Session[];
}

export interface DashboardStats {
  totalSales: number;
  ordersToday: number;
  membersToday: number;
  salesGrowth: number;
  recentTransactions: {
    id: number;
    member: string;
    amount: number;
    time: string;
  }[];
}

export interface SessionItem {
  id: number;
  type: 'product' | 'service'; 
  name: string;
  price: number;
  quantity: number;
}

export interface Session {
  id: number;
  resource_id: number;
  user_id?: number;
  member_id?: number | null;
  start_time: string;
  end_time?: string;
  status: 'active' | 'completed' | 'cancelled' | 'open' | 'closed';
  products: SessionItem[];
  services: SessionItem[];
  notes?: string;
}

export interface CartItem {
  id: number; // This is the resource_id
  name: string;
  price: number; // This is the resource's hourly_rate
  sessionId?: number; // The ID of the active session associated with this resource
}

// Authentication Types
export interface LoginCredentials {
  username: string;
  password: string;
  store_id: number;
}

export interface LoginResponse {
  token: string;
  user: User;
}
