"use client";

import React, { useState, useMemo, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  InputAdornment,
  Stack,
  Card,
  CardContent,
  CardActions,
  Chip,
  Divider,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  SelectChangeEvent
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  AccessTime as TimeIcon,
  Description as DescriptionIcon,
  Category as CategoryIcon
} from '@mui/icons-material';
import usePosStore from '@/lib/store';
import ConfirmDialog from '@/components/ConfirmDialog';

interface ServiceFormData {
  id?: number;
  name: string;
  price: string;
  duration?: string;
  unit?: string;
  description?: string;
  category?: string;
}

export default function ServicesPage() {
  const { services, fetchServices, addService, updateService, deleteService, authUser } = usePosStore();
  const [searchQuery, setSearchQuery] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [formData, setFormData] = useState<ServiceFormData>({
    name: '',
    price: '',
    duration: '',
    unit: '',
    description: '',
    category: ''
  });
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [serviceToDelete, setServiceToDelete] = useState<number | null>(null);
  const [serviceType, setServiceType] = useState('all');
  const [selectedCategory, setSelectedCategory] = useState('all');

  useEffect(() => {
    if (authUser.token) {
      fetchServices();
    }
  }, [authUser.token, fetchServices]);

  // Extract unique categories from services
  const serviceCategories = useMemo(() => {
    const categories = services
      .map(service => service.category)
      .filter((category): category is string => Boolean(category));
    return Array.from(new Set(categories)).sort();
  }, [services]);

  // Filter services based on search query, selected type and category
  const filteredServices = services.filter(service => {
    const matchesSearch = 
      service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (service.description && service.description.toLowerCase().includes(searchQuery.toLowerCase()));
    
    let matchesType = true;
    if (serviceType === 'hourly') {
      matchesType = Boolean(service.duration);
    } else if (serviceType === 'per-unit') {
      matchesType = Boolean(service.unit);
    } else if (serviceType === 'fixed') {
      matchesType = !service.duration && !service.unit;
    }
    
    const matchesCategory = selectedCategory === 'all' || service.category === selectedCategory;
    
    return matchesSearch && matchesType && matchesCategory;
  });

  const handleOpenDialog = (edit = false, service?: typeof services[0]) => {
    if (edit && service) {
      setFormData({
        id: service.id,
        name: service.name,
        price: service.price.toString(),
        duration: service.duration?.toString() || '',
        unit: service.unit || '',
        description: service.description || '',
        category: service.category || ''
      });
      setEditMode(true);
    } else {
      setFormData({
        name: '',
        price: '',
        duration: '',
        unit: '',
        description: '',
        category: ''
      });
      setEditMode(false);
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (e: SelectChangeEvent) => {
    setServiceType(e.target.value);
  };

  const handleSubmit = async () => {
    const serviceData = {
      name: formData.name,
      price: parseFloat(formData.price),
      duration: formData.duration ? parseInt(formData.duration) : undefined,
      unit: formData.unit || undefined,
      description: formData.description || undefined,
      category: formData.category || undefined,
    };

    try {
      if (editMode && formData.id !== undefined) {
        await updateService(formData.id, serviceData);
      } else {
        // For addService, ensure id is not part of the payload if your API expects it that way
        const { id, ...addData } = formData; // eslint-disable-line @typescript-eslint/no-unused-vars
        await addService({
            name: addData.name,
            price: parseFloat(addData.price),
            duration: addData.duration ? parseInt(addData.duration) : undefined,
            unit: addData.unit || undefined,
            description: addData.description || '', // Ensure description is always a string
            category: addData.category || undefined,
        });
      }
      handleCloseDialog();
    } catch (error) {
      console.error("Failed to save service:", error);
      // Optionally, show an error message to the user
    }
  };

  const handleDeleteClick = (serviceId: number) => {
    setServiceToDelete(serviceId);
    setDeleteConfirmOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (serviceToDelete !== null) {
      try {
        await deleteService(serviceToDelete);
        setDeleteConfirmOpen(false);
        setServiceToDelete(null);
      } catch (error) {
        console.error("Failed to delete service:", error);
        // Optionally, show an error message to the user
      }
    }
  };

  const handleCancelDelete = () => {
    setDeleteConfirmOpen(false);
    setServiceToDelete(null);
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Services</Typography>
        <Button 
          variant="contained" 
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Add Service
        </Button>
      </Box>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
          <Box sx={{ flexBasis: { xs: '100%', sm: '30%' } }}>
            <TextField
              fullWidth
              placeholder="Search services..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Box>
          <Box sx={{ flexBasis: { xs: '100%', sm: '30%' } }}>
            <FormControl fullWidth>
              <InputLabel id="service-type-label">Service Type</InputLabel>
              <Select
                labelId="service-type-label"
                value={serviceType}
                label="Service Type"
                onChange={handleSelectChange}
              >
                <MenuItem value="all">All Services</MenuItem>
                <MenuItem value="hourly">Hourly</MenuItem>
                <MenuItem value="fixed">Fixed Price</MenuItem>
                <MenuItem value="per-unit">Per Unit</MenuItem>
              </Select>
            </FormControl>
          </Box>
          <Box sx={{ flexBasis: { xs: '100%', sm: '30%' } }}>
            <FormControl fullWidth>
              <InputLabel id="service-category-label">Category</InputLabel>
              <Select
                labelId="service-category-label"
                value={selectedCategory}
                label="Category"
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                <MenuItem value="all">All Categories</MenuItem>
                {serviceCategories.map(category => (
                  <MenuItem key={category} value={category}>{category}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </Box>
      </Paper>

      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
        {filteredServices.length > 0 ? (
          filteredServices.map(service => (
            <Box key={service.id} sx={{ flexBasis: { xs: '100%', sm: '45%', md: '30%' } }}>
              <Card elevation={2}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {service.name}
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                    {service.category && (
                      <Chip 
                        label={service.category} 
                        size="small" 
                        color="primary" 
                        variant="outlined"
                      />
                    )}
                    {service.duration && (
                      <Chip 
                        icon={<TimeIcon fontSize="small" />} 
                        label={`${service.duration} min`} 
                        size="small" 
                      />
                    )}
                    {service.unit && (
                      <Chip 
                        icon={<CategoryIcon fontSize="small" />} 
                        label={`Per ${service.unit}`} 
                        size="small" 
                      />
                    )}
                  </Box>
                  
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {service.description}
                  </Typography>
                  
                  <Divider sx={{ mb: 2 }} />
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="h6" color="primary">
                      ${Number(service.price).toFixed(2)}
                    </Typography>
                  </Box>
                </CardContent>
                <CardActions>
                  <Button 
                    size="small" 
                    startIcon={<EditIcon />}
                    onClick={() => handleOpenDialog(true, service)}
                  >
                    Edit
                  </Button>
                  <Button 
                    size="small" 
                    color="error" 
                    startIcon={<DeleteIcon />}
                    onClick={() => handleDeleteClick(service.id)}
                  >
                    Delete
                  </Button>
                </CardActions>
              </Card>
            </Box>
          ))
        ) : (
          <Box sx={{ width: '100%', p: 4, textAlign: 'center' }}>
            <Paper sx={{ p: 4, borderRadius: 2 }}>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No services found
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Try adjusting your search or category filter, or add a new service.
              </Typography>
              <Button 
                variant="contained" 
                startIcon={<AddIcon />} 
                sx={{ mt: 2 }}
                onClick={() => handleOpenDialog()}
              >
                Add Service
              </Button>
            </Paper>
          </Box>
        )}
      </Box>

      {/* Add/Edit Service Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>{editMode ? 'Edit Service' : 'Add New Service'}</DialogTitle>
        <DialogContent>
          <Stack spacing={2} sx={{ mt: 1 }}>
            <TextField
              label="Service Name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              fullWidth
              required
            />
            <TextField
              label="Price"
              name="price"
              type="number"
              value={formData.price}
              onChange={handleInputChange}
              fullWidth
              required
              InputProps={{
                startAdornment: <InputAdornment position="start">$</InputAdornment>,
              }}
            />
            <TextField
              label="Duration (minutes)"
              name="duration"
              type="number"
              value={formData.duration}
              onChange={handleInputChange}
              fullWidth
              helperText="Leave empty for non-time-based services"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <TimeIcon />
                  </InputAdornment>
                ),
              }}
            />
            <TextField
              label="Unit (e.g., 'per page', 'per person')"
              name="unit"
              value={formData.unit}
              onChange={handleInputChange}
              fullWidth
              helperText="Leave empty for fixed-price services"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <CategoryIcon />
                  </InputAdornment>
                ),
              }}
            />
            <TextField
              label="Description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              fullWidth
              multiline
              rows={3}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <DescriptionIcon />
                  </InputAdornment>
                ),
              }}
            />
            <FormControl fullWidth>
              <InputLabel id="service-category-label">Category</InputLabel>
              <Select
                labelId="service-category-label"
                value={formData.category}
                label="Category"
                onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
              >
                <MenuItem value="all">All Categories</MenuItem>
                {serviceCategories.map(category => (
                  <MenuItem key={category} value={category}>{category}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained"
            disabled={!formData.name || !formData.price}
          >
            {editMode ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog using shared component */}
      <ConfirmDialog
        open={deleteConfirmOpen}
        title="Confirm Delete"
        message="Are you sure you want to delete this service? This action cannot be undone."
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        confirmText="Delete"
        confirmButtonColor="error"
      />
    </Box>
  );
}
