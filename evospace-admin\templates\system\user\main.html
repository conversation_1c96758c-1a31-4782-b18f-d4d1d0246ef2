<!DOCTYPE html>
<html>
<head>
    <title>用户管理</title>
    {% include 'system/common/header.html' %}
    <link rel="stylesheet" href="{{ url_for('static', filename='system/admin/css/other/user.css') }}"/>
</head>
<body class="pear-container">
{# 查询表单 #}
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form" action="" lay-filter="user-query-form">
            <div class="layui-form-item" style="margin-bottom: unset;">
                <label class="layui-form-label">用户</label>
                <div class="layui-input-inline">
                    <input type="text" name="realname" placeholder="" class="layui-input">
                </div>
                <label class="layui-form-label">账号</label>
                <div class="layui-input-inline">
                    <input type="text" name="username" placeholder="" class="layui-input">
                </div>
                <button class="layui-btn layui-btn-md" lay-submit lay-filter="user-query">
                    <i class="layui-icon layui-icon-search"></i>
                    查询
                </button>
                <button type="reset" class="layui-btn layui-btn-primary layui-btn-md">
                    <i class="layui-icon layui-icon-refresh"></i>
                    重置
                </button>
            </div>
        </form>
    </div>
</div>
<div class="user-left user-collasped">
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="button button-primary user-group" user-group=""> 全 部 用 户</div>
            <div class="button button-default user-group" user-group="-1"> 默 认 分 组</div>
            <div style="overflow: auto">
                <ul id="dept-tree" class="dept-tree" data-id="0"></ul>
            </div>
        </div>
    </div>
</div>
{# 用户表格 #}
<div class="user-main user-collasped">
    <div class="layui-card">
        <div class="layui-card-body">
            <table id="user-table" lay-filter="user-table"></table>
        </div>
    </div>
</div>
</body>

{% include 'system/common/footer.html' %}
{# 表格操作 #}
<script type="text/html" id="user-toolbar">
    {% if authorize("system:user:add") %}
        <button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="add">
            <i class="pear-icon pear-icon-add"></i>
            新增
        </button>
    {% endif %}
    <button class="layui-btn layui-btn-sm" lay-event="collasped">
        <i class="pear-icon pear-icon-modular"></i>
        高级
    </button>
</script>

{# 用户修改操作 #}
<script type="text/html" id="user-bar">
    {% if authorize("system:user:edit") %}
        <button class="layui-btn layui-btn-xs" lay-event="edit"><i class="pear-icon pear-icon-edit"> 编辑</i>
        </button>
    {% endif %}
    {% if authorize("system:user:remove") %}
        <button class="layui-btn layui-btn-danger layui-btn-xs" lay-event="remove"><i
                class="pear-icon pear-icon-ashbin"> 删除</i>
        </button>
    {% endif %}
</script>

{% raw %}
<script type="text/html" id="user-enable">
    <input type="checkbox" name="enable" value="{{ d.id }}" lay-skin="switch" lay-text="启用|禁用"
           lay-filter="user-enable"
            {{# if(d.enable==1){ }} checked {{# } }} />
</script>

<script type="text/html" id="user-createTime">
    {{layui.util.toDateString(d.create_at,  "yyyy-MM-dd HH:mm:ss")}}
</script>

<script type="text/html" id="user-updateTime">
    {{layui.util.toDateString(d.update_at,  "yyyy-MM-dd HH:mm:ss")}}
</script>
{% endraw %}


<script>
    layui.use(['table', 'dtree', 'form', 'jquery', 'popup', 'common'], function () {
        let table = layui.table
        let form = layui.form
        let $ = layui.jquery
        let dtree = layui.dtree
        let popup = layui.popup
        let common = layui.common
        let MODULE_PATH = "{{ url_for('system.user.main') }}"
        // 表格数据
        let cols = [
            [
                {title: '编号', field: 'id', align: 'center'},
                {title: '姓名', field: 'realname', align: 'center', width: 110},
                {title: '账号', field: 'username', align: 'center'},
                {title: '部门', field: 'dept_name', align: 'center'},
                {title: '启用', field: 'enable', align: 'center', templet: '#user-enable', width: 120},
                {title: '注册时间', field: 'create_at', templet: '#user-createTime', align: 'center'},
                {title: '更新时间', field: 'update_at', templet: '#user-updateTime', align: 'center'},
                {title: '操作', toolbar: '#user-bar', align: 'center', width: 180}
            ]
        ]

        // 渲染表格数据
        table.render({
            elem: '#user-table',
            url: MODULE_PATH + 'data',
            page: true,
            cols: cols,
            skin: 'line',
            toolbar: '#user-toolbar', /*工具栏*/
            text: {none: '暂无人员信息'},
            defaultToolbar: [{layEvent: 'refresh', icon: 'layui-icon-refresh'}, 'filter', 'print', 'exports'] /*默认工具栏*/
        })

        // 公司部门树状图菜单
        dtree.render({
            elem: '#dept-tree',
            method: 'get',
            url: '/system/dept/tree',
            dataFormat: 'list',
            line: true,
            skin: 'laySimple',
            icon: '-1',
            response: {treeId: 'id', parentId: 'parent_id', title: 'dept_name'},
        })

        // 菜单栏渲染
        dtree.on('node(\'dept-tree\')', function (obj) {
            let field = form.val('user-query-form') /*用户账号查询*/
            field.deptId = obj.param.nodeId
            window.refresh(field)
        })

        //
        $('.user-group').click(function () {
            let group = $(this).attr('user-group')
            let field = form.val('user-query-form')
            if (group === '-1') {
                field.deptId = group
                $(this).removeClass('button-default')
                $(this).prev().removeClass('button-primary')
                $(this).prev().addClass('button-default')
                $(this).addClass('button-primary')
            } else {
                field.deptId = group
                $(this).removeClass('button-default')
                $(this).next().removeClass('button-primary')
                $(this).next().addClass('button-default')
                $(this).addClass('button-primary')
            }
            window.refresh(field)
        })

        table.on('tool(user-table)', function (obj) {
            if (obj.event === 'remove') {
                window.remove(obj)
            } else if (obj.event === 'edit') {
                window.edit(obj)
            }
        })

        table.on('toolbar(user-table)', function (obj) {
            if (obj.event === 'add') {
                window.add()
            } else if (obj.event === 'refresh') {
                window.refresh()
            } else if (obj.event === 'batchRemove') {
                window.batchRemove(obj)
            } else if (obj.event === 'collasped') {
                $('.user-left').toggleClass('user-collasped')
                $('.user-main').toggleClass('user-collasped')
                table.resize()
            }
        })

        form.on('submit(user-query)', function (data) {
            window.refresh(data.field)
            return false
        })

        form.on('switch(user-enable)', function (obj) {
            let operate
            if (obj.elem.checked) {
                operate = 'enable'
            } else {
                operate = 'disable'
            }
            let loading = layer.load()
            $.ajax({
                url: '{{ url_for('system.user.main') }}' + operate,
                data: JSON.stringify({userId: this.value}),
                dataType: 'json',
                contentType: 'application/json',
                type: 'put',
                success: function (result) {
                    layer.close(loading)
                    if (result.success) {
                        popup.success(result.msg)
                    } else {
                        popup.failure(result.msg)
                    }
                }
            })
        })

        window.add = function () {
            layer.open({
                type: 2,
                title: '新增',
                shade: 0.1,
                area: ['550px', '550px'],
                content: MODULE_PATH + 'add'
            })
        }

        window.edit = function (obj) {
            layer.open({
                type: 2,
                title: '修改',
                shade: 0.1,
                area: ['550px', '500px'],
                content: MODULE_PATH + 'edit/' + obj.data['id']
            })
        }

        window.remove = function (obj) {
            layer.confirm('确定要删除该用户', {icon: 3, title: '提示'}, function (index) {
                layer.close(index)
                let loading = layer.load()
                $.ajax({
                    url: MODULE_PATH + 'remove/' + obj.data['id'],
                    dataType: 'json',
                    type: 'delete',
                    success: function (result) {
                        layer.close(loading)
                        if (result.success) {
                            popup.success(result.msg, function () {
                                obj.del()
                            })
                        } else {
                            popup.failure(result.msg)
                        }
                    }
                })
            })
        }


        window.refresh = function (param) {
            table.reload('user-table', {where: param})
        }
    })
</script>
</html>