from applications.extensions import db
from sqlalchemy.dialects.mysql import INTEGER
import datetime

class PosTransaction(db.Model):
    __tablename__ = 'pos_transaction'

    id = db.Column(INTEGER(unsigned=True), primary_key=True, autoincrement=True)
    transaction_number = db.Column(db.String(50), nullable=True, unique=True)  # Human-readable transaction ID
    
    # Customer information
    member_id = db.Column(INTEGER(unsigned=True), db.<PERSON>ey('pos_member.id'), nullable=True)  # Optional: transaction might not be linked to a member
    user_id = db.Column(INTEGER(unsigned=True), db.<PERSON><PERSON><PERSON>('admin_user.id'), nullable=True)  # Staff who processed the transaction
    
    # Store isolation - Multi-store support
    store_id = db.Column(db.Integer, db.ForeignKey('admin_dept.id'), nullable=False)
    store = db.relationship('Dept', backref='pos_transactions')
    
    # Financial details
    subtotal = db.Column(db.Numeric(10, 2), nullable=False)  # Sum of all items before tax and discount
    tax_amount = db.Column(db.Numeric(10, 2), nullable=False, default=0.00)  # Total tax amount
    tax_rate = db.Column(db.Numeric(5, 2), nullable=True)  # Tax rate percentage
    discount_amount = db.Column(db.Numeric(10, 2), nullable=True)  # Discount amount
    discount_rate = db.Column(db.Numeric(5, 2), nullable=True)  # Discount percentage
    total_amount = db.Column(db.Numeric(10, 2), nullable=False)  # Subtotal + tax
    actual_amount = db.Column(db.Numeric(10, 2), nullable=False)  # Final amount after discounts
    
    # Payment information
    payment_method = db.Column(db.String(50), nullable=False)  # e.g., 'cash', 'credit_card', 'mobile_payment'
    payment_reference = db.Column(db.String(100), nullable=True)  # Reference number from payment processor
    payment_details = db.Column(db.Text, nullable=True)  # Additional payment details (JSON)
    
    # Receipt information
    receipt_number = db.Column(db.String(50), nullable=True, unique=True)  # Receipt/invoice number
    receipt_url = db.Column(db.String(255), nullable=True)  # URL to digital receipt
    
    # Refund information
    refund_amount = db.Column(db.Numeric(10, 2), nullable=True)  # Amount refunded
    refund_reason = db.Column(db.String(255), nullable=True)  # Reason for refund
    refund_date = db.Column(db.DateTime, nullable=True)  # When refund was processed
    refund_reference = db.Column(db.String(100), nullable=True)  # Reference for refund
    original_transaction_id = db.Column(INTEGER(unsigned=True), nullable=True)  # For refund transactions, link to original
    
    # Status and metadata
    status = db.Column(db.String(50), nullable=False, default='pending')  # e.g., 'pending', 'completed', 'refunded', 'cancelled'
    notes = db.Column(db.Text, nullable=True)  # Transaction notes
    source = db.Column(db.String(50), nullable=True, default='pos')  # e.g., 'pos', 'online', 'mobile'
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    # Relationships
    sessions = db.relationship('PosTransactionSession', back_populates='transaction')
    member = db.relationship('PosMember', backref=db.backref('transactions', lazy=True))
    user = db.relationship('User', backref=db.backref('transactions', lazy=True))  # Staff member

class PosTransactionSession(db.Model):
    __tablename__ = 'pos_transaction_session'

    id = db.Column(INTEGER(unsigned=True), primary_key=True, autoincrement=True)
    transaction_id = db.Column(INTEGER(unsigned=True), db.ForeignKey('pos_transaction.id'), nullable=False)
    session_id = db.Column(INTEGER(unsigned=True), db.ForeignKey('pos_session.id'), nullable=False)
    duration = db.Column(db.Integer, nullable=True)  # Duration of the session in minutes
    amount = db.Column(db.Numeric(10, 2), nullable=True)  # Amount attributed to this session
    notes = db.Column(db.Text, nullable=True)  # Notes about this session's contribution
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)
    
    # Relationship to session
    transaction = db.relationship('PosTransaction', back_populates='sessions')
    session = db.relationship('PosSession', backref=db.backref('transaction_sessions'))

class PosTransactionService(db.Model):
    __tablename__ = 'pos_transaction_service'
    
    id = db.Column(INTEGER(unsigned=True), primary_key=True, autoincrement=True)
    transaction_id = db.Column(INTEGER(unsigned=True), db.ForeignKey('pos_transaction.id'), nullable=False)
    service_id = db.Column(INTEGER(unsigned=True), db.ForeignKey('pos_service.id'), nullable=False)
    
    # Service details at time of purchase (for historical record)
    name = db.Column(db.String(255), nullable=True)
    description = db.Column(db.Text, nullable=True)
    quantity = db.Column(db.Integer, nullable=False, default=1)
    unit_price = db.Column(db.Numeric(10, 2), nullable=False)  # Price per unit
    discount_amount = db.Column(db.Numeric(10, 2), nullable=True)  # Discount per unit
    tax_amount = db.Column(db.Numeric(10, 2), nullable=True)  # Tax per unit
    total_price = db.Column(db.Numeric(10, 2), nullable=False)  # (unit_price * quantity) - discount + tax
    
    # Additional information
    notes = db.Column(db.Text, nullable=True)  # Service-specific notes
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)
    
    # Relationships
    transaction = db.relationship('PosTransaction', backref=db.backref('services', lazy=True))
    service = db.relationship('PosService', backref=db.backref('transaction_services', lazy=True))

class PosTransactionProduct(db.Model):
    __tablename__ = 'pos_transaction_product'
    
    id = db.Column(INTEGER(unsigned=True), primary_key=True, autoincrement=True)
    transaction_id = db.Column(INTEGER(unsigned=True), db.ForeignKey('pos_transaction.id'), nullable=False)
    product_id = db.Column(INTEGER(unsigned=True), db.ForeignKey('pos_product.id'), nullable=False)
    
    # Product details at time of purchase (for historical record)
    name = db.Column(db.String(255), nullable=True)
    description = db.Column(db.Text, nullable=True)
    quantity = db.Column(db.Integer, nullable=False, default=1)
    unit_price = db.Column(db.Numeric(10, 2), nullable=False)  # Price per unit
    discount_amount = db.Column(db.Numeric(10, 2), nullable=True)  # Discount per unit
    tax_amount = db.Column(db.Numeric(10, 2), nullable=True)  # Tax per unit
    total_price = db.Column(db.Numeric(10, 2), nullable=False)  # (unit_price * quantity) - discount + tax
    
    # Additional information
    sku = db.Column(db.String(50), nullable=True)  # Stock keeping unit
    barcode = db.Column(db.String(50), nullable=True)  # Barcode/UPC
    category = db.Column(db.String(100), nullable=True)  # Product category
    notes = db.Column(db.Text, nullable=True)  # Product-specific notes
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)
    
    # Relationships
    transaction = db.relationship('PosTransaction', backref=db.backref('products', lazy=True))
    product = db.relationship('PosProduct', backref=db.backref('transaction_products', lazy=True))