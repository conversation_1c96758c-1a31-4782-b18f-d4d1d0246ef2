import { apiCall, ApiResponse, PaginatedApiResponse } from './apiClient';
import type {
  Product,
  Service,
  Member,
  Resource,
  Transaction,
  Session,
  LoginCredentials,
  LoginResponse,
  Store
} from './types';

// Define transaction item types
interface TransactionItem {
  id: number;
  transaction_id: number;
  product_id?: number;
  service_id?: number;
  name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
}

const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    STORES: '/auth/stores',
  },
  MEMBERS: {
    BASE: '/members',
    DETAIL: (id: number) => `/members/${id}`,
  },
  PRODUCTS: {
    BASE: '/products',
    DETAIL: (id: number) => `/products/${id}`,
  },
  SERVICES: {
    BASE: '/services',
    DETAIL: (id: number) => `/services/${id}`,
  },
  RESOURCES: {
    BASE: '/resources',
    DETAIL: (id: number) => `/resources/${id}`,
  },
  TRANSACTIONS: {
    BASE: '/transactions',
    DETAIL: (id: number) => `/transactions/${id}`,
    ITEMS: (id: number) => `/transactions/${id}/items`,
    ITEM_DETAIL: (transactionId: number, itemId: number) => `/transactions/${transactionId}/items/${itemId}`,
    COMPLETE: (id: number) => `/transactions/${id}/complete`,
    REFUND: (id: number) => `/transactions/${id}/refund`,
    CANCEL: (id: number) => `/transactions/${id}/cancel`,
  },
  SESSIONS: {
    BASE: '/sessions',
    DETAIL: (id: number) => `/sessions/${id}`,
    CLOSE: (id: number) => `/sessions/${id}/close`,
    SERVICES: (id: number) => `/sessions/${id}/services`,
    PRODUCTS: (id: number) => `/sessions/${id}/products`,
  },
  REPORTS: {
    SALES_SUMMARY: '/reports/sales_summary',
    SALES_BY_CATEGORY: '/reports/sales_by_category',
    PRODUCT_PERFORMANCE: '/reports/product_performance',
    RESOURCE_UTILIZATION: '/reports/resource_utilization',
    DASHBOARD_SUMMARY: '/reports/dashboard_summary',
    SALES_BY_PAYMENT_METHOD: '/reports/sales_by_payment_method',
    MEMBER_ACTIVITY: '/reports/member_activity',
  },
};

// Auth Service
export const authService = {
  login: async (credentials: LoginCredentials): Promise<ApiResponse<LoginResponse>> => {
    return apiCall<ApiResponse<LoginResponse>>(API_ENDPOINTS.AUTH.LOGIN, {
      method: 'POST',
      body: credentials,
    });
  },
  
  getStores: async (): Promise<ApiResponse<{ items: Store[] }>> => {
    return apiCall<ApiResponse<{ items: Store[] }>>(API_ENDPOINTS.AUTH.STORES, {
      method: 'GET',
    });
  },

  getUserStores: async (credentials: { username: string; password: string }): Promise<ApiResponse<{ items: Store[] }>> => {
    return apiCall<ApiResponse<{ items: Store[] }>>(API_ENDPOINTS.AUTH.STORES, {
      method: 'POST',
      body: credentials,
    });
  },
};

// Member Service
export const memberService = {
  getMembers: async (params?: {
    name?: string;
    phone?: string;
    email?: string;
    membership_level?: string;
    status?: string;
    city?: string;
    state?: string;
    country?: string;
    min_total_spent?: number;
    min_visits?: number;
    page?: number;
    per_page?: number;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
  }): Promise<PaginatedApiResponse<Member>> => {
    return apiCall<PaginatedApiResponse<Member>>(API_ENDPOINTS.MEMBERS.BASE, {
      params,
    });
  },

  getMember: async (id: number): Promise<ApiResponse<{ member: Member }>> => {
    return apiCall<ApiResponse<{ member: Member }>>(API_ENDPOINTS.MEMBERS.DETAIL(id));
  },

  createMember: async (memberData: Partial<Member>): Promise<ApiResponse<{ member: Member }>> => {
    return apiCall<ApiResponse<{ member: Member }>>(API_ENDPOINTS.MEMBERS.BASE, {
      method: 'POST',
      body: memberData,
    });
  },

  updateMember: async (id: number, memberData: Partial<Member>): Promise<ApiResponse<{ member: Member }>> => {
    return apiCall<ApiResponse<{ member: Member }>>(API_ENDPOINTS.MEMBERS.DETAIL(id), {
      method: 'PUT',
      body: memberData,
    });
  },

  deleteMember: async (id: number): Promise<ApiResponse<void>> => {
    return apiCall<ApiResponse<void>>(API_ENDPOINTS.MEMBERS.DETAIL(id), {
      method: 'DELETE',
    });
  },
};

// Product Service
export const productService = {
  getProducts: async (params?: {
    name?: string;
    category?: string;
    min_price?: number;
    max_price?: number;
    sku?: string;
    barcode?: string;
    status?: string;
    page?: number;
    per_page?: number;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
  }): Promise<PaginatedApiResponse<Product>> => {
    return apiCall<PaginatedApiResponse<Product>>(API_ENDPOINTS.PRODUCTS.BASE, {
      params,
    });
  },

  getProduct: async (id: number): Promise<ApiResponse<{ product: Product }>> => {
    return apiCall<ApiResponse<{ product: Product }>>(API_ENDPOINTS.PRODUCTS.DETAIL(id));
  },

  createProduct: async (productData: Partial<Product>): Promise<ApiResponse<{ product: Product }>> => {
    return apiCall<ApiResponse<{ product: Product }>>(API_ENDPOINTS.PRODUCTS.BASE, {
      method: 'POST',
      body: productData,
    });
  },

  updateProduct: async (id: number, productData: Partial<Product>): Promise<ApiResponse<{ product: Product }>> => {
    return apiCall<ApiResponse<{ product: Product }>>(API_ENDPOINTS.PRODUCTS.DETAIL(id), {
      method: 'PUT',
      body: productData,
    });
  },

  deleteProduct: async (id: number): Promise<ApiResponse<void>> => {
    return apiCall<ApiResponse<void>>(API_ENDPOINTS.PRODUCTS.DETAIL(id), {
      method: 'DELETE',
    });
  },
};

// Service Service
export const serviceService = {
  getServices: async (params?: {
    name?: string;
    category?: string;
    subcategory?: string;
    min_price?: number;
    max_price?: number;
    min_duration?: number;
    max_duration?: number;
    status?: string;
    is_featured?: boolean;
    page?: number;
    per_page?: number;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
  }): Promise<PaginatedApiResponse<Service>> => {
    return apiCall<PaginatedApiResponse<Service>>(API_ENDPOINTS.SERVICES.BASE, {
      params,
    });
  },

  getService: async (id: number): Promise<ApiResponse<{ service: Service }>> => {
    return apiCall<ApiResponse<{ service: Service }>>(API_ENDPOINTS.SERVICES.DETAIL(id));
  },

  createService: async (serviceData: Partial<Service>): Promise<ApiResponse<{ service: Service }>> => {
    return apiCall<ApiResponse<{ service: Service }>>(API_ENDPOINTS.SERVICES.BASE, {
      method: 'POST',
      body: serviceData,
    });
  },

  updateService: async (id: number, serviceData: Partial<Service>): Promise<ApiResponse<{ service: Service }>> => {
    return apiCall<ApiResponse<{ service: Service }>>(API_ENDPOINTS.SERVICES.DETAIL(id), {
      method: 'PUT',
      body: serviceData,
    });
  },

  deleteService: async (id: number): Promise<ApiResponse<void>> => {
    return apiCall<ApiResponse<void>>(API_ENDPOINTS.SERVICES.DETAIL(id), {
      method: 'DELETE',
    });
  },
};

// Resource Service
export const resourceService = {
  getResources: async (params?: {
    name?: string;
    type?: string;
    status?: string;
    zone?: string;
    floor?: string;
    min_capacity?: number;
    max_capacity?: number;
    available?: boolean;
    page?: number;
    per_page?: number;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
  }): Promise<PaginatedApiResponse<Resource>> => {
    return apiCall<PaginatedApiResponse<Resource>>(API_ENDPOINTS.RESOURCES.BASE, {
      params,
    });
  },

  getResource: async (id: number): Promise<ApiResponse<{ resource: Resource }>> => {
    return apiCall<ApiResponse<{ resource: Resource }>>(API_ENDPOINTS.RESOURCES.DETAIL(id));
  },

  createResource: async (resourceData: Partial<Resource>): Promise<ApiResponse<{ resource: Resource }>> => {
    return apiCall<ApiResponse<{ resource: Resource }>>(API_ENDPOINTS.RESOURCES.BASE, {
      method: 'POST',
      body: resourceData,
    });
  },

  updateResource: async (id: number, resourceData: Partial<Resource>): Promise<ApiResponse<{ resource: Resource }>> => {
    return apiCall<ApiResponse<{ resource: Resource }>>(API_ENDPOINTS.RESOURCES.DETAIL(id), {
      method: 'PUT',
      body: resourceData,
    });
  },

  deleteResource: async (id: number): Promise<ApiResponse<void>> => {
    return apiCall<ApiResponse<void>>(API_ENDPOINTS.RESOURCES.DETAIL(id), {
      method: 'DELETE',
    });
  },
};

// Transaction Service
export const transactionService = {
  getTransactions: async (params?: {
    member_id?: number;
    user_id?: number;
    status?: string;
    payment_method?: string;
    min_amount?: number;
    max_amount?: number;
    start_date?: string;
    end_date?: string;
    transaction_number?: string;
    receipt_number?: string;
    source?: string;
    created_after?: string;
    created_before?: string;
    page?: number;
    per_page?: number;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
  }): Promise<PaginatedApiResponse<Transaction>> => {
    return apiCall<PaginatedApiResponse<Transaction>>(API_ENDPOINTS.TRANSACTIONS.BASE, {
      params,
    });
  },

  getTransaction: async (id: number): Promise<ApiResponse<{ transaction: Transaction }>> => {
    return apiCall<ApiResponse<{ transaction: Transaction }>>(API_ENDPOINTS.TRANSACTIONS.DETAIL(id));
  },

  createTransaction: async (transactionData: {
    member_id?: number | null;
    subtotal: number;
    tax_amount?: number;
    tax_rate?: number;
    discount_amount?: number;
    discount_rate?: number;
    total_amount: number;
    actual_amount?: number;
    payment_method: string;
    payment_reference?: string;
    notes?: string;
    sessions: Session[];
  }): Promise<ApiResponse<{ transaction: Transaction }>> => {
    return apiCall<ApiResponse<{ transaction: Transaction }>>(API_ENDPOINTS.TRANSACTIONS.BASE, {
      method: 'POST',
      body: transactionData,
    });
  },

  getTransactionItems: async (id: number): Promise<ApiResponse<{ count: number, items: TransactionItem[] }>> => {
    return apiCall<ApiResponse<{ count: number, items: TransactionItem[] }>>(API_ENDPOINTS.TRANSACTIONS.ITEMS(id));
  },

  addTransactionItem: async (id: number, itemData: {
    product_id?: number;
    service_id?: number;
    name: string;
    quantity: number;
    unit_price: number;
    total_price: number;
  }): Promise<ApiResponse<{ item: TransactionItem, updated_transaction: Transaction }>> => {
    return apiCall<ApiResponse<{ item: TransactionItem, updated_transaction: Transaction }>>(API_ENDPOINTS.TRANSACTIONS.ITEMS(id), {
      method: 'POST',
      body: itemData,
    });
  },

  removeTransactionItem: async (transactionId: number, itemId: number): Promise<ApiResponse<{ updated_transaction: Transaction }>> => {
    return apiCall<ApiResponse<{ updated_transaction: Transaction }>>(API_ENDPOINTS.TRANSACTIONS.ITEM_DETAIL(transactionId, itemId), {
      method: 'DELETE',
    });
  },

  completeTransaction: async (id: number): Promise<ApiResponse<{ transaction: Transaction }>> => {
    return apiCall<ApiResponse<{ transaction: Transaction }>>(API_ENDPOINTS.TRANSACTIONS.COMPLETE(id), {
      method: 'PUT',
    });
  },

  refundTransaction: async (id: number, refundData: { 
    refund_amount: number, 
    refund_reason: string, 
    create_refund_transaction?: boolean 
  }): Promise<ApiResponse<{ transaction: Transaction }>> => {
    return apiCall<ApiResponse<{ transaction: Transaction }>>(API_ENDPOINTS.TRANSACTIONS.REFUND(id), {
      method: 'PUT',
      body: refundData,
    });
  },

  cancelTransaction: async (id: number, reason: string): Promise<ApiResponse<{ transaction: Transaction }>> => {
    return apiCall<ApiResponse<{ transaction: Transaction }>>(API_ENDPOINTS.TRANSACTIONS.CANCEL(id), {
      method: 'PUT',
      body: { reason },
    });
  },
};

// Session Service
export const sessionService = {
  /**
   * Get all active sessions
   * GET /api/sessions
   */
  getSessions: async (params?: {
    user_id?: number;
    status?: string;
    member_id?: number;
    resource_id?: number;
    start_date?: string;
    end_date?: string;
    page?: number;
    per_page?: number;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
  }): Promise<PaginatedApiResponse<Session>> => {
    return apiCall<PaginatedApiResponse<Session>>(API_ENDPOINTS.SESSIONS.BASE, {
      params,
    });
  },

  /**
   * Get a specific session by ID
   * GET /api/sessions/{id}
   */
  getSession: async (id: number): Promise<ApiResponse<{ session: Session }>> => {
    return apiCall<ApiResponse<{ session: Session }>>(API_ENDPOINTS.SESSIONS.DETAIL(id));
  },

  /**
   * Create a new session
   * POST /api/sessions
   */
  createSession: async (sessionData: {
    user_id?: number;
    member_id?: number | null;
    resource_id: number;
    notes?: string;
  }): Promise<ApiResponse<{ session: Session }>> => {
    return apiCall<ApiResponse<{ session: Session }>>(API_ENDPOINTS.SESSIONS.BASE, {
      method: 'POST',
      body: sessionData,
    });
  },

  /**
   * Update a session
   * PUT /api/sessions/{id}
   */
  updateSession: async (id: number, sessionData: {
    member_id?: number | null;
    resource_id?: number | null;
    notes?: string;
  }): Promise<ApiResponse<{ session: Session }>> => {
    return apiCall<ApiResponse<{ session: Session }>>(API_ENDPOINTS.SESSIONS.DETAIL(id), {
      method: 'PUT',
      body: sessionData,
    });
  },

  /**
   * Close a session
   * PUT /api/sessions/{id}/close
   */
  closeSession: async (id: number, sessionData: {
    status: 'closed';
    notes?: string;
  }): Promise<ApiResponse<{ session: Session }>> => {
    return apiCall<ApiResponse<{ session: Session }>>(API_ENDPOINTS.SESSIONS.CLOSE(id), {
      method: 'PUT',
      body: sessionData,
    });
  },

  /**
   * Add a service to a session
   * POST /api/sessions/{id}/services
   */
  addServiceToSession: async (id: number, serviceData: {
    service_id: number;
    unit: string;
    quantity?: number;
    price: number;
  }): Promise<ApiResponse<{ session_service: { id: number, session_id: number, service_id: number } }>> => {
    return apiCall<ApiResponse<{ session_service: { id: number, session_id: number, service_id: number } }>>(API_ENDPOINTS.SESSIONS.SERVICES(id), {
      method: 'POST',
      body: serviceData,
    });
  },

  /**
   * Add a product to a session
   * POST /api/sessions/{id}/products
   */
  addProductToSession: async (id: number, productData: {
    product_id: number;
    quantity?: number;
    price: number;
  }): Promise<ApiResponse<{ session_product: { id: number, session_id: number, product_id: number } }>> => {
    return apiCall<ApiResponse<{ session_product: { id: number, session_id: number, product_id: number } }>>(API_ENDPOINTS.SESSIONS.PRODUCTS(id), {
      method: 'POST',
      body: productData,
    });
  },
};

// Report Service
export const reportService = {
  getSalesSummary: async (params: { start_date: string, end_date: string }): Promise<ApiResponse<{
    total_revenue: number;
    transaction_count: number;
    average_transaction_value: number;
    top_products: {
      product_id: number;
      name: string;
      quantity_sold: number;
      revenue: number;
    }[];
    period: {
      start_date: string;
      end_date: string;
    };
  }>> => {
    return apiCall<ApiResponse<{
      total_revenue: number;
      transaction_count: number;
      average_transaction_value: number;
      top_products: {
        product_id: number;
        name: string;
        quantity_sold: number;
        revenue: number;
      }[];
      period: {
        start_date: string;
        end_date: string;
      };
    }>>(API_ENDPOINTS.REPORTS.SALES_SUMMARY, {
      params,
    });
  },

  getSalesByCategory: async (params: { start_date: string, end_date: string }): Promise<ApiResponse<{
    categories: {
      category: string;
      total_amount: number;
      transaction_count: number;
      percentage: number;
    }[];
    filters_applied: {
      start_date: string;
      end_date: string;
    };
  }>> => {
    return apiCall<ApiResponse<{
      categories: {
        category: string;
        total_amount: number;
        transaction_count: number;
        percentage: number;
      }[];
      filters_applied: {
        start_date: string;
        end_date: string;
      };
    }>>(API_ENDPOINTS.REPORTS.SALES_BY_CATEGORY, {
      params,
    });
  },

  getProductPerformance: async (params: { 
    start_date: string, 
    end_date: string, 
    category?: string, 
    limit?: number 
  }): Promise<ApiResponse<{
    top_products: {
      id: number;
      name: string;
      category: string;
      quantity_sold: number;
      revenue: number;
      stock_level: number;
      stock_status: string;
    }[];
    low_stock_products: {
      id: number;
      name: string;
      category: string;
      stock_level: number;
      stock_status: string;
    }[];
    filters_applied: {
      start_date: string;
      end_date: string;
      category?: string;
      limit?: number;
    };
  }>> => {
    return apiCall<ApiResponse<{
      top_products: {
        id: number;
        name: string;
        category: string;
        quantity_sold: number;
        revenue: number;
        stock_level: number;
        stock_status: string;
      }[];
      low_stock_products: {
        id: number;
        name: string;
        category: string;
        stock_level: number;
        stock_status: string;
      }[];
      filters_applied: {
        start_date: string;
        end_date: string;
        category?: string;
        limit?: number;
      };
    }>>(API_ENDPOINTS.REPORTS.PRODUCT_PERFORMANCE, {
      params,
    });
  },

  getResourceUtilization: async (params: { 
    start_date: string, 
    end_date: string, 
    resource_type?: string 
  }): Promise<ApiResponse<{
    resource_stats: {
      resource_id: number;
      resource_name: string;
      total_sessions: number;
      total_hours: number;
      occupancy_rate: number;
      revenue: number;
    }[];
    usage_patterns: {
      peak_hours: string[];
      popular_days: string[];
    };
  }>> => {
    return apiCall<ApiResponse<{
      resource_stats: {
        resource_id: number;
        resource_name: string;
        total_sessions: number;
        total_hours: number;
        occupancy_rate: number;
        revenue: number;
      }[];
      usage_patterns: {
        peak_hours: string[];
        popular_days: string[];
      };
    }>>(API_ENDPOINTS.REPORTS.RESOURCE_UTILIZATION, {
      params,
    });
  },

  getDashboardSummary: async (params?: { date?: string }): Promise<ApiResponse<{
    daily_sales: number;
    monthly_revenue: number;
    sales_growth: number;
    orders_today: number;
    members_today: number;
    resource_utilization: number;
    recent_transactions: {
      id: number;
      member: string;
      amount: number;
      time: string;
    }[];
    date: string;
  }>> => {
    return apiCall<ApiResponse<{
      daily_sales: number;
      monthly_revenue: number;
      sales_growth: number;
      orders_today: number;
      members_today: number;
      resource_utilization: number;
      recent_transactions: {
        id: number;
        member: string;
        amount: number;
        time: string;
      }[];
      date: string;
    }>>(API_ENDPOINTS.REPORTS.DASHBOARD_SUMMARY, {
      params,
    });
  },

  getSalesByPaymentMethod: async (params: { start_date: string, end_date: string }): Promise<ApiResponse<{
    payment_methods: {
      method: string;
      transaction_count: number;
      total_amount: number;
      percentage: number;
    }[];
    filters_applied: {
      start_date: string;
      end_date: string;
    };
  }>> => {
    return apiCall<ApiResponse<{
      payment_methods: {
        method: string;
        transaction_count: number;
        total_amount: number;
        percentage: number;
      }[];
      filters_applied: {
        start_date: string;
        end_date: string;
      };
    }>>(API_ENDPOINTS.REPORTS.SALES_BY_PAYMENT_METHOD, {
      params,
    });
  },

  getMemberActivity: async (params: { 
    start_date: string, 
    end_date: string,
    limit?: number,
    page?: number,
    per_page?: number
  }): Promise<ApiResponse<{
    members: {
      id: number;
      name: string;
      email: string;
      visits: number;
      total_spent: number;
      last_visit: string;
    }[];
    total_count: number;
    page: number;
    per_page: number;
    filters_applied: {
      start_date: string;
      end_date: string;
    };
  }>> => {
    return apiCall<ApiResponse<{
      members: {
        id: number;
        name: string;
        email: string;
        visits: number;
        total_spent: number;
        last_visit: string;
      }[];
      total_count: number;
      page: number;
      per_page: number;
      filters_applied: {
        start_date: string;
        end_date: string;
      };
    }>>(API_ENDPOINTS.REPORTS.MEMBER_ACTIVITY, {
      params,
    });
  },
};

// Export all services
export const apiService = {
  auth: authService,
  members: memberService,
  products: productService,
  services: serviceService,
  resources: resourceService,
  transactions: transactionService,
  sessions: sessionService,
  reports: reportService,
};

export default apiService;
