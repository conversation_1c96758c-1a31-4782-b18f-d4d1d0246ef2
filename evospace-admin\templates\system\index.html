<!DOCTYPE html>
<html>
<head>
    <title>Pear Admin Flask</title>
    <meta charset="utf-8">
    {% include 'system/common/header.html' %}
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
</head>
<!-- 结 构 代 码 -->
<body class="layui-layout-body pear-admin">
<!-- 布 局 框 架 -->
<div class="layui-layout layui-layout-admin">
    <!-- 顶 部 样 式 -->
    <div class="layui-header">
        <!-- 菜 单 顶 部 -->
        <div class="layui-logo">
            <!-- 图 标 -->
            <img class="logo">
            <!-- 标 题 -->
            <span class="title"></span>
        </div>
        <!-- 顶 部 左 侧 功 能 -->
        <ul class="layui-nav layui-layout-left">
            <li class="collapse layui-nav-item"><a href="#" class="layui-icon layui-icon-shrink-right"></a></li>
            <li class="refresh layui-nav-item"><a href="#" class="layui-icon layui-icon-refresh-1" loading=600></a></li>
        </ul>
        <!-- 多 系 统 菜 单 -->
        <div id="control" class="layui-layout-control"></div>
        <!-- 顶 部 右 侧 菜 单 -->
        <ul class="layui-nav layui-layout-right">
            <li class="layui-nav-item layui-hide-xs"><a href="#" class="menuSearch layui-icon layui-icon-search"></a>
            </li>
            <li class="layui-nav-item layui-hide-xs message"></li>
            <li class="layui-nav-item layui-hide-xs"><a href="#"
                                                        class="fullScreen layui-icon layui-icon-screen-full"></a></li>
            <li class="layui-nav-item user">
                <!-- 头 像 -->
                <a class="layui-icon layui-icon-username" href="javascript:;"></a>
                <!-- 功 能 菜 单 -->
                <dl class="layui-nav-child">
                    <dd><a href="javascript:void(0);" user-menu-url="/system/user/center" user-menu-id="5555"
                           user-menu-title="基本资料">基本资料</a></dd>
                    <dd><a href="javascript:void(0);" class="logout">注销登录</a></dd>
                </dl>
            </li>
            <!-- 主 题 配 置 -->
            <li class="layui-nav-item setting"><a href="#" class="layui-icon layui-icon-more-vertical"></a></li>
        </ul>
    </div>
    <!-- 侧 边 区 域 -->
    <div class="layui-side layui-bg-black">
        <!-- 菜 单 顶 部 -->
        <div class="layui-logo">
            <!-- 图 标 -->
            <img class="logo">
            <!-- 标 题 -->
            <span class="title"></span>
        </div>
        <!-- 菜 单 内 容 -->
        <div class="layui-side-scroll">
            <div id="side"></div>
        </div>
    </div>
    <!-- 视 图 页 面 -->
    <div class="layui-body">
        <!-- 内 容 页 面 -->
        <div id="content"></div>
    </div>
    <!-- 页脚 -->
    <div class="layui-footer layui-text">
				<span class="left">
					<span>
						官网：<a href="http://www.pearadmin.com/team.html" target="_blank">www.pearadmin.com</a>&nbsp;&nbsp;&nbsp;
						仓库：<a href="https://gitee.com/pear-admin/Pear-Admin-Layui" target="_blank">gitee.com/pear-admin</a>
					</span>
				</span>
        <span class="center"></span>
        <span class="right"></span>
    </div>
    <!-- 遮 盖 层 -->
    <div class="pear-cover"></div>
    <!-- 加 载 动 画 -->
    <div class="loader-wrapper">
        <!-- 动 画 对 象 -->
        <div class="loader"></div>
    </div>
</div>
<!-- 移 动 端 便 捷 操 作 -->
<div class="pear-collapsed-pe collapse">
    <a href="#" class="layui-icon layui-icon-shrink-right"></a>
</div>
<!-- 依 赖 脚 本 -->
<script src="{{ url_for('static', filename='system/component/layui/layui.js') }}"></script>
<script src="{{ url_for('static', filename='system/component/pear/pear.js') }}"></script>
<!-- 框 架 初 始 化 -->
<script>
    layui.use(['admin', 'jquery', 'popup'], function () {
        var admin = layui.admin;
        var popup = layui.popup;
        var $ = layui.jquery;

        // yml | json | api
        admin.setConfigurationPath("{{ url_for('system.rights.configs') }}");

        // 主题色自动通知子页面
        admin._changeTheme = admin.changeTheme;
        admin.changeTheme = function () {
            admin._changeTheme();  // 调用原方法

            /* 遍历所有 iframe 更改主题色 */
            const variableKey = "--global-primary-color";
            const variableVal = localStorage.getItem("theme-color-color");
            const iframes = document.querySelectorAll('iframe');
            iframes.forEach(function (iframe) {
                try {
                    const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
                    // 设置 iframe 中的主题色
                    iframeDocument.documentElement.style.setProperty(variableKey, variableVal);
                } catch (e) {
                }
            });
        }

        // 夜间模式切换通知子页面
        admin._switchTheme = admin.switchTheme
        admin.switchTheme = function (checked) {
            admin.isdrak = checked;
            admin._switchTheme(checked)

            const iframes = document.querySelectorAll('iframe');
            iframes.forEach(function (iframe) {
                try {
                    const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;

                    if (checked === true || checked === "true") {
                        iframeDocument.body.classList.add("pear-admin-dark");
                    } else {
                        iframeDocument.body.classList.remove("pear-admin-dark");
                    }
                } catch (e) {
                }
            });
        }

        // 渲染
        admin.render();

        // 注销
        admin.logout(function () {
            let loading = layer.load()
            $.ajax({
                url: '{{ url_for('system.passport.logout') }}',
                dataType: 'json',
                async: false,
                type: 'post',
                success: function (result) {
                    layer.close(loading)
                    if (result.success) {
                        popup.success(result.msg, function () {
                            location.href = '/'
                        })
                        return true
                    }
                }
            })
        })
    })
</script>
</body>
</html>