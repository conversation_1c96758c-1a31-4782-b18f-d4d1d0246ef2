"use client";

import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  List,
  ListItemButton,
  ListItemText,
  Avatar,
  Chip,
  Button,
  InputAdornment
} from '@mui/material';
import { Search as SearchIcon } from '@mui/icons-material';
import type { Member } from '@/lib/types';

interface MemberSelectionDialogProps {
  open: boolean;
  onClose: () => void;
  members: Member[];
  onSelectMember: (member: Member) => void;
  onSelectWalkIn: () => void; 
  currentMemberId?: number | string | null;
}

const MemberSelectionDialog: React.FC<MemberSelectionDialogProps> = ({
  open,
  onClose,
  members,
  onSelectMember,
  onSelectWalkIn,
  currentMemberId
}) => {
  // TODO: Implement search functionality if needed
  // const [searchTerm, setSearchTerm] = useState('');
  // const filteredMembers = members.filter(member => 
  //   member.name.toLowerCase().includes(searchTerm.toLowerCase())
  // );

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>Select Member</DialogTitle>
      <DialogContent>
        <TextField
          autoFocus
          margin="dense"
          label="Search Members"
          type="text"
          fullWidth
          variant="outlined"
          // value={searchTerm}
          // onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ mb: 2 }}
        />
        <List sx={{ pt: 0 }}>
          <ListItemButton 
            onClick={onSelectWalkIn} 
            selected={currentMemberId === null}
          >
            <ListItemText primary="No Member (Walk-in)" />
          </ListItemButton>
          {members.map((member) => (
            <ListItemButton 
              onClick={() => onSelectMember(member)} 
              key={member.id} 
              selected={currentMemberId === member.id}
            >
              <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                {member.name.charAt(0)}
              </Avatar>
              <ListItemText 
                primary={member.name} 
                secondary={member.email}
              />
              {member.visits !== undefined && member.visits > 0 && (
                <Chip 
                  label={`Visits: ${member.visits}`}
                  size="small" 
                  sx={{ mr: 1 }} 
                />
              )}
            </ListItemButton>
          ))}
        </List>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
      </DialogActions>
    </Dialog>
  );
};

export default MemberSelectionDialog;
