﻿/** EasyWeb site v2.0.0 date:2019-10-01 License By http://easyweb.vip */
layui.define(["form"],function(a){var d=layui.jquery;var b=layui.form;var e={psw:function(g,f){if(g&&!/^[\S]{5,12}$/.test(g)){return"密码必须5到12位，且不能出现空格"}},repeat:function(h,g){if(h!=d(d(g).data("target")).val()){var f=d(g).data("tips");return f?f:"两次输入不一致"}}};var c={init:function(){b.verify(e)},formVal:function(g,f){d('.layui-form[lay-filter="'+g+'"]').each(function(){var i=d(this);for(var k in f){var h=i.find('[name="'+k+'"]');if(h.length>0){var j=h[0].type;if(j=="checkbox"){h[0].checked=value}else{if(j=="radio"){h.each(function(){if(this.value==f[k]){this.checked=true}})}else{h.val(f[k])}}}}});b.render(null,g)}};c.init();a("formX",c)});