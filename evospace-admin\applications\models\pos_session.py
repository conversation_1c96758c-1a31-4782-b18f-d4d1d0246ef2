from applications.extensions import db
from sqlalchemy.dialects.mysql import INTEGER
import datetime

class PosSession(db.Model):
    __tablename__ = 'pos_session'

    id = db.Column(INTEGER(unsigned=True), primary_key=True, autoincrement=True)
    user_id = db.Column(INTEGER(unsigned=True), db.<PERSON>('admin_user.id'), nullable=False)  # Assuming 'admin_user' is the table for staff/users
    member_id = db.Column(INTEGER(unsigned=True), db.<PERSON><PERSON><PERSON>('pos_member.id'), nullable=True)  # Optional: session might not be linked to a member
    resource_id = db.Column(INTEGER(unsigned=True), db.<PERSON><PERSON>ey('pos_resource.id'), nullable=True)  # Optional: session might not be linked to a resource
    
    # Store isolation - Multi-store support
    store_id = db.Column(db.Integer, db.ForeignKey('admin_dept.id'), nullable=False)
    store = db.relationship('Dept', backref='pos_sessions')
    
    notes = db.Column(db.String(255), nullable=True)
    start_time = db.Column(db.DateTime, default=datetime.datetime.now)
    end_time = db.Column(db.DateTime, nullable=True)  # Nullable until the session is closed
    status = db.Column(db.String(50), nullable=False, default='open')  # e.g., 'active', 'completed', 'cancelled', 'open', 'closed'
    create_at = db.Column(db.DateTime, default=datetime.datetime.now)
    update_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    # Relationships to session_services and session_products
    session_services = db.relationship('PosSessionService', backref='session', lazy='dynamic', cascade='all, delete-orphan')
    session_products = db.relationship('PosSessionProduct', backref='session', lazy='dynamic', cascade='all, delete-orphan')
    
    # Direct relationships to services and products through the join tables
    user = db.relationship('User', backref=db.backref('sessions', lazy=True))  # Assuming 'admin_user' is the table for staff/users
    member = db.relationship('PosMember', backref=db.backref('sessions', lazy=True))
    resource = db.relationship('PosResource', backref=db.backref('sessions', lazy=True))  # Assuming 'pos_resource' is the table for resources
   

class PosSessionService(db.Model):
    __tablename__ = 'pos_session_service'

    id = db.Column(INTEGER(unsigned=True), primary_key=True, autoincrement=True)
    session_id = db.Column(INTEGER(unsigned=True), db.ForeignKey('pos_session.id'), nullable=False)
    service_id = db.Column(INTEGER(unsigned=True), db.ForeignKey('pos_service.id'), nullable=False)
    unit = db.Column(db.String(50), nullable=False)  # e.g., 'hour', 'session'
    quantity = db.Column(db.Integer, default=1)  # Default quantity is 1
    price = db.Column(db.Numeric(10, 2), nullable=False)  # Price of the service
    start_time = db.Column(db.DateTime, default=datetime.datetime.now)
    end_time = db.Column(db.DateTime, nullable=True)  # Nullable until the service is completed
    create_at = db.Column(db.DateTime, default=datetime.datetime.now)
    update_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)
    
    # Direct relationship to the service
    service = db.relationship('PosService', backref=db.backref('session_services', lazy='dynamic'))


class PosSessionProduct(db.Model):
    __tablename__ = 'pos_session_product'

    id = db.Column(INTEGER(unsigned=True), primary_key=True, autoincrement=True)
    session_id = db.Column(INTEGER(unsigned=True), db.ForeignKey('pos_session.id'), nullable=False)
    product_id = db.Column(INTEGER(unsigned=True), db.ForeignKey('pos_product.id'), nullable=False)
    quantity = db.Column(db.Integer, default=1)  # Default quantity is 1
    price = db.Column(db.Numeric(10, 2), nullable=False)  # Price of the product
    create_at = db.Column(db.DateTime, default=datetime.datetime.now)
    update_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)
    
    # Direct relationship to the product
    product = db.relationship('PosProduct', backref=db.backref('session_products', lazy='dynamic'))
