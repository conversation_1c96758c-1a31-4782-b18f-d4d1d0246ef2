from applications.extensions import db
from sqlalchemy.dialects.mysql import INTEGER
import datetime

class PosResource(db.Model):
    __tablename__ = 'pos_resource'

    id = db.Column(INTEGER(unsigned=True), primary_key=True, autoincrement=True)
    name = db.Column(db.String(255), nullable=False)
    type = db.Column(db.String(100), nullable=True)  # e.g., 'room', 'desk', 'equipment'
    capacity = db.Column(db.Integer, nullable=True)
    hourly_rate = db.Column(db.Numeric(10, 2), nullable=True)
    
    # Store isolation - Multi-store support
    store_id = db.Column(db.Integer, db.<PERSON>ey('admin_dept.id'), nullable=False)
    store = db.relationship('Dept', backref='pos_resources')
    # Status based on SYSTEM_DESIGN.md: 'available', 'in-use', 'booked', 'maintenance'
    status = db.Column(db.String(50), nullable=False, default='available') 
    x = db.Column(db.Integer, nullable=True) # For layout/map purposes
    y = db.Column(db.Integer, nullable=True)
    width = db.Column(db.Integer, nullable=True)
    height = db.Column(db.Integer, nullable=True)
    floor = db.Column(db.Integer, nullable=True)
    zone = db.Column(db.String(100), nullable=True) # e.g., 'Quiet Zone', 'Collaborative Area'
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)
