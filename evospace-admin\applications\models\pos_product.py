from applications.extensions import db
from sqlalchemy.dialects.mysql import INTEGER
import datetime

class PosProduct(db.Model):
    __tablename__ = 'pos_product'

    id = db.Column(INTEGER(unsigned=True), primary_key=True, autoincrement=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text, nullable=True)
    
    # Store isolation - Multi-store support
    store_id = db.Column(db.Integer, db.ForeignKey('admin_dept.id'), nullable=False)
    store = db.relationship('Dept', backref='pos_products')
    
    # Product identification
    sku = db.Column(db.String(50), nullable=True, unique=True)  # Stock Keeping Unit
    barcode = db.Column(db.String(50), nullable=True, unique=True)  # UPC, EAN, etc.
    
    # Categorization
    category = db.Column(db.String(100), nullable=True)
    subcategory = db.Column(db.String(100), nullable=True)
    brand = db.Column(db.String(100), nullable=True)
    
    # Pricing
    price = db.Column(db.Numeric(10, 2), nullable=False)
    cost_price = db.Column(db.Numeric(10, 2), nullable=True)  # For profit margin calculation
    discount_price = db.Column(db.Numeric(10, 2), nullable=True)  # Sale price
    tax_rate = db.Column(db.Numeric(5, 2), nullable=True)  # Percentage
    
    # Inventory management
    stock = db.Column(db.Integer, nullable=False, default=0)
    min_stock = db.Column(db.Integer, nullable=True)  # Reorder threshold
    max_stock = db.Column(db.Integer, nullable=True)  # Maximum inventory level
    reorder_quantity = db.Column(db.Integer, nullable=True)  # How many to order when restocking
    last_restock_date = db.Column(db.DateTime, nullable=True)
    
    # Physical attributes
    weight = db.Column(db.Numeric(8, 2), nullable=True)  # In grams or kg
    width = db.Column(db.Numeric(8, 2), nullable=True)  # In cm
    height = db.Column(db.Numeric(8, 2), nullable=True)  # In cm
    depth = db.Column(db.Numeric(8, 2), nullable=True)  # In cm
    
    # Media
    image_url = db.Column(db.String(512), nullable=True)
    additional_images = db.Column(db.Text, nullable=True)  # JSON array of image URLs
    
    # Supplier information
    supplier_id = db.Column(INTEGER(unsigned=True), nullable=True)  # Could be a foreign key to a suppliers table
    supplier_name = db.Column(db.String(255), nullable=True)
    supplier_code = db.Column(db.String(50), nullable=True)  # Supplier's product code
    
    # System fields
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)
    status = db.Column(db.String(50), nullable=False, default='active')  # active, inactive, discontinued