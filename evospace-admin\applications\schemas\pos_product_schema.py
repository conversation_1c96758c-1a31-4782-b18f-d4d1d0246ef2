from applications.extensions import ma
from applications.models.pos_product import PosProduct

class PosProductSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = PosProduct
        load_instance = True  # Optional: deserialize to model instances
        # fields = ('id', 'name', 'category', 'price', 'stock', 'image_url', 'created_at', 'updated_at') # Or specify fields explicitly
        # dump_only = ('id', 'created_at', 'updated_at') # Fields to include only during serialization

    id = ma.auto_field(dump_only=True)
    name = ma.auto_field(required=True)
    category = ma.auto_field()
    price = ma.auto_field(required=True)
    stock = ma.auto_field(required=True)
    image_url = ma.auto_field(data_key="image")
    created_at = ma.auto_field(dump_only=True)
    updated_at = ma.auto_field(dump_only=True)
