'use client';

import { useState, FormEvent, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import usePosStore from '@/lib/store';
import apiService from '@/lib/apiService';
import CircularProgress from '@mui/material/CircularProgress';
import Fade from '@mui/material/Fade';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
import MenuItem from '@mui/material/MenuItem';
import PersonOutline from '@mui/icons-material/PersonOutline';
import LockOutlined from '@mui/icons-material/LockOutlined';
import StoreIcon from '@mui/icons-material/Store';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import type { Store } from '@/lib/types';

export default function LoginPage() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [selectedStore, setSelectedStore] = useState<number | ''>('');
  const [errorMessage, setErrorMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showStoreSelection, setShowStoreSelection] = useState(false);
  const [availableStores, setAvailableStores] = useState<Store[]>([]);
  const router = useRouter();
  const { login, loadAuthToken, authUser } = usePosStore();

  const handleInitialLogin = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsLoading(true);
    setErrorMessage('');

    try {
      // First, validate credentials and get available stores for this user
      const response = await apiService.auth.getUserStores({ username, password });
      
      if (!response.success) {
        setErrorMessage('Login failed. Please check your credentials.');
        setIsLoading(false);
        return;
      }

      const stores = response.data.items;
      setAvailableStores(stores);

      // If user has only one store, auto-login
      if (stores.length === 1) {
        const success = await login(username, password, stores[0].id);
        if (!success) {
          setErrorMessage('Login failed. Please check your credentials.');
        } else {
          router.push('/');
        }
      } else if (stores.length > 1) {
        // Show store selection for multiple stores and set default to first store
        setSelectedStore(stores[0].id);
        setShowStoreSelection(true);
      } else {
        setErrorMessage('No stores available for this user.');
      }
    } catch (error) {
      console.error('Login error:', error);
      setErrorMessage('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStoreSelection = async () => {
    if (!selectedStore) {
      setErrorMessage('Please select a store.');
      return;
    }

    setIsLoading(true);
    setErrorMessage('');

    try {
      const success = await login(username, password, Number(selectedStore));
      if (!success) {
        setErrorMessage('Login failed. Please check your credentials and store selection.');
      } else {
        router.push('/');
      }
    } catch (error) {
      console.error('Login error:', error);
      setErrorMessage('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToCredentials = () => {
    setShowStoreSelection(false);
    setSelectedStore('');
    setAvailableStores([]);
    setErrorMessage('');
  };

  // Load token on component mount
  useEffect(() => {
    loadAuthToken();
  }, [loadAuthToken]);

  // Redirect if user is already authenticated
  useEffect(() => {
    if (authUser.token) {
      router.push('/');
    }
  }, [authUser.token, router]);

  // If user is already authenticated (token exists), show loading/redirect indicator
  if (authUser.token) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-slate-900 to-slate-700"> 
        <CircularProgress color="inherit" /> 
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-slate-900 to-slate-700 p-4"> 
      <Fade in={true} timeout={700}>
        <div className="w-full max-w-md p-8 space-y-6 bg-white rounded-xl shadow-2xl"> 
          <h2 className="text-3xl font-bold text-center text-slate-700"> 
            EvoSpace POS Login
          </h2>
          <form onSubmit={handleInitialLogin} className="space-y-6">
            {!showStoreSelection ? (
              // Step 1: Username/Password
              <>
                <div>
                  <TextField
                    fullWidth
                    id="username"
                    label="Username"
                    variant="outlined"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    required
                    placeholder="admin"
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <PersonOutline />
                        </InputAdornment>
                      ),
                    }}
                  />
                </div>
                <div>
                  <TextField
                    fullWidth
                    type="password"
                    id="password"
                    label="Password"
                    variant="outlined"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    placeholder="123456"
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <LockOutlined />
                        </InputAdornment>
                      ),
                    }}
                  />
                </div>
                {errorMessage && (
                  <p className="text-sm text-red-600 text-center">{errorMessage}</p> 
                )}
                <div>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="w-full px-4 py-3 font-semibold text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-60 transition-colors duration-300 ease-in-out"
                  >
                    {isLoading ? (
                      <CircularProgress size={24} color="inherit" />
                    ) : (
                      'Continue'
                    )}
                  </button>
                </div>
              </>
            ) : (
              // Step 2: Store Selection
              <>
                <div className="text-sm text-gray-600 text-center mb-4">
                  Logged in as: <span className="font-medium">{username}</span>
                </div>
                <div>
                  <TextField
                    fullWidth
                    select
                    id="store"
                    label="Select Store"
                    variant="outlined"
                    value={selectedStore}
                    onChange={(e) => setSelectedStore(Number(e.target.value))}
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <StoreIcon />
                        </InputAdornment>
                      ),
                    }}
                    helperText="Choose your store location"
                  >
                    {availableStores.map((store) => (
                      <MenuItem key={store.id} value={store.id}>
                        <div>
                          <div className="font-medium">{store.name}</div>
                          {store.address && (
                            <div className="text-sm text-gray-500">{store.address}</div>
                          )}
                        </div>
                      </MenuItem>
                    ))}
                  </TextField>
                </div>
                {errorMessage && (
                  <p className="text-sm text-red-600 text-center">{errorMessage}</p>
                )}
                <div>
                  <button
                    type="button"
                    onClick={handleStoreSelection}
                    disabled={isLoading || !selectedStore}
                    className="w-full px-4 py-3 font-semibold text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-60 transition-colors duration-300 ease-in-out"
                  >
                    {isLoading ? (
                      <CircularProgress size={24} color="inherit" />
                    ) : (
                      'Login to Store'
                    )}
                  </button>
                </div>
                <div className="flex justify-center mt-4">
                  <button
                    type="button"
                    onClick={handleBackToCredentials}
                    className="px-4 py-2 font-medium text-indigo-600 bg-white border border-indigo-600 rounded-lg hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-300 ease-in-out flex items-center space-x-2"
                  >
                    <ArrowBackIcon className="w-4 h-4" />
                    <span>Back</span>
                  </button>
                </div>
              </>
            )}
          </form>
        </div>
      </Fade>
    </div>
  );
}
