from flask import Blueprint, request, jsonify, g, url_for
from applications.extensions import db, ma
from applications.models.pos_product import PosProduct
from applications.schemas.pos_product_schema import PosProductSchema
from applications.api.auth_api import token_required, store_context_required
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import joinedload
from marshmallow import ValidationError
from datetime import datetime
import logging

# Set up logger
logger = logging.getLogger(__name__)

product_api_bp = Blueprint('product_api', __name__, url_prefix='/api/products')

# Create schemas
product_schema = PosProductSchema()
products_schema = PosProductSchema(many=True)

@product_api_bp.route('', methods=['POST'])
@token_required
@store_context_required
def create_product():
    """Create a new product
    
    Request Body:
    - name: Required, product name
    - description: Optional, product description
    - price: Required, product price
    - stock: Optional, current stock level
    - category: Optional, product category
    - sku: Optional, stock keeping unit
    - barcode: Optional, product barcode
    - and other fields as defined in the PosProduct model
    """
    try:
        # Validate request data
        json_data = request.get_json()
        if not json_data:
            return jsonify({
                'success': False,
                'message': 'No input data provided'
            }), 400
        
        # Validate and deserialize input
        try:
            data = product_schema.load(json_data)
        except ValidationError as err:
            return jsonify({
                'success': False,
                'message': 'Validation error',
                'errors': err.messages
            }), 400
        
        # Create new product and assign store_id
        product = data
        product.store_id = g.store_id  # Set store context from JWT token
        db.session.add(product)
        db.session.commit()
        
        # Log the product creation
        logger.info(f"Product created: ID={product.id}, Name={product.name}, Price={product.price}")
        
        return jsonify({
            'success': True,
            'message': 'Product created successfully',
            'data': {
                'product': product_schema.dump(product)
            }
        }), 201
        
    except ValidationError as err:
        return jsonify({'message': 'Validation error', 'errors': err.messages}), 400
    except SQLAlchemyError as e:
        db.session.rollback()
        return jsonify({'message': 'Database error', 'error': str(e)}), 500

@product_api_bp.route('', methods=['GET'])
@token_required
@store_context_required
def get_products():
    """Get all products with optional filtering and pagination
    
    Query Parameters:
    - name: Filter by product name (partial match)
    - category: Filter by product category (partial match)
    - min_price: Filter by minimum price
    - max_price: Filter by maximum price
    - sku: Filter by SKU (exact match)
    - barcode: Filter by barcode (exact match)
    - status: Filter by status
    - page: Page number (default: 1)
    - per_page: Items per page (default: 20, max: 100)
    - sort_by: Field to sort by (default: name)
    - sort_order: Sort order ('asc' or 'desc', default: 'asc')
    """
    try:
        # Get query parameters for filtering
        name = request.args.get('name')
        category = request.args.get('category')
        min_price = request.args.get('min_price', type=float)
        max_price = request.args.get('max_price', type=float)
        sku = request.args.get('sku')
        barcode = request.args.get('barcode')
        status = request.args.get('status')
        
        # Pagination parameters
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)  # Limit max per_page to 100
        
        # Sorting parameters
        sort_by = request.args.get('sort_by', 'name')
        sort_order = request.args.get('sort_order', 'asc')
        
        # Validate sort parameters
        valid_sort_fields = ['id', 'name', 'price', 'stock', 'category', 'created_at', 'updated_at']
        if sort_by not in valid_sort_fields:
            return jsonify({
                'success': False,
                'message': f'Invalid sort_by parameter. Valid options are: {valid_sort_fields}'
            }), 400
            
        if sort_order not in ['asc', 'desc']:
            return jsonify({
                'success': False,
                'message': 'Invalid sort_order parameter. Valid options are: asc, desc'
            }), 400
        
        # Build query with filters (filter by store first)
        query = PosProduct.query.filter(PosProduct.store_id == g.store_id)
        
        if name:
            query = query.filter(PosProduct.name.ilike(f'%{name}%'))
        if category:
            query = query.filter(PosProduct.category.ilike(f'%{category}%'))
        if min_price is not None:
            query = query.filter(PosProduct.price >= min_price)
        if max_price is not None:
            query = query.filter(PosProduct.price <= max_price)
        if sku:
            query = query.filter(PosProduct.sku == sku)
        if barcode:
            query = query.filter(PosProduct.barcode == barcode)
        if status:
            query = query.filter(PosProduct.status == status)
        
        # Apply sorting
        sort_column = getattr(PosProduct, sort_by)
        if sort_order == 'desc':
            query = query.order_by(sort_column.desc())
        else:
            query = query.order_by(sort_column.asc())
        
        # Get paginated results
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        products = pagination.items
        
        # Log the query for monitoring
        logger.info(f"Products query: page={page}, per_page={per_page}, filters applied: {request.args}")
        
        # Build pagination metadata
        meta = {
            'page': page,
            'per_page': per_page,
            'total_pages': pagination.pages,
            'total_items': pagination.total
        }
        
        # Add pagination links
        links = {}
        if pagination.has_prev:
            links['prev'] = url_for('product_api.get_products', page=page-1, per_page=per_page, **request.args)
        if pagination.has_next:
            links['next'] = url_for('product_api.get_products', page=page+1, per_page=per_page, **request.args)
        links['first'] = url_for('product_api.get_products', page=1, per_page=per_page, **request.args)
        links['last'] = url_for('product_api.get_products', page=pagination.pages, per_page=per_page, **request.args)
        
        return jsonify({
            'success': True,
            'data': {
                'items': products_schema.dump(products),
                'meta': meta,
                'links': links
            }
        }), 200
        
    except SQLAlchemyError as e:
        return jsonify({'message': 'Database error', 'error': str(e)}), 500

@product_api_bp.route('/<int:id>', methods=['GET'])
@token_required
@store_context_required
def get_product(id):
    """Get a specific product by ID"""
    try:
        product = PosProduct.query.filter(
            PosProduct.id == id,
            PosProduct.store_id == g.store_id
        ).first()
        
        if not product:
            return jsonify({
                'success': False,
                'message': 'Product not found'
            }), 404
        
        return jsonify({
            'success': True,
            'data': {
                'product': product_schema.dump(product)
            }
        }), 200
        
    except SQLAlchemyError as e:
        return jsonify({'message': 'Database error', 'error': str(e)}), 500

@product_api_bp.route('/<int:id>', methods=['PUT'])
@token_required
@store_context_required
def update_product(id):
    """Update a specific product"""
    try:
        # Find the product (within the current store context)
        product = PosProduct.query.filter(
            PosProduct.id == id,
            PosProduct.store_id == g.store_id
        ).first()
        
        if not product:
            return jsonify({
                'success': False,
                'message': 'Product not found'
            }), 404
        
        # Validate request data
        json_data = request.get_json()
        if not json_data:
            return jsonify({
                'success': False,
                'message': 'No input data provided'
            }), 400
        
        # Validate and deserialize input
        try:
            # Load the data into a new PosProduct instance
            updated_product = product_schema.load(json_data, partial=True)
            
            # Get the fields that were provided in the request
            provided_fields = json_data.keys()
            
            # Update only the fields that were provided in the request
            for field in provided_fields:
                if hasattr(updated_product, field):
                    setattr(product, field, getattr(updated_product, field))
        except ValidationError as err:
            return jsonify({
                'success': False,
                'message': 'Validation error',
                'errors': err.messages
            }), 400
        
        # Set updated_at timestamp
        product.updated_at = datetime.now()
        
        db.session.commit()
        
        # Log the product update
        logger.info(f"Product updated: ID={product.id}, Name={product.name}")
        
        return jsonify({
            'success': True,
            'message': 'Product updated successfully',
            'data': {
                'product': product_schema.dump(product)
            }
        }), 200
        
    except ValidationError as err:
        return jsonify({'message': 'Validation error', 'errors': err.messages}), 400
    except SQLAlchemyError as e:
        db.session.rollback()
        return jsonify({'message': 'Database error', 'error': str(e)}), 500

@product_api_bp.route('/<int:id>', methods=['DELETE'])
@token_required
@store_context_required
def delete_product(id):
    """Delete a product"""
    try:
        # Find the product (within the current store context)
        product = PosProduct.query.filter(
            PosProduct.id == id,
            PosProduct.store_id == g.store_id
        ).first()
        
        if not product:
            return jsonify({
                'success': False,
                'message': 'Product not found'
            }), 404
        
        # Store product info for logging before deletion
        product_id = product.id
        product_name = product.name
        
        # Delete the product
        db.session.delete(product)
        db.session.commit()
        
        # Log the product deletion
        logger.info(f"Product deleted: ID={product_id}, Name={product_name}")
        
        return jsonify({
            'success': True,
            'message': 'Product deleted successfully'
        }), 200
        
    except SQLAlchemyError as e:
        db.session.rollback()
        return jsonify({'message': 'Database error', 'error': str(e)}), 500
