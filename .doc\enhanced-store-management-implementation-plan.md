# Enhanced Store Management System - Implementation Plan

## 🎯 Project Overview

This document outlines the implementation plan for creating an enhanced store management system with proper user-store permissions, hierarchical access control, and isolated store API operations.

## 📋 Requirements Summary

1. **Create isolated `store_api.py`** for all store operations
2. **Implement user-store permission** based on `user.dept_id`
3. **Support hierarchical store access** (user's store + sub-branches)
4. **Enhanced login flow** with proper store isolation
5. **Update auth_api.py** to use the new store service

## 🏗️ Architecture Overview

### Current State Analysis
- **User Model**: Has `dept_id` field linking users to departments/stores
- **Dept Model**: Has hierarchical structure with `parent_id` field for branch relationships
- **Current Store API**: Returns all active stores regardless of user permissions

### Target Architecture

```mermaid
graph TD
    A[store_api.py] --> B[Store Service Layer]
    B --> C[User Store Permissions]
    B --> D[Store Hierarchy Logic]
    B --> E[CRUD Operations]
    
    C --> F[get_user_accessible_stores]
    D --> G[get_store_branches_recursive]
    E --> H[Standard Store Management]
    
    subgraph "API Endpoints"
        I[POST /api/stores/user-stores]
        J[GET /api/stores/hierarchy/{store_id}]
        K[GET /api/stores/{id}]
        L[GET /api/stores]
        M[POST /api/stores]
        N[PUT /api/stores/{id}]
        O[DELETE /api/stores/{id}]
    end
```

## 📐 Implementation Phases

### Phase 1: Create New Store API (`store_api.py`)

#### 1.1 Core Store Service
Create a comprehensive store service that handles:
- User permission validation
- Hierarchical store queries
- CRUD operations
- Store relationship management

#### 1.2 API Endpoints Structure
```python
# store_api.py
@store_api_bp.route('/user-stores', methods=['POST'])
def get_user_accessible_stores():
    """Get stores accessible to authenticated user based on dept_id"""

@store_api_bp.route('/hierarchy/<int:store_id>', methods=['GET'])  
def get_store_hierarchy():
    """Get complete hierarchy tree for a store"""

@store_api_bp.route('/<int:store_id>/branches', methods=['GET'])
def get_store_branches():
    """Get direct and recursive branches of a store"""
```

### Phase 2: Store Permission Logic

#### 2.1 Permission Model
```mermaid
flowchart TD
    A[User Login] --> B[Get user.dept_id]
    B --> C[Find User's Primary Store]
    C --> D[Query Recursive Branches]
    D --> E{Has Branches?}
    E -->|Yes| F[Include All Sub-branches]
    E -->|No| G[Only Primary Store]
    F --> H[Return Store List]
    G --> H
```

#### 2.2 Database Query Logic
```sql
-- Get user's accessible stores (recursive CTE)
WITH RECURSIVE store_hierarchy AS (
    SELECT id, dept_name, parent_id, 0 as level
    FROM admin_dept 
    WHERE id = user.dept_id AND status = 1
    UNION ALL
    SELECT d.id, d.dept_name, d.parent_id, sh.level + 1
    FROM admin_dept d
    INNER JOIN store_hierarchy sh ON d.parent_id = sh.id
    WHERE d.status = 1
)
SELECT * FROM store_hierarchy;
```

### Phase 3: Update Authentication Flow

#### 3.1 Enhanced Login Sequence
```mermaid
sequenceDiagram
    participant U as User
    participant A as Auth API
    participant S as Store API
    participant DB as Database
    
    U->>A: POST /auth/stores (credentials)
    A->>DB: Validate user credentials
    A->>S: get_user_accessible_stores(user.dept_id)
    S->>DB: Query user store + branches
    S->>A: Return accessible stores
    A->>U: Store list for selection
```

## 🔧 Technical Implementation Details

### File Structure
```
evospace-admin/applications/
├── api/
│   ├── auth_api.py (updated)
│   ├── store_api.py (new)
│   └── __init__.py (register new blueprint)
├── services/
│   └── store_service.py (new helper service)
├── models/
│   ├── admin_user.py (existing)
│   └── admin_dept.py (existing)
└── schemas/
    └── store_schemas.py (new)
```

### Core Permission Logic
- **Primary Store**: User's `dept_id` determines their main store
- **Branch Access**: Recursive query to find all stores where `parent_id` traces back to user's store
- **Validation**: Ensure user can only login to stores they have access to
- **Future-ready**: Structure allows for easy addition of role-based permissions

## 🔄 Migration Strategy

### Step 1: Backend Implementation
1. Create `services/store_service.py` with core business logic
2. Create `api/store_api.py` with RESTful endpoints
3. Update `api/auth_api.py` to use store service
4. Register new blueprint in `__init__.py`

### Step 2: Testing & Validation
1. Unit tests for store service logic
2. Integration tests for API endpoints
3. Permission validation tests

### Step 3: Frontend Updates
1. Update `apiService.ts` to use new store endpoints
2. Enhance login flow to handle store selection
3. Update store context management

## 📝 Implementation Checklist

### Backend Tasks
- [ ] Create `store_service.py` with core business logic
- [ ] Implement recursive store hierarchy queries
- [ ] Create `store_api.py` with all CRUD endpoints
- [ ] Add user permission validation logic
- [ ] Update `auth_api.py` to use store service
- [ ] Add comprehensive error handling

### Frontend Tasks
- [ ] Update API service with new store endpoints
- [ ] Enhance login page for improved store selection
- [ ] Update store context management
- [ ] Add permission-based UI elements

---

**Ready to proceed with implementation in code mode!**