"use client";

import React, { useState, useMemo } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  InputAdornment,
  Typography,
  Box,
  Card,
  CardContent,
  CardMedia,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Pagination,
} from '@mui/material';
import { Search as SearchIcon } from '@mui/icons-material';
import type { Product } from '@/lib/types';

interface ProductSelectionDialogProps {
  open: boolean;
  onClose: () => void;
  products: Product[]; 
  onSelectProduct: (product: Product) => void;
  title?: string;
}

const ITEMS_PER_PAGE = 8; // Adjust as needed

const ProductSelectionDialog: React.FC<ProductSelectionDialogProps> = ({
  open,
  onClose,
  products, 
  onSelectProduct,
  title = "Select Product"
}) => {
  const [searchTerm, setSearchTerm] = useState(''); 
  const [selectedDialogCategory, setSelectedDialogCategory] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);

  const dialogProductCategories = useMemo(() => {
    if (!products) return [];
    const categories = new Set(products.map(p => p.category));
    return Array.from(categories).sort();
  }, [products]);

  const locallyFilteredProducts = useMemo(() => {
    return products
      .filter(product => {
        const searchMatch = 
          product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          product.category.toLowerCase().includes(searchTerm.toLowerCase()); // Keep searching category text for broader match
        
        const categoryMatch = 
          selectedDialogCategory === 'all' || product.category === selectedDialogCategory;
        
        return searchMatch && categoryMatch;
      });
  }, [products, searchTerm, selectedDialogCategory]);

  const paginatedProducts = useMemo(() => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    return locallyFilteredProducts.slice(startIndex, startIndex + ITEMS_PER_PAGE);
  }, [locallyFilteredProducts, currentPage]);

  const totalPages = Math.ceil(locallyFilteredProducts.length / ITEMS_PER_PAGE);

  const handleSelect = (product: Product) => {
    onSelectProduct(product);
    setSearchTerm(''); 
    setSelectedDialogCategory('all');
    setCurrentPage(1);
    onClose(); 
  };

  const handleCloseDialog = () => {
    setSearchTerm('');
    setSelectedDialogCategory('all');
    setCurrentPage(1);
    onClose();
  }

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setCurrentPage(value);
  };

  // Reset page to 1 when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, selectedDialogCategory]);

  return (
    <Dialog open={open} onClose={handleCloseDialog} maxWidth="lg" fullWidth scroll="paper">
      <DialogTitle>{title}</DialogTitle>
      <DialogContent sx={{ pb: 1, display: 'flex', flexDirection: 'column' }}> 
        <Box sx={{ display: 'flex', gap: 2, mb: 2, position: 'sticky', top: 0, bgcolor: 'background.paper', zIndex: 1, pt:1, pb:1 }}>
          <TextField
            autoFocus
            margin="none" // Changed from dense
            label="Search products..."
            type="text"
            fullWidth
            variant="outlined"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ flexGrow: 2 }}
          />
          <FormControl sx={{ minWidth: 200, flexGrow: 1 }}>
            <InputLabel>Category</InputLabel>
            <Select
              value={selectedDialogCategory}
              label="Category"
              onChange={(e) => setSelectedDialogCategory(e.target.value as string)}
            >
              <MenuItem value="all">All Categories</MenuItem>
              {dialogProductCategories.map((category) => (
                <MenuItem key={category} value={category}>{category}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>

        <Box sx={{ flexGrow: 1, overflowY: 'auto', pb: 1 }}>
          {paginatedProducts.length > 0 ? (
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, justifyContent: 'flex-start', pt: 1 }}>
              {paginatedProducts.map((product) => (
                <Card 
                  key={product.id} 
                  sx={{ 
                    width: 200, 
                    cursor: 'pointer',
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'scale(1.03)',
                      boxShadow: 3
                    }
                  }}
                  onClick={() => handleSelect(product)}
                >
                  {product.image ? (
                    <CardMedia
                      component="img"
                      height="140"
                      image={product.image}
                      alt={product.name}
                    />
                  ) : (
                    <Box 
                      sx={{ 
                        height: 140, 
                        display: 'flex', 
                        alignItems: 'center', 
                        justifyContent: 'center',
                        bgcolor: 'primary.light',
                        color: 'white'
                      }}
                    >
                      <Typography variant="h6" align="center" sx={{ p: 2 }}>
                        {product.name}
                      </Typography>
                    </Box>
                  )}
                  <CardContent>
                    <Typography gutterBottom variant="h6" component="div">
                      {product.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {product.category}
                    </Typography>
                    <Typography variant="h6" color="primary" sx={{ mt: 1 }}>
                      ${Number(product.price).toFixed(2)}
                    </Typography>
                  </CardContent>
                </Card>
              ))}
            </Box>
          ) : (
            <Typography sx={{ textAlign: 'center', mt: 2 }}>
              {searchTerm || selectedDialogCategory !== 'all' ? 'No products match your search or filter.' : 'No products available.'}
            </Typography>
          )}
        </Box>
      </DialogContent>
      <DialogActions sx={{ justifyContent: 'flex-end', alignItems: 'center', pt:1, pb:1, gap: 2 }}>
        {totalPages > 1 && (
          <Pagination 
            count={totalPages} 
            page={currentPage} 
            onChange={handlePageChange} 
            color="primary" 
            size="small"
            sx={{ mr: 'auto' }} // Push pagination to the left
          />
        )}
        <Button onClick={handleCloseDialog}>Cancel</Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProductSelectionDialog;
