import React from 'react';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography
} from '@mui/material';

interface ConfirmDialogProps {
  open: boolean;
  title: string;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
  infoMode?: boolean;
  confirmText?: string;
  confirmButtonColor?: 'inherit' | 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning';
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({ 
  open, 
  title, 
  message, 
  onConfirm, 
  onCancel, 
  infoMode = false, 
  confirmText, 
  confirmButtonColor = 'primary'
}) => (
  <Dialog open={open} onClose={onCancel}> 
    <DialogTitle>{title}</DialogTitle>
    <DialogContent>
      <Typography>{message}</Typography>
    </DialogContent>
    <DialogActions>
      {!infoMode && (
        <Button onClick={onCancel}>Cancel</Button>
      )}
      <Button onClick={onConfirm} variant="contained" color={infoMode ? 'primary' : confirmButtonColor}>
        {infoMode ? (confirmText || 'OK') : (confirmText || 'Confirm')}
      </Button>
    </DialogActions>
  </Dialog>
);

export default ConfirmDialog;
