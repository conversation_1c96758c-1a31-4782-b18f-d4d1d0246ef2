<!DOCTYPE html>
<html>
<head>
    <title>日志</title>
    {% include 'system/common/header.html' %}
</head>
<body class="pear-container">

<div class="layui-card">
    <div class="layui-card-body">
        <div class="layui-tab layui-tab-card">
            <ul class="layui-tab-title">
                <li class="layui-this">登录日志</li>
                <li>操作日志</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <table style="margin-top: 10px;" id="log-login-table" lay-filter="log-login-table"></table>
                </div>
                <div class="layui-tab-item">
                    <table style="margin-top: 10px;" id="log-operate-table" lay-filter="log-operate-table"></table>
                </div>
            </div>
        </div>
    </div>
</div>
</body>


<script type="text/html" id="log-createTime">
    {{ '  {{layui.util.toDateString(d.create_time,  "yyyy-MM-dd HH:mm:ss")}' |safe }}}
</script>
<script type="text/html" id="log-status">
    {{ '{{#if (d.success == true) { }}
    <span style="color: green">成功</span>
    {{# }else if(d.success == false){ }}
    <span style="color: red">失败</span>
    {{# } }}'|safe }}
</script>
{% include 'system/common/footer.html' %}
<script>
    layui.use(['table', 'form', 'jquery', 'element', 'util'], function () {
        let table = layui.table
        let form = layui.form

        let MODULE_PATH = '/system/log/'

        let cols = [
            [
                {title: '编号', field: 'id', align: 'center', width: 80},
                {title: '请求方式', field: 'method', align: 'center', width: 100},
                {title: '接口', field: 'url', align: 'center', width: 300},
                {title: '浏览器', field: 'user_agent', align: 'center', width: 350},
                {title: '操作地址', field: 'ip', align: 'center', width: 100},
                {title: '访问时间', field: 'create_time', templet: '#log-createTime', align: 'center', width: 100},
                {title: '操作人ID', field: 'uid', align: 'center', width: 100},
                {title: '描述', field: 'desc', align: 'center', width: 150},
                {title: '访问状态', templet: '#log-status', align: 'center', width: 100}
            ]
        ]

        table.render({
            elem: '#log-operate-table',
            url: MODULE_PATH + 'operateLog',
            page: true,
            cols: cols,
            skin: 'line',
            toolbar: false
        })

        table.render({
            elem: '#log-login-table',
            url: MODULE_PATH + 'loginLog',
            page: true,
            cols: JSON.parse(JSON.stringify(cols)),  // 复制对象
            skin: 'line',
            toolbar: false
        })

        form.on('submit(dict-type-query)', function (data) {
            table.reload('dict-type-table', {where: data.field})
            return false
        })

        table.on('tool(log-operate-table)', function (obj) {
            if (obj.event === 'details') {
                window.info(obj)
            }
        })

        table.on('tool(log-login-table)', function (obj) {
            if (obj.event === 'details') {
                window.info(obj)
            }
        })

        window.info = function (obj) {
            layer.open({
                type: 2,
                title: '详细信息',
                shade: 0,
                area: ['400px', '400px'],
                content: MODULE_PATH + 'info',
                success: function (layero) {
                    let iframeWin = window[layero.find('iframe')[0]['name']]
                    iframeWin.show(obj.data)
                }
            })
        }
    })
</script>
</html>