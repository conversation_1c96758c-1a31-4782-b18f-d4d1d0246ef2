<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>IconFont Demo</title>
  <link rel="shortcut icon" href="https://img.alicdn.com/tps/i4/TB1_oz6GVXXXXaFXpXXJDFnIXXX-64-64.ico" type="image/x-icon"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">&#xe86b;</a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=1858883" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe615;</span>
                <div class="name">日历</div>
                <div class="code-name">&amp;#xe615;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe616;</span>
                <div class="name">书包</div>
                <div class="code-name">&amp;#xe616;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe617;</span>
                <div class="name">测量</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe618;</span>
                <div class="name">放大镜</div>
                <div class="code-name">&amp;#xe618;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe619;</span>
                <div class="name">地球仪</div>
                <div class="code-name">&amp;#xe619;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61a;</span>
                <div class="name">笔记</div>
                <div class="code-name">&amp;#xe61a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61b;</span>
                <div class="name">学士帽</div>
                <div class="code-name">&amp;#xe61b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61c;</span>
                <div class="name">线上直播</div>
                <div class="code-name">&amp;#xe61c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe670;</span>
                <div class="name">目录1</div>
                <div class="code-name">&amp;#xe670;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe671;</span>
                <div class="name">编辑</div>
                <div class="code-name">&amp;#xe671;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe672;</span>
                <div class="name">更多1</div>
                <div class="code-name">&amp;#xe672;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe673;</span>
                <div class="name">搜素</div>
                <div class="code-name">&amp;#xe673;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe674;</span>
                <div class="name">设置</div>
                <div class="code-name">&amp;#xe674;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe675;</span>
                <div class="name">下载</div>
                <div class="code-name">&amp;#xe675;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe676;</span>
                <div class="name">我的</div>
                <div class="code-name">&amp;#xe676;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe677;</span>
                <div class="name">工具箱</div>
                <div class="code-name">&amp;#xe677;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe678;</span>
                <div class="name">警告</div>
                <div class="code-name">&amp;#xe678;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe679;</span>
                <div class="name">星球</div>
                <div class="code-name">&amp;#xe679;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe67a;</span>
                <div class="name">邮件</div>
                <div class="code-name">&amp;#xe67a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe67b;</span>
                <div class="name">链接</div>
                <div class="code-name">&amp;#xe67b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe644;</span>
                <div class="name">转换</div>
                <div class="code-name">&amp;#xe644;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe647;</span>
                <div class="name">分类1</div>
                <div class="code-name">&amp;#xe647;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64a;</span>
                <div class="name">扫码</div>
                <div class="code-name">&amp;#xe64a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64b;</span>
                <div class="name">二维码</div>
                <div class="code-name">&amp;#xe64b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64c;</span>
                <div class="name">定位</div>
                <div class="code-name">&amp;#xe64c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64d;</span>
                <div class="name">关闭</div>
                <div class="code-name">&amp;#xe64d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64e;</span>
                <div class="name">分享</div>
                <div class="code-name">&amp;#xe64e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64f;</span>
                <div class="name">分享1</div>
                <div class="code-name">&amp;#xe64f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe650;</span>
                <div class="name">关闭1</div>
                <div class="code-name">&amp;#xe650;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe651;</span>
                <div class="name">多云</div>
                <div class="code-name">&amp;#xe651;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe652;</span>
                <div class="name">发现</div>
                <div class="code-name">&amp;#xe652;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe653;</span>
                <div class="name">我的</div>
                <div class="code-name">&amp;#xe653;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe654;</span>
                <div class="name">编辑</div>
                <div class="code-name">&amp;#xe654;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe655;</span>
                <div class="name">消息</div>
                <div class="code-name">&amp;#xe655;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe656;</span>
                <div class="name">首页</div>
                <div class="code-name">&amp;#xe656;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe657;</span>
                <div class="name">窗户</div>
                <div class="code-name">&amp;#xe657;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe658;</span>
                <div class="name">账单</div>
                <div class="code-name">&amp;#xe658;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe659;</span>
                <div class="name">导航</div>
                <div class="code-name">&amp;#xe659;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65a;</span>
                <div class="name">文件夹1</div>
                <div class="code-name">&amp;#xe65a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65b;</span>
                <div class="name">鼠标</div>
                <div class="code-name">&amp;#xe65b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65c;</span>
                <div class="name">生活</div>
                <div class="code-name">&amp;#xe65c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65d;</span>
                <div class="name">耳机</div>
                <div class="code-name">&amp;#xe65d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65e;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe65e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65f;</span>
                <div class="name">更多</div>
                <div class="code-name">&amp;#xe65f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe660;</span>
                <div class="name">语音</div>
                <div class="code-name">&amp;#xe660;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe661;</span>
                <div class="name">文件</div>
                <div class="code-name">&amp;#xe661;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe662;</span>
                <div class="name">信号</div>
                <div class="code-name">&amp;#xe662;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe663;</span>
                <div class="name">首页1</div>
                <div class="code-name">&amp;#xe663;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe664;</span>
                <div class="name">图片</div>
                <div class="code-name">&amp;#xe664;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe665;</span>
                <div class="name">文件夹</div>
                <div class="code-name">&amp;#xe665;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe666;</span>
                <div class="name">锁</div>
                <div class="code-name">&amp;#xe666;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe667;</span>
                <div class="name">等级</div>
                <div class="code-name">&amp;#xe667;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe668;</span>
                <div class="name">上传1</div>
                <div class="code-name">&amp;#xe668;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe669;</span>
                <div class="name">添加</div>
                <div class="code-name">&amp;#xe669;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66a;</span>
                <div class="name">返回</div>
                <div class="code-name">&amp;#xe66a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66b;</span>
                <div class="name">账单</div>
                <div class="code-name">&amp;#xe66b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66c;</span>
                <div class="name">下载</div>
                <div class="code-name">&amp;#xe66c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66d;</span>
                <div class="name">目录</div>
                <div class="code-name">&amp;#xe66d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66e;</span>
                <div class="name">关闭</div>
                <div class="code-name">&amp;#xe66e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66f;</span>
                <div class="name">上传</div>
                <div class="code-name">&amp;#xe66f;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>兼容性最好，支持 IE6+，及所有现代浏览器。</li>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>但是因为是字体，所以不支持多色。只能使用平台里单色的图标，就算项目里有多色图标也会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持多色图标，这些多色图标在 Unicode 模式下将不能使用，如果有需求建议使用symbol 的引用方式</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot');
  src: url('iconfont.eot?#iefix') format('embedded-opentype'),
      url('iconfont.woff2') format('woff2'),
      url('iconfont.woff') format('woff'),
      url('iconfont.ttf') format('truetype'),
      url('iconfont.svg#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont pear-iconrili"></span>
            <div class="name">
              日历
            </div>
            <div class="code-name">.pear-iconrili
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconshubao"></span>
            <div class="name">
              书包
            </div>
            <div class="code-name">.pear-iconshubao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconceliang"></span>
            <div class="name">
              测量
            </div>
            <div class="code-name">.pear-iconceliang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconfangdajing"></span>
            <div class="name">
              放大镜
            </div>
            <div class="code-name">.pear-iconfangdajing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-icondiqiuyi"></span>
            <div class="name">
              地球仪
            </div>
            <div class="code-name">.pear-icondiqiuyi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconbiji"></span>
            <div class="name">
              笔记
            </div>
            <div class="code-name">.pear-iconbiji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconxueshimao"></span>
            <div class="name">
              学士帽
            </div>
            <div class="code-name">.pear-iconxueshimao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconxianshangzhibo"></span>
            <div class="name">
              线上直播
            </div>
            <div class="code-name">.pear-iconxianshangzhibo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconmulu1"></span>
            <div class="name">
              目录1
            </div>
            <div class="code-name">.pear-iconmulu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconbianji1"></span>
            <div class="name">
              编辑
            </div>
            <div class="code-name">.pear-iconbianji1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-icongengduo1"></span>
            <div class="name">
              更多1
            </div>
            <div class="code-name">.pear-icongengduo1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconsousu"></span>
            <div class="name">
              搜素
            </div>
            <div class="code-name">.pear-iconsousu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconshezhi"></span>
            <div class="name">
              设置
            </div>
            <div class="code-name">.pear-iconshezhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconxiazai1"></span>
            <div class="name">
              下载
            </div>
            <div class="code-name">.pear-iconxiazai1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconwode1"></span>
            <div class="name">
              我的
            </div>
            <div class="code-name">.pear-iconwode1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-icongongjuxiang"></span>
            <div class="name">
              工具箱
            </div>
            <div class="code-name">.pear-icongongjuxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconjinggao"></span>
            <div class="name">
              警告
            </div>
            <div class="code-name">.pear-iconjinggao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconxingqiu"></span>
            <div class="name">
              星球
            </div>
            <div class="code-name">.pear-iconxingqiu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconyoujian"></span>
            <div class="name">
              邮件
            </div>
            <div class="code-name">.pear-iconyoujian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconlianjie"></span>
            <div class="name">
              链接
            </div>
            <div class="code-name">.pear-iconlianjie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconzhuanhuan"></span>
            <div class="name">
              转换
            </div>
            <div class="code-name">.pear-iconzhuanhuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconfenlei1"></span>
            <div class="name">
              分类1
            </div>
            <div class="code-name">.pear-iconfenlei1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconsaoma"></span>
            <div class="name">
              扫码
            </div>
            <div class="code-name">.pear-iconsaoma
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconerweima"></span>
            <div class="name">
              二维码
            </div>
            <div class="code-name">.pear-iconerweima
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-icondingwei"></span>
            <div class="name">
              定位
            </div>
            <div class="code-name">.pear-icondingwei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconguanbi"></span>
            <div class="name">
              关闭
            </div>
            <div class="code-name">.pear-iconguanbi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconfenxiang"></span>
            <div class="name">
              分享
            </div>
            <div class="code-name">.pear-iconfenxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconfenxiang1"></span>
            <div class="name">
              分享1
            </div>
            <div class="code-name">.pear-iconfenxiang1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconguanbi1"></span>
            <div class="name">
              关闭1
            </div>
            <div class="code-name">.pear-iconguanbi1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconduoyun"></span>
            <div class="name">
              多云
            </div>
            <div class="code-name">.pear-iconduoyun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconfaxian"></span>
            <div class="name">
              发现
            </div>
            <div class="code-name">.pear-iconfaxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconwode"></span>
            <div class="name">
              我的
            </div>
            <div class="code-name">.pear-iconwode
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconbianji"></span>
            <div class="name">
              编辑
            </div>
            <div class="code-name">.pear-iconbianji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconxiaoxi"></span>
            <div class="name">
              消息
            </div>
            <div class="code-name">.pear-iconxiaoxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconshouye"></span>
            <div class="name">
              首页
            </div>
            <div class="code-name">.pear-iconshouye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconchuanghu"></span>
            <div class="name">
              窗户
            </div>
            <div class="code-name">.pear-iconchuanghu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconzhangdan"></span>
            <div class="name">
              账单
            </div>
            <div class="code-name">.pear-iconzhangdan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-icondaohang"></span>
            <div class="name">
              导航
            </div>
            <div class="code-name">.pear-icondaohang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconwenjianjia1"></span>
            <div class="name">
              文件夹1
            </div>
            <div class="code-name">.pear-iconwenjianjia1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconshubiao"></span>
            <div class="name">
              鼠标
            </div>
            <div class="code-name">.pear-iconshubiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconshenghuo"></span>
            <div class="name">
              生活
            </div>
            <div class="code-name">.pear-iconshenghuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconerji"></span>
            <div class="name">
              耳机
            </div>
            <div class="code-name">.pear-iconerji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconshanchu"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.pear-iconshanchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-icongengduo"></span>
            <div class="name">
              更多
            </div>
            <div class="code-name">.pear-icongengduo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconyuyin"></span>
            <div class="name">
              语音
            </div>
            <div class="code-name">.pear-iconyuyin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconwenjian"></span>
            <div class="name">
              文件
            </div>
            <div class="code-name">.pear-iconwenjian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconxinhao"></span>
            <div class="name">
              信号
            </div>
            <div class="code-name">.pear-iconxinhao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconshouye1"></span>
            <div class="name">
              首页1
            </div>
            <div class="code-name">.pear-iconshouye1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-icontupian"></span>
            <div class="name">
              图片
            </div>
            <div class="code-name">.pear-icontupian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconwenjianjia"></span>
            <div class="name">
              文件夹
            </div>
            <div class="code-name">.pear-iconwenjianjia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconsuo"></span>
            <div class="name">
              锁
            </div>
            <div class="code-name">.pear-iconsuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-icondengji"></span>
            <div class="name">
              等级
            </div>
            <div class="code-name">.pear-icondengji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconshangchuan1"></span>
            <div class="name">
              上传1
            </div>
            <div class="code-name">.pear-iconshangchuan1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-icontianjia"></span>
            <div class="name">
              添加
            </div>
            <div class="code-name">.pear-icontianjia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconfanhui"></span>
            <div class="name">
              返回
            </div>
            <div class="code-name">.pear-iconfanhui
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconzhangdan1"></span>
            <div class="name">
              账单
            </div>
            <div class="code-name">.pear-iconzhangdan1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconxiazai"></span>
            <div class="name">
              下载
            </div>
            <div class="code-name">.pear-iconxiazai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconmulu"></span>
            <div class="name">
              目录
            </div>
            <div class="code-name">.pear-iconmulu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconguanbi2"></span>
            <div class="name">
              关闭
            </div>
            <div class="code-name">.pear-iconguanbi2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pear-iconshangchuan"></span>
            <div class="name">
              上传
            </div>
            <div class="code-name">.pear-iconshangchuan
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>兼容性良好，支持 IE8+，及所有现代浏览器。</li>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
          <li>不过因为本质上还是使用的字体，所以多色图标还是不支持的。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont pear-iconxxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconrili"></use>
                </svg>
                <div class="name">日历</div>
                <div class="code-name">#pear-iconrili</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconshubao"></use>
                </svg>
                <div class="name">书包</div>
                <div class="code-name">#pear-iconshubao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconceliang"></use>
                </svg>
                <div class="name">测量</div>
                <div class="code-name">#pear-iconceliang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconfangdajing"></use>
                </svg>
                <div class="name">放大镜</div>
                <div class="code-name">#pear-iconfangdajing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-icondiqiuyi"></use>
                </svg>
                <div class="name">地球仪</div>
                <div class="code-name">#pear-icondiqiuyi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconbiji"></use>
                </svg>
                <div class="name">笔记</div>
                <div class="code-name">#pear-iconbiji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconxueshimao"></use>
                </svg>
                <div class="name">学士帽</div>
                <div class="code-name">#pear-iconxueshimao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconxianshangzhibo"></use>
                </svg>
                <div class="name">线上直播</div>
                <div class="code-name">#pear-iconxianshangzhibo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconmulu1"></use>
                </svg>
                <div class="name">目录1</div>
                <div class="code-name">#pear-iconmulu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconbianji1"></use>
                </svg>
                <div class="name">编辑</div>
                <div class="code-name">#pear-iconbianji1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-icongengduo1"></use>
                </svg>
                <div class="name">更多1</div>
                <div class="code-name">#pear-icongengduo1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconsousu"></use>
                </svg>
                <div class="name">搜素</div>
                <div class="code-name">#pear-iconsousu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconshezhi"></use>
                </svg>
                <div class="name">设置</div>
                <div class="code-name">#pear-iconshezhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconxiazai1"></use>
                </svg>
                <div class="name">下载</div>
                <div class="code-name">#pear-iconxiazai1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconwode1"></use>
                </svg>
                <div class="name">我的</div>
                <div class="code-name">#pear-iconwode1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-icongongjuxiang"></use>
                </svg>
                <div class="name">工具箱</div>
                <div class="code-name">#pear-icongongjuxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconjinggao"></use>
                </svg>
                <div class="name">警告</div>
                <div class="code-name">#pear-iconjinggao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconxingqiu"></use>
                </svg>
                <div class="name">星球</div>
                <div class="code-name">#pear-iconxingqiu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconyoujian"></use>
                </svg>
                <div class="name">邮件</div>
                <div class="code-name">#pear-iconyoujian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconlianjie"></use>
                </svg>
                <div class="name">链接</div>
                <div class="code-name">#pear-iconlianjie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconzhuanhuan"></use>
                </svg>
                <div class="name">转换</div>
                <div class="code-name">#pear-iconzhuanhuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconfenlei1"></use>
                </svg>
                <div class="name">分类1</div>
                <div class="code-name">#pear-iconfenlei1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconsaoma"></use>
                </svg>
                <div class="name">扫码</div>
                <div class="code-name">#pear-iconsaoma</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconerweima"></use>
                </svg>
                <div class="name">二维码</div>
                <div class="code-name">#pear-iconerweima</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-icondingwei"></use>
                </svg>
                <div class="name">定位</div>
                <div class="code-name">#pear-icondingwei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconguanbi"></use>
                </svg>
                <div class="name">关闭</div>
                <div class="code-name">#pear-iconguanbi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconfenxiang"></use>
                </svg>
                <div class="name">分享</div>
                <div class="code-name">#pear-iconfenxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconfenxiang1"></use>
                </svg>
                <div class="name">分享1</div>
                <div class="code-name">#pear-iconfenxiang1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconguanbi1"></use>
                </svg>
                <div class="name">关闭1</div>
                <div class="code-name">#pear-iconguanbi1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconduoyun"></use>
                </svg>
                <div class="name">多云</div>
                <div class="code-name">#pear-iconduoyun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconfaxian"></use>
                </svg>
                <div class="name">发现</div>
                <div class="code-name">#pear-iconfaxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconwode"></use>
                </svg>
                <div class="name">我的</div>
                <div class="code-name">#pear-iconwode</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconbianji"></use>
                </svg>
                <div class="name">编辑</div>
                <div class="code-name">#pear-iconbianji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconxiaoxi"></use>
                </svg>
                <div class="name">消息</div>
                <div class="code-name">#pear-iconxiaoxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconshouye"></use>
                </svg>
                <div class="name">首页</div>
                <div class="code-name">#pear-iconshouye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconchuanghu"></use>
                </svg>
                <div class="name">窗户</div>
                <div class="code-name">#pear-iconchuanghu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconzhangdan"></use>
                </svg>
                <div class="name">账单</div>
                <div class="code-name">#pear-iconzhangdan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-icondaohang"></use>
                </svg>
                <div class="name">导航</div>
                <div class="code-name">#pear-icondaohang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconwenjianjia1"></use>
                </svg>
                <div class="name">文件夹1</div>
                <div class="code-name">#pear-iconwenjianjia1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconshubiao"></use>
                </svg>
                <div class="name">鼠标</div>
                <div class="code-name">#pear-iconshubiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconshenghuo"></use>
                </svg>
                <div class="name">生活</div>
                <div class="code-name">#pear-iconshenghuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconerji"></use>
                </svg>
                <div class="name">耳机</div>
                <div class="code-name">#pear-iconerji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconshanchu"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#pear-iconshanchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-icongengduo"></use>
                </svg>
                <div class="name">更多</div>
                <div class="code-name">#pear-icongengduo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconyuyin"></use>
                </svg>
                <div class="name">语音</div>
                <div class="code-name">#pear-iconyuyin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconwenjian"></use>
                </svg>
                <div class="name">文件</div>
                <div class="code-name">#pear-iconwenjian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconxinhao"></use>
                </svg>
                <div class="name">信号</div>
                <div class="code-name">#pear-iconxinhao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconshouye1"></use>
                </svg>
                <div class="name">首页1</div>
                <div class="code-name">#pear-iconshouye1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-icontupian"></use>
                </svg>
                <div class="name">图片</div>
                <div class="code-name">#pear-icontupian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconwenjianjia"></use>
                </svg>
                <div class="name">文件夹</div>
                <div class="code-name">#pear-iconwenjianjia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconsuo"></use>
                </svg>
                <div class="name">锁</div>
                <div class="code-name">#pear-iconsuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-icondengji"></use>
                </svg>
                <div class="name">等级</div>
                <div class="code-name">#pear-icondengji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconshangchuan1"></use>
                </svg>
                <div class="name">上传1</div>
                <div class="code-name">#pear-iconshangchuan1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-icontianjia"></use>
                </svg>
                <div class="name">添加</div>
                <div class="code-name">#pear-icontianjia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconfanhui"></use>
                </svg>
                <div class="name">返回</div>
                <div class="code-name">#pear-iconfanhui</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconzhangdan1"></use>
                </svg>
                <div class="name">账单</div>
                <div class="code-name">#pear-iconzhangdan1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconxiazai"></use>
                </svg>
                <div class="name">下载</div>
                <div class="code-name">#pear-iconxiazai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconmulu"></use>
                </svg>
                <div class="name">目录</div>
                <div class="code-name">#pear-iconmulu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconguanbi2"></use>
                </svg>
                <div class="name">关闭</div>
                <div class="code-name">#pear-iconguanbi2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pear-iconshangchuan"></use>
                </svg>
                <div class="name">上传</div>
                <div class="code-name">#pear-iconshangchuan</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
