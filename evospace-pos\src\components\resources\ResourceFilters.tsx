import React from 'react';
import { Box, FormControl, InputLabel, Select, MenuItem, Paper, SelectChangeEvent } from '@mui/material';

interface ResourceFiltersProps {
  selectedFloor: number | null;
  onFloorChange: (event: SelectChangeEvent<string | number>) => void; // Allow number for Select value
  floors: (string | number)[]; // Allow string or number for floor values
  selectedZone: string;
  onZoneChange: (event: SelectChangeEvent<string>) => void;
  zones: string[];
  viewMode?: 'list' | 'grid' | 'floorPlan'; // Make it optional to maintain backward compatibility
}

const ResourceFilters: React.FC<ResourceFiltersProps> = ({
  selectedFloor,
  onFloorChange,
  floors,
  selectedZone,
  onZoneChange,
  zones,
  // viewMode is defined in the interface but not used in this component
}) => {

  return (
    <Paper sx={{ p: 2, mb: 3, mt: -1 }}> {/* mt -1 to pull it up slightly if ResourceControls has mb:3 */}
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
        <Box sx={{ flexBasis: { xs: '100%', sm: 'calc(50% - 8px)', md: 'calc(33.333% - 11px)' }, minWidth: '150px' }}>
          <FormControl fullWidth size="small">
            <InputLabel id="floor-filter-label">Filter by Floor</InputLabel>
            <Select
              labelId="floor-filter-label"
              value={selectedFloor === null ? '' : selectedFloor.toString()} // Handle null for "All Floors" if needed, or ensure selectedFloor is always a string/number
              onChange={onFloorChange}
              label="Filter by Floor"
            >
              <MenuItem value="">
                <em>All Floors</em>
              </MenuItem>
              {floors.map((floor) => (
                <MenuItem key={floor} value={floor.toString()}>
                  Floor {floor}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
        <Box sx={{ flexBasis: { xs: '100%', sm: 'calc(50% - 8px)', md: 'calc(33.333% - 11px)' }, minWidth: '150px' }}>
          <FormControl fullWidth size="small">
            <InputLabel id="zone-filter-label">Filter by Zone</InputLabel>
            <Select
              labelId="zone-filter-label"
              value={selectedZone}
              onChange={onZoneChange}
              label="Filter by Zone"
            >
              <MenuItem value="all">
                <em>All Zones</em>
              </MenuItem>
              {zones.map((zone) => (
                <MenuItem key={zone} value={zone}>
                  {zone}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </Box>
    </Paper>
  );
};

export default ResourceFilters;