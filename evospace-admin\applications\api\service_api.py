from flask import Blueprint, request, jsonify, g, url_for
from applications.extensions import db, ma
from applications.models.pos_service import PosService
from applications.schemas.pos_service_schema import PosServiceSchema
from applications.api.auth_api import token_required, store_context_required
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import joinedload
from marshmallow import ValidationError
from datetime import datetime
import logging

# Set up logger
logger = logging.getLogger(__name__)

service_api_bp = Blueprint('service_api', __name__, url_prefix='/api/services')

# Create schemas
service_schema = PosServiceSchema()
services_schema = PosServiceSchema(many=True)

@service_api_bp.route('', methods=['POST'])
@token_required
@store_context_required
def create_service():
    """Create a new service
    
    Request Body:
    - name: Required, service name
    - description: Required, service description
    - price: Required, service price
    - duration: Optional, service duration in minutes
    - category: Optional, service category
    - and other fields as defined in the PosService model
    """
    try:
        # Validate request data
        json_data = request.get_json()
        if not json_data:
            return jsonify({
                'success': False,
                'message': 'No input data provided'
            }), 400
        
        # Validate and deserialize input
        try:
            data = service_schema.load(json_data)
        except ValidationError as err:
            return jsonify({
                'success': False,
                'message': 'Validation error',
                'errors': err.messages
            }), 400
        
        # Create new service and assign store_id
        service = data
        service.store_id = g.store_id  # Set store context from JWT token
        db.session.add(service)
        db.session.commit()
        
        # Log the service creation
        logger.info(f"Service created: ID={service.id}, Name={service.name}, Price={service.price}")
        
        return jsonify({
            'success': True,
            'message': 'Service created successfully',
            'data': {
                'service': service_schema.dump(service)
            }
        }), 201
        
    except ValidationError as err:
        return jsonify({'message': 'Validation error', 'errors': err.messages}), 400
    except SQLAlchemyError as e:
        db.session.rollback()
        return jsonify({'message': 'Database error', 'error': str(e)}), 500

@service_api_bp.route('', methods=['GET'])
@token_required
@store_context_required
def get_services():
    """Get all services with optional filtering and pagination
    
    Query Parameters:
    - name: Filter by service name (partial match)
    - category: Filter by service category (partial match)
    - subcategory: Filter by service subcategory (partial match)
    - min_price: Filter by minimum price
    - max_price: Filter by maximum price
    - min_duration: Filter by minimum duration in minutes
    - max_duration: Filter by maximum duration in minutes
    - status: Filter by status
    - is_featured: Filter featured services (true/false)
    - page: Page number (default: 1)
    - per_page: Items per page (default: 20, max: 100)
    - sort_by: Field to sort by (default: name)
    - sort_order: Sort order ('asc' or 'desc', default: 'asc')
    """
    try:
        # Get query parameters for filtering
        name = request.args.get('name')
        category = request.args.get('category')
        subcategory = request.args.get('subcategory')
        min_price = request.args.get('min_price', type=float)
        max_price = request.args.get('max_price', type=float)
        min_duration = request.args.get('min_duration', type=int)
        max_duration = request.args.get('max_duration', type=int)
        status = request.args.get('status')
        is_featured = request.args.get('is_featured')
        
        # Pagination parameters
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)  # Limit max per_page to 100
        
        # Sorting parameters
        sort_by = request.args.get('sort_by', 'name')
        sort_order = request.args.get('sort_order', 'asc')
        
        # Validate sort parameters
        valid_sort_fields = ['id', 'name', 'price', 'duration', 'category', 'popularity_score', 'created_at', 'updated_at']
        if sort_by not in valid_sort_fields:
            return jsonify({
                'success': False,
                'message': f'Invalid sort_by parameter. Valid options are: {valid_sort_fields}'
            }), 400
            
        if sort_order not in ['asc', 'desc']:
            return jsonify({
                'success': False,
                'message': 'Invalid sort_order parameter. Valid options are: asc, desc'
            }), 400
        
        # Build query with filters (filter by store first)
        query = PosService.query.filter(PosService.store_id == g.store_id)
        
        if name:
            query = query.filter(PosService.name.ilike(f'%{name}%'))
        if category:
            query = query.filter(PosService.category.ilike(f'%{category}%'))
        if subcategory:
            query = query.filter(PosService.subcategory.ilike(f'%{subcategory}%'))
        if min_price is not None:
            query = query.filter(PosService.price >= min_price)
        if max_price is not None:
            query = query.filter(PosService.price <= max_price)
        if min_duration is not None:
            query = query.filter(PosService.duration >= min_duration)
        if max_duration is not None:
            query = query.filter(PosService.duration <= max_duration)
        if status:
            query = query.filter(PosService.status == status)
        if is_featured is not None:
            is_featured_bool = is_featured.lower() == 'true'
            query = query.filter(PosService.is_featured == is_featured_bool)
        
        # Apply sorting
        sort_column = getattr(PosService, sort_by)
        if sort_order == 'desc':
            query = query.order_by(sort_column.desc())
        else:
            query = query.order_by(sort_column.asc())
        
        # Get paginated results
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        services = pagination.items
        
        # Log the query for monitoring
        logger.info(f"Services query: page={page}, per_page={per_page}, filters applied: {request.args}")
        
        # Build pagination metadata
        meta = {
            'page': page,
            'per_page': per_page,
            'total_pages': pagination.pages,
            'total_items': pagination.total
        }
        
        # Add pagination links
        links = {}
        if pagination.has_prev:
            links['prev'] = url_for('service_api.get_services', page=page-1, per_page=per_page, **request.args)
        if pagination.has_next:
            links['next'] = url_for('service_api.get_services', page=page+1, per_page=per_page, **request.args)
        links['first'] = url_for('service_api.get_services', page=1, per_page=per_page, **request.args)
        links['last'] = url_for('service_api.get_services', page=pagination.pages, per_page=per_page, **request.args)
        
        return jsonify({
            'success': True,
            'data': {
                'items': services_schema.dump(services),
                'meta': meta,
                'links': links
            }
        }), 200
        
    except SQLAlchemyError as e:
        return jsonify({'message': 'Database error', 'error': str(e)}), 500

@service_api_bp.route('/<int:id>', methods=['GET'])
@token_required
@store_context_required
def get_service(id):
    """Get a specific service by ID"""
    try:
        service = PosService.query.filter(
            PosService.id == id,
            PosService.store_id == g.store_id
        ).first()
        
        if not service:
            return jsonify({
                'success': False,
                'message': 'Service not found'
            }), 404
        
        return jsonify({
            'success': True,
            'data': {
                'service': service_schema.dump(service)
            }
        }), 200
        
    except SQLAlchemyError as e:
        return jsonify({'message': 'Database error', 'error': str(e)}), 500

@service_api_bp.route('/<int:id>', methods=['PUT'])
@token_required
@store_context_required
def update_service(id):
    """Update a specific service"""
    try:
        # Find the service (within the current store context)
        service = PosService.query.filter(
            PosService.id == id,
            PosService.store_id == g.store_id
        ).first()
        
        if not service:
            return jsonify({
                'success': False,
                'message': 'Service not found'
            }), 404
        
        # Validate request data
        json_data = request.get_json()
        if not json_data:
            return jsonify({
                'success': False,
                'message': 'No input data provided'
            }), 400
        
        # Validate and deserialize input
        try:
            # Load the data into a new PosService instance
            updated_service = service_schema.load(json_data, partial=True)
            
            # Get the fields that were provided in the request
            provided_fields = json_data.keys()
            
            # Update only the fields that were provided in the request
            for field in provided_fields:
                if hasattr(updated_service, field):
                    setattr(service, field, getattr(updated_service, field))
        except ValidationError as err:
            return jsonify({
                'success': False,
                'message': 'Validation error',
                'errors': err.messages
            }), 400
        
        # Set updated_at timestamp
        service.updated_at = datetime.now()
        
        db.session.commit()
        
        # Log the service update
        logger.info(f"Service updated: ID={service.id}, Name={service.name}")
        
        return jsonify({
            'success': True,
            'message': 'Service updated successfully',
            'data': {
                'service': service_schema.dump(service)
            }
        }), 200
        
    except ValidationError as err:
        return jsonify({'message': 'Validation error', 'errors': err.messages}), 400
    except SQLAlchemyError as e:
        db.session.rollback()
        return jsonify({'message': 'Database error', 'error': str(e)}), 500

@service_api_bp.route('/<int:id>', methods=['DELETE'])
@token_required
@store_context_required
def delete_service(id):
    """Delete a service"""
    try:
        # Find the service (within the current store context)
        service = PosService.query.filter(
            PosService.id == id,
            PosService.store_id == g.store_id
        ).first()
        
        if not service:
            return jsonify({
                'success': False,
                'message': 'Service not found'
            }), 404
        
        # Store service info for logging before deletion
        service_id = service.id
        service_name = service.name
        
        # Delete the service
        db.session.delete(service)
        db.session.commit()
        
        # Log the service deletion
        logger.info(f"Service deleted: ID={service_id}, Name={service_name}")
        
        return jsonify({
            'success': True,
            'message': 'Service deleted successfully'
        }), 200
        
    except SQLAlchemyError as e:
        db.session.rollback()
        return jsonify({'message': 'Database error', 'error': str(e)}), 500