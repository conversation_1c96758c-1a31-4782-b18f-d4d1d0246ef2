'use client';

import { useEffect } from 'react';
import usePosStore from '@/lib/store';
import { Box, Chip, Typography } from '@mui/material';
import StoreIcon from '@mui/icons-material/Store';

interface StoreContextProps {
  showFullInfo?: boolean;
  className?: string;
}

export default function StoreContext({ showFullInfo = false, className = '' }: StoreContextProps) {
  const { authUser, stores, fetchStores } = usePosStore();

  useEffect(() => {
    if (stores.length === 0) {
      fetchStores();
    }
  }, [stores.length, fetchStores]);

  if (!authUser.store_id) {
    return null;
  }

  const currentStore = stores.find(store => store.id === authUser.store_id) || {
    id: authUser.store_id,
    name: authUser.store_name || `Store ${authUser.store_id}`,
    address: '',
    phone: ''
  };

  if (showFullInfo) {
    return (
      <Box className={`p-4 bg-blue-50 border-l-4 border-blue-400 ${className}`}>
        <Box className="flex items-center space-x-2 mb-2">
          <StoreIcon className="text-blue-600" />
          <Typography variant="h6" className="text-blue-800 font-semibold">
            Current Store
          </Typography>
        </Box>
        <Typography variant="body1" className="font-medium text-gray-800">
          {currentStore.name}
        </Typography>
        {currentStore.address && (
          <Typography variant="body2" className="text-gray-600">
            {currentStore.address}
          </Typography>
        )}
        {currentStore.phone && (
          <Typography variant="body2" className="text-gray-600">
            {currentStore.phone}
          </Typography>
        )}
      </Box>
    );
  }

  return (
    <Chip
      icon={<StoreIcon />}
      label={currentStore.name}
      variant="outlined"
      size="small"
      className={`bg-blue-50 border-blue-200 text-blue-800 ${className}`}
    />
  );
}