from applications.extensions import ma
from applications.models.pos_resource import PosResource

class PosResourceSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = PosResource
        load_instance = True
        # Optionally specify fields or dump_only fields as needed
        # fields = ('id', 'name', 'type', 'capacity', 'hourly_rate', 'status', 'x', 'y', 'width', 'height', 'floor', 'zone', 'created_at', 'updated_at')
        # dump_only = ('id', 'created_at', 'updated_at')

    id = ma.auto_field(dump_only=True)
    name = ma.auto_field(required=True)
    type = ma.auto_field()
    capacity = ma.auto_field()
    hourly_rate = ma.auto_field() # Corresponds to hourlyRate in input JSON
    status = ma.auto_field(required=False) # Default is set in model
    x = ma.auto_field()
    y = ma.auto_field()
    width = ma.auto_field()
    height = ma.auto_field()
    floor = ma.auto_field()
    zone = ma.auto_field()
    created_at = ma.auto_field(dump_only=True)
    updated_at = ma.auto_field(dump_only=True)
