<!DOCTYPE html>
<html>
<head>
    <title>用户管理</title>
    {% include 'system/common/header.html' %}
</head>
<body>
<form class="layui-form">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item">
                    <label class="layui-form-label">账号</label>
                    <div class="layui-input-block">
                        <input type="text" name="username" lay-verify="title" autocomplete="off" placeholder="用户名"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">姓名</label>
                    <div class="layui-input-block">
                        <input type="text" name="realName" lay-verify="title" autocomplete="off" placeholder="用户姓名"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">密码</label>
                    <div class="layui-input-block">
                        <input type="password" name="password" lay-verify="title" autocomplete="off" placeholder="用户密码"
                               class="layui-input">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">部门</label>
                    <div class="layui-input-block">
                        <ul id="selectParent" name="deptId" class="dtree" data-id="0"></ul>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">角色</label>
                    <div class="layui-input-block">

                        {% for role in roles %}
                            <input
                                    value="{{ role.id }}" title="{{ role.name }}" type="checkbox"
                                    name="roleIds" lay-skin="primary">
                        {% endfor %}

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button type="submit" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="user-save">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
</form>
{% include 'system/common/footer.html' %}
<script>
  layui.use(['form', 'jquery', 'dtree'], function () {
    let form = layui.form
    let $ = layui.jquery

    let dtree = layui.dtree

    dtree.renderSelect({
        elem: '#selectParent',
        url: '/system/dept/tree',
        method: 'get',
        selectInputName: {nodeId: 'deptId', context: 'deptName'},
        skin: 'layui',
        dataFormat: 'list',
        response: {treeId: 'id', parentId: 'parent_id', title: 'dept_name'}
    })

    form.on('submit(user-save)', function (data) {
      let roleIds = ''
      $('input[type=checkbox]:checked').each(function () {
        roleIds += $(this).val() + ','
      })
      roleIds = roleIds.substr(0, roleIds.length - 1)
      data.field.roleIds = roleIds

      $.ajax({
        url: '/system/user/save',
        data: JSON.stringify(data.field),
        dataType: 'json',
        contentType: 'application/json',
        type: 'post',
        success: function (result) {
          if (result.success) {
            layer.msg(result.msg, { icon: 1, time: 1000 }, function () {
              parent.layer.close(parent.layer.getFrameIndex(window.name))//关闭当前页
              parent.layui.table.reload('user-table')
            })
          } else {
            layer.msg(result.msg, { icon: 2, time: 1000 })
          }
        }
      })
      return false
    })
  })
</script>

</body>
</html>