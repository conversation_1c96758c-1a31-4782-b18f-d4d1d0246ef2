from flask import Blueprint, request, jsonify, g, url_for
from applications.extensions import db, ma
from applications.models.pos_resource import PosResource
from applications.schemas.pos_resource_schema import PosResourceSchema
from applications.api.auth_api import token_required, store_context_required
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import joinedload
from marshmallow import ValidationError
from datetime import datetime
import logging

# Set up logger
logger = logging.getLogger(__name__)

resource_api_bp = Blueprint('resource_api', __name__, url_prefix='/api/resources')

# Create schemas
resource_schema = PosResourceSchema()
resources_schema = PosResourceSchema(many=True)

@resource_api_bp.route('', methods=['POST'])
@token_required
@store_context_required
def create_resource():
    """Create a new resource
    
    Request Body:
    - name: Required, resource name
    - type: Optional, resource type
    - status: Optional, resource status
    - zone: Optional, resource zone
    - floor: Optional, resource floor
    - capacity: Optional, resource capacity
    - and other fields as defined in the PosResource model
    """
    try:
        # Validate request data
        json_data = request.get_json()
        if not json_data:
            return jsonify({
                'success': False,
                'message': 'No input data provided'
            }), 400
        
        # Validate and deserialize input
        try:
            data = resource_schema.load(json_data)
        except ValidationError as err:
            return jsonify({
                'success': False,
                'message': 'Validation error',
                'errors': err.messages
            }), 400
        
        # Create new resource and assign store_id
        resource = data
        resource.store_id = g.store_id  # Set store context from JWT token
        db.session.add(resource)
        db.session.commit()
        
        # Log the resource creation
        logger.info(f"Resource created: ID={resource.id}, Name={resource.name}, Type={resource.type}")
        
        return jsonify({
            'success': True,
            'message': 'Resource created successfully',
            'data': {
                'resource': resource_schema.dump(resource)
            }
        }), 201
        
    except ValidationError as err:
        return jsonify({'message': 'Validation error', 'errors': err.messages}), 400
    except SQLAlchemyError as e:
        db.session.rollback()
        return jsonify({'message': 'Database error', 'error': str(e)}), 500

@resource_api_bp.route('', methods=['GET'])
@token_required
@store_context_required
def get_resources():
    """Get all resources with optional filtering and pagination
    
    Query Parameters:
    - name: Filter by resource name (partial match)
    - type: Filter by resource type
    - status: Filter by resource status
    - zone: Filter by resource zone
    - floor: Filter by resource floor
    - min_capacity: Filter by minimum capacity
    - max_capacity: Filter by maximum capacity
    - available: Filter by availability (true/false)
    - page: Page number (default: 1)
    - per_page: Items per page (default: 20, max: 100)
    - sort_by: Field to sort by (default: name)
    - sort_order: Sort order ('asc' or 'desc', default: 'asc')
    """
    try:
        # Get query parameters for filtering
        name = request.args.get('name')
        type = request.args.get('type')
        status = request.args.get('status')
        zone = request.args.get('zone')
        floor = request.args.get('floor')
        min_capacity = request.args.get('min_capacity', type=int)
        max_capacity = request.args.get('max_capacity', type=int)
        available = request.args.get('available')
        
        # Pagination parameters
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)  # Limit max per_page to 100
        
        # Sorting parameters
        sort_by = request.args.get('sort_by', 'name')
        sort_order = request.args.get('sort_order', 'asc')
        
        # Validate sort parameters
        valid_sort_fields = ['id', 'name', 'type', 'status', 'zone', 'floor', 'capacity', 'created_at', 'updated_at']
        if sort_by not in valid_sort_fields:
            return jsonify({
                'success': False,
                'message': f'Invalid sort_by parameter. Valid options are: {valid_sort_fields}'
            }), 400
            
        if sort_order not in ['asc', 'desc']:
            return jsonify({
                'success': False,
                'message': 'Invalid sort_order parameter. Valid options are: asc, desc'
            }), 400
        
        # Build query with filters (filter by store first)
        query = PosResource.query.filter(PosResource.store_id == g.store_id)
        
        if name:
            query = query.filter(PosResource.name.ilike(f'%{name}%'))
        if type:
            query = query.filter(PosResource.type == type)
        if status:
            query = query.filter(PosResource.status == status)
        if zone:
            query = query.filter(PosResource.zone == zone)
        if floor:
            query = query.filter(PosResource.floor == floor)
        if min_capacity is not None:
            query = query.filter(PosResource.capacity >= min_capacity)
        if max_capacity is not None:
            query = query.filter(PosResource.capacity <= max_capacity)
        if available is not None:
            available_bool = available.lower() == 'true'
            query = query.filter(PosResource.available == available_bool)
        
        # Apply sorting
        sort_column = getattr(PosResource, sort_by)
        if sort_order == 'desc':
            query = query.order_by(sort_column.desc())
        else:
            query = query.order_by(sort_column.asc())
        
        # Get paginated results
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        resources = pagination.items
        
        # Log the query for monitoring
        logger.info(f"Resources query: page={page}, per_page={per_page}, filters applied: {request.args}")
        
        # Build pagination metadata
        meta = {
            'page': page,
            'per_page': per_page,
            'total_pages': pagination.pages,
            'total_items': pagination.total
        }
        
        # Add pagination links
        links = {}
        if pagination.has_prev:
            links['prev'] = url_for('resource_api.get_resources', page=page-1, per_page=per_page, **request.args)
        if pagination.has_next:
            links['next'] = url_for('resource_api.get_resources', page=page+1, per_page=per_page, **request.args)
        links['first'] = url_for('resource_api.get_resources', page=1, per_page=per_page, **request.args)
        links['last'] = url_for('resource_api.get_resources', page=pagination.pages, per_page=per_page, **request.args)
        
        return jsonify({
            'success': True,
            'data': {
                'items': resources_schema.dump(resources),
                'meta': meta,
                'links': links
            }
        }), 200
        
    except SQLAlchemyError as e:
        return jsonify({'message': 'Database error', 'error': str(e)}), 500

@resource_api_bp.route('/<int:id>', methods=['GET'])
@token_required
@store_context_required
def get_resource(id):
    """Get a specific resource by ID"""
    try:
        resource = PosResource.query.filter(
            PosResource.id == id,
            PosResource.store_id == g.store_id
        ).first()
        
        if not resource:
            return jsonify({
                'success': False,
                'message': 'Resource not found'
            }), 404
        
        return jsonify({
            'success': True,
            'data': {
                'resource': resource_schema.dump(resource)
            }
        }), 200
        
    except SQLAlchemyError as e:
        return jsonify({'message': 'Database error', 'error': str(e)}), 500

@resource_api_bp.route('/<int:id>', methods=['PUT'])
@token_required
@store_context_required
def update_resource(id):
    """Update a specific resource"""
    try:
        # Find the resource (within the current store context)
        resource = PosResource.query.filter(
            PosResource.id == id,
            PosResource.store_id == g.store_id
        ).first()
        
        if not resource:
            return jsonify({
                'success': False,
                'message': 'Resource not found'
            }), 404
        
        # Validate request data
        json_data = request.get_json()
        if not json_data:
            return jsonify({
                'success': False,
                'message': 'No input data provided'
            }), 400
        
        # Validate and deserialize input
        try:
            # Load the data into a new PosResource instance
            updated_resource = resource_schema.load(json_data, partial=True)
            
            # Get the fields that were provided in the request
            provided_fields = json_data.keys()
            
            # Update only the fields that were provided in the request
            for field in provided_fields:
                if hasattr(updated_resource, field):
                    setattr(resource, field, getattr(updated_resource, field))
        except ValidationError as err:
            return jsonify({
                'success': False,
                'message': 'Validation error',
                'errors': err.messages
            }), 400
        
        # Set updated_at timestamp
        resource.updated_at = datetime.now()
        
        db.session.commit()
        
        # Log the resource update
        logger.info(f"Resource updated: ID={resource.id}, Name={resource.name}")
        
        return jsonify({
            'success': True,
            'message': 'Resource updated successfully',
            'data': {
                'resource': resource_schema.dump(resource)
            }
        }), 200
        
    except ValidationError as err:
        return jsonify({'message': 'Validation error', 'errors': err.messages}), 400
    except SQLAlchemyError as e:
        db.session.rollback()
        return jsonify({'message': 'Database error', 'error': str(e)}), 500

@resource_api_bp.route('/<int:id>', methods=['DELETE'])
@token_required
@store_context_required
def delete_resource(id):
    """Delete a resource"""
    try:
        # Find the resource (within the current store context)
        resource = PosResource.query.filter(
            PosResource.id == id,
            PosResource.store_id == g.store_id
        ).first()
        
        if not resource:
            return jsonify({
                'success': False,
                'message': 'Resource not found'
            }), 404
        
        # Store resource info for logging before deletion
        resource_id = resource.id
        resource_name = resource.name
        
        # Delete the resource
        db.session.delete(resource)
        db.session.commit()
        
        # Log the resource deletion
        logger.info(f"Resource deleted: ID={resource_id}, Name={resource_name}")
        
        return jsonify({
            'success': True,
            'message': 'Resource deleted successfully'
        }), 200
        
    except SQLAlchemyError as e:
        db.session.rollback()
        return jsonify({'message': 'Database error', 'error': str(e)}), 500
