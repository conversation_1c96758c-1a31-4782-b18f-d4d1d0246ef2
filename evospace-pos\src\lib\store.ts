import { create } from 'zustand';
import {
  products as mockProducts,
  categories as mockCategories,
  resources as mockResources,
  users as mockUsers,
  services as mockServices
} from './mockData';
import type { Session, SessionItem, CartItem, Transaction as AppTransaction, ResourceStatus, Member, Product, Resource, Category, Service, LoginCredentials, User, Store } from '@/lib/types';
import { apiService } from './apiService';

// Utility function for handling API calls with fallback to mock data
const handleApiWithFallback = async <T>(
  apiCall: () => Promise<{ success: boolean; data: { items?: T[] } }>,
  mockData: T[],
  errorMessage: string
): Promise<T[]> => {
  try {
    const response = await apiCall();

    if (response.success && response.data.items && response.data.items.length > 0) {
      return response.data.items;
    } else {
      console.warn(`API returned no ${errorMessage}, using mock data`);
      return mockData;
    }
  } catch (error) {
    console.error(`Error fetching ${errorMessage}:`, error);
    return mockData;
  }
};

// Utility function for handling transaction status updates
const updateTransactionStatus = async <T extends { id: number; status: string }>(
  id: number,
  apiCall: () => Promise<{ success: boolean; data: { transaction: T } }>,
  newStatus: 'completed' | 'cancelled' | 'refunded',
  errorMessage: string,
  state: { transactions: T[] },
  setState: (fn: (state: { transactions: T[] }) => { transactions: T[] }) => void
): Promise<T | undefined> => {
  try {
    const response = await apiCall();

    if (response.success && response.data.transaction) {
      setState(state => ({
        transactions: state.transactions.map(t =>
          t.id === id ? response.data.transaction : t
        )
      }));
      return response.data.transaction;
    } else {
      // Fallback if API fails
      setState(state => ({
        transactions: state.transactions.map(t =>
          t.id === id ? { ...t, status: newStatus } as T : t
        )
      }));
      return state.transactions.find(t => t.id === id);
    }
  } catch (error) {
    console.error(`Error ${errorMessage} transaction ${id}:`, error);
    // Fallback if API fails
    setState(state => ({
      transactions: state.transactions.map(t =>
        t.id === id ? { ...t, status: newStatus } as T : t
      )
    }));
    return state.transactions.find(t => t.id === id);
  }
};

// Utility function for adding items to active sessions
const addItemToActiveSession = async <T extends { id: number; name: string; price: number }>(
  resource_id: number,
  itemData: T,
  itemType: 'product' | 'service',
  getActiveSession: (resource_id: number) => Session | undefined,
  apiCall: (sessionId: number, data: unknown) => Promise<unknown>,
  setState: (fn: (state: { activeSessions: Session[] }) => { activeSessions: Session[] }) => void
): Promise<void> => {
  const session = getActiveSession(resource_id);
  if (!session) {
    console.error(`No active session found for resource ${resource_id}`);
    return;
  }

  // Prepare item data for API
  const itemToAdd = itemType === 'product'
    ? {
        product_id: itemData.id,
        quantity: 1,
        price: itemData.price
      }
    : {
        service_id: itemData.id,
        unit: 'hour', // Default unit
        quantity: 1,
        price: itemData.price
      };

  try {
    // Call API to add item to session
    await apiCall(session.id, itemToAdd);

    // Update local state
    setState(state => ({
      activeSessions: state.activeSessions.map(s => {
        if (s.id === session.id) {
          const items = itemType === 'product' ? s.products : s.services;
          const existingItem = items.find((item: SessionItem) => item.id === itemData.id);
          let updatedItems;

          if (existingItem) {
            updatedItems = items.map((item: SessionItem) =>
              item.id === itemData.id ? { ...item, quantity: item.quantity + 1 } : item
            );
          } else {
            updatedItems = [...items, {
              id: itemData.id,
              type: itemType,
              name: itemData.name,
              price: itemData.price,
              quantity: 1,
              [`${itemType}_id`]: itemData.id
            }];
          }

          return itemType === 'product'
            ? { ...s, products: updatedItems }
            : { ...s, services: updatedItems };
        }
        return s;
      })
    }));
  } catch (error) {
    console.error(`Error adding ${itemType} ${itemData.id} to session ${session.id}:`, error);
    // Still update local state for offline functionality
    setState(state => ({
      activeSessions: state.activeSessions.map(s => {
        if (s.id === session.id) {
          const items = itemType === 'product' ? s.products : s.services;
          const existingItem = items.find((item: SessionItem) => item.id === itemData.id);
          let updatedItems;

          if (existingItem) {
            updatedItems = items.map((item: SessionItem) =>
              item.id === itemData.id ? { ...item, quantity: item.quantity + 1 } : item
            );
          } else {
            updatedItems = [...items, {
              id: itemData.id,
              type: itemType,
              name: itemData.name,
              price: itemData.price,
              quantity: 1,
              [`${itemType}_id`]: itemData.id
            }];
          }

          return itemType === 'product'
            ? { ...s, products: updatedItems }
            : { ...s, services: updatedItems };
        }
        return s;
      })
    }));
  }
};

interface UserAuthState {
  username: string | null;
  token: string | null;
  store_id: number | null;
  store_name: string | null;
}

interface PosState {
  authUser: UserAuthState;
  currentUser: User;
  isAuthenticated: boolean;
  
  // Store management
  stores: Store[];
  fetchStores: () => Promise<void>;
  
  setUser: (user: UserAuthState) => void;
  loadAuthToken: () => void;
  login: (username: string, password: string, store_id: number) => Promise<boolean>;
  logout: () => void;

  activeSessions: Session[];
  startSession: (resource_id: number, memberId?: number, notes?: string) => Promise<number>;
  endSession: (sessionId: number, notes?: string) => Promise<void>;
  getActiveSession: (resource_id: number) => Session | undefined;
  fetchActiveSessions: () => Promise<void>;


  products: Product[];
  getProduct: (id: number) => Product | undefined;
  addProduct: (product: Omit<Product, 'id'>) => Promise<void>;
  updateProduct: (id: number, data: Partial<Product>) => Promise<void>;
  deleteProduct: (id: number) => Promise<void>;
  fetchProducts: () => Promise<void>;

  categories: Category[];
  getCategory: (id: number) => Category | undefined;

  services: Service[];
  getService: (id: number) => Service | undefined;
  addService: (service: Omit<Service, 'id'>) => Promise<void>;
  updateService: (id: number, data: Partial<Service>) => Promise<void>;
  deleteService: (id: number) => Promise<void>;
  fetchServices: () => Promise<void>;

  resources: Resource[];
  getResource: (id: number) => Resource | undefined;
  addResource: (resourceData: Omit<Resource, 'id'>) => Promise<void>;
  updateResource: (id: number, resourceData: Partial<Resource>) => Promise<void>;
  updateResourceStatus: (id: number, status: ResourceStatus) => Promise<void>; // Make async for consistency
  setResources: (newResources: Resource[]) => void;
  deleteResource: (id: number) => Promise<void>; // Make async for consistency
  fetchResources: () => Promise<void>;

  members: Member[];
  getMember: (id: number) => Member | undefined;
  addMember: (member: Omit<Member, 'id' | 'visits' | 'totalSpent'>) => Promise<void>;
  updateMember: (id: number, data: Partial<Omit<Member, 'id' | 'visits' | 'totalSpent'>>) => Promise<void>;
  deleteMember: (id: number) => Promise<void>;
  fetchMembers: () => Promise<void>;

  transactions: AppTransaction[];
  fetchTransactions: () => Promise<void>;
  addTransaction: (transaction: Omit<AppTransaction, 'id'>) => Promise<AppTransaction>;
  completeTransaction: (id: number) => Promise<AppTransaction | undefined>;
  cancelTransaction: (id: number, reason?: string) => Promise<AppTransaction | undefined>;

  cart: CartItem[];
  addToCart: (item: Omit<CartItem, 'quantity'>) => void;
  removeFromCart: (id: number) => void;
  clearCart: () => void;
  cartTotal: () => number;

  selectedMember: Member | null;
  setSelectedMember: (member: Member | null) => void;

  addServiceToActiveSession: (resource_id: number, serviceData: { id: number; name: string; price: number; }) => void;
  addProductToActiveSession: (resource_id: number, productData: { id: number; name: string; price: number; }) => void;
}

const usePosStore = create<PosState>((set, get) => ({
  authUser: {
    username: null,
    token: null,
    store_id: null,
    store_name: null,
  },
  
  // Store management
  stores: [],
  fetchStores: async () => {
    try {
      const response = await apiService.auth.getStores();
      if (response.success && response.data && response.data.items) {
        set({ stores: response.data.items });
      }
    } catch (error) {
      console.error('Error fetching stores:', error);
      // Fallback to mock stores if needed
      set({ stores: [
        { id: 1, name: 'Main Store', address: '123 Main St', phone: '555-0100' },
        { id: 2, name: 'Branch Store', address: '456 Oak Ave', phone: '555-0200' }
      ]});
    }
  },
  setUser: (userAuth) => set({ authUser: userAuth }),
  loadAuthToken: () => {
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('authToken');
      const storeId = localStorage.getItem('storeId');
      const storeName = localStorage.getItem('storeName');
      if (token) {
        set(state => ({
          authUser: {
            ...state.authUser,
            token,
            store_id: storeId ? parseInt(storeId) : null,
            store_name: storeName || null
          }
        }));
      }
    }
  },

  currentUser: mockUsers[0],
  isAuthenticated: false,
  login: async (username: string, password: string, store_id: number): Promise<boolean> => {
    try {
      const credentials: LoginCredentials = { username, password, store_id };
      const response = await apiService.auth.login(credentials);

      if (response.success && response.data) {
        const { user, token } = response.data;

        // Set user in state
        set({
          currentUser: user,
          isAuthenticated: true,
          authUser: {
            username: user.username,
            token: token,
            store_id: user.store_id || store_id,
            store_name: user.store_name || null
          }
        });

        // Store auth data in localStorage
        if (typeof window !== 'undefined') {
          localStorage.setItem('authToken', token);
          localStorage.setItem('storeId', String(user.store_id || store_id));
          localStorage.setItem('storeName', user.store_name || '');
        }

        return true;
      }

      return false;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  },
  logout: () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('authToken');
      localStorage.removeItem('storeId');
      localStorage.removeItem('storeName');
    }
    set({
      currentUser: mockUsers[0],
      isAuthenticated: false,
      authUser: {
        username: null,
        token: null,
        store_id: null,
        store_name: null
      }
    });
  },

  products: [],
  getProduct: (id) => get().products.find(p => p.id === id),
  fetchProducts: async () => {
    const products = await handleApiWithFallback(
      () => apiService.products.getProducts(),
      mockProducts,
      'products'
    );
    set({ products });
  },
  addProduct: async (productData) => {
    try {
      const response = await apiService.products.createProduct(productData);

      if (response.success && response.data.product) {
        set(state => ({
          products: [...state.products, response.data.product]
        }));
      }
    } catch (error) {
      console.error('Error adding product:', error);
      throw error;
    }
  },
  updateProduct: async (id, productData) => {
    try {
      const response = await apiService.products.updateProduct(id, productData);

      if (response.success && response.data.product) {
        set(state => ({
          products: state.products.map(product =>
            product.id === id ? response.data.product : product
          )
        }));
      }
    } catch (error) {
      console.error(`Error updating product ${id}:`, error);
      throw error;
    }
  },
  deleteProduct: async (id) => {
    try {
      const response = await apiService.products.deleteProduct(id);

      if (response.success) {
        set(state => ({
          products: state.products.filter(product => product.id !== id)
        }));
      }
    } catch (error) {
      console.error(`Error deleting product ${id}:`, error);
      throw error;
    }
  },

  categories: mockCategories,
  getCategory: (id) => get().categories.find(c => c.id === id),

  services: [],
  getService: (id) => get().services.find(s => s.id === id),
  fetchServices: async () => {
    const services = await handleApiWithFallback(
      () => apiService.services.getServices(),
      mockServices,
      'services'
    );
    set({ services });
  },
  addService: async (serviceData) => {
    try {
      const response = await apiService.services.createService(serviceData);

      if (response.success && response.data.service) {
        set(state => ({
          services: [...state.services, response.data.service]
        }));
      }
    } catch (error) {
      console.error('Error adding service:', error);
      throw error;
    }
  },
  updateService: async (id, serviceData) => {
    try {
      const response = await apiService.services.updateService(id, serviceData);

      if (response.success && response.data.service) {
        set(state => ({
          services: state.services.map(service =>
            service.id === id ? response.data.service : service
          )
        }));
      }
    } catch (error) {
      console.error(`Error updating service ${id}:`, error);
      throw error;
    }
  },
  deleteService: async (id) => {
    try {
      const response = await apiService.services.deleteService(id);

      if (response.success) {
        set(state => ({
          services: state.services.filter(service => service.id !== id)
        }));
      }
    } catch (error) {
      console.error(`Error deleting service ${id}:`, error);
      throw error;
    }
  },

  resources: mockResources, // Initialized with mock data
  getResource: (id) => get().resources.find(r => r.id === id),
  fetchResources: async () => {
    const resources = await handleApiWithFallback(
      () => apiService.resources.getResources(),
      mockResources,
      'resources'
    );
    set({ resources });
  },
  addResource: async (resourceData) => {
    try {
      const response = await apiService.resources.createResource(resourceData);

      if (response.success && response.data.resource) {
        set(state => ({
          resources: [...state.resources, response.data.resource]
        }));
      }
    } catch (error) {
      console.error('Error adding resource:', error);
      throw error;
    }
  },
  updateResource: async (id, resourceData) => {
    try {
      const response = await apiService.resources.updateResource(id, resourceData);

      if (response.success && response.data.resource) {
        set(state => ({
          resources: state.resources.map(resource =>
            resource.id === id ? response.data.resource : resource
          )
        }));
      }
    } catch (error) {
      console.error(`Error updating resource ${id}:`, error);
      throw error;
    }
  },
  updateResourceStatus: async (id, status) => {
    try {
      // Update the resource with the new status
      const response = await apiService.resources.updateResource(id, { status });

      if (response.success && response.data.resource) {
        set(state => ({
          resources: state.resources.map(resource =>
            resource.id === id ? response.data.resource : resource
          )
        }));
      }
    } catch (error) {
      console.error(`Error updating resource status ${id}:`, error);
      throw error;
    }
  },
  setResources: (newResources) => set({ resources: newResources }),
  deleteResource: async (id) => {
    try {
      const response = await apiService.resources.deleteResource(id);

      if (response.success) {
        set(state => ({
          resources: state.resources.filter(resource => resource.id !== id)
        }));
      }
    } catch (error) {
      console.error(`Error deleting resource ${id}:`, error);
      throw error;
    }
  },

  members: [],
  getMember: (id) => get().members.find(m => m.id === id),
  addMember: async (memberData) => {
    try {
      const response = await apiService.members.createMember(memberData);

      if (response.success && response.data.member) {
        set(state => ({
          members: [...state.members, response.data.member]
        }));
      }
    } catch (error) {
      console.error('Error adding member:', error);
      throw error;
    }
  },
  updateMember: async (id, data) => {
    try {
      const response = await apiService.members.updateMember(id, data);

      if (response.success && response.data.member) {
        set(state => ({
          members: state.members.map(member =>
            member.id === id ? response.data.member : member
          )
        }));
      }
    } catch (error) {
      console.error(`Error updating member ${id}:`, error);
      throw error;
    }
  },
  deleteMember: async (id) => {
    try {
      const response = await apiService.members.deleteMember(id);

      if (response.success) {
        set(state => ({
          members: state.members.filter(member => member.id !== id)
        }));
      }
    } catch (error) {
      console.error(`Error deleting member ${id}:`, error);
      throw error;
    }
  },
  fetchMembers: async () => {
    // Import members dynamically to avoid circular dependencies
    const { members: mockMembers } = await import('./mockData');
    const members = await handleApiWithFallback(
      () => apiService.members.getMembers(),
      mockMembers,
      'members'
    );
    set({ members });
  },

  transactions: [],
  fetchTransactions: async () => {
    try {
      const response = await apiService.transactions.getTransactions();

      if (response.success && response.data.items) {
        set({ transactions: response.data.items });
      } else {
        // Keep existing transactions if API fails
        console.warn('API returned no transactions');
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
      // Keep existing transactions if API fails
    }
  },

  addTransaction: async (transaction) => {
    try {
      // Format transaction data according to API requirements
      const transactionData = {
        member_id: transaction.memberId || null,
        subtotal: transaction.subtotal || transaction.totalAmount,
        tax_amount: transaction.tax_amount || 0,
        tax_rate: transaction.tax_rate || 0,
        discount_amount: transaction.discount_amount || 0,
        discount_rate: transaction.discount_rate || 0,
        total_amount: transaction.totalAmount,
        actual_amount: transaction.actual_amount || transaction.totalAmount,
        payment_method: transaction.paymentMethod,
        payment_reference: transaction.payment_reference || '',
        notes: transaction.notes || '',
        sessions: transaction.sessions || []
      };

      const response = await apiService.transactions.createTransaction(transactionData);

      if (response.success && response.data.transaction) {
        set(state => ({
          transactions: [...state.transactions, response.data.transaction]
        }));

        // Clear the cart after successful transaction
        get().clearCart();

        return response.data.transaction;
      } else {
        // Fallback if API fails
        const newId = Math.max(0, ...get().transactions.map(t => t.id)) + 1;
        const newTransaction: AppTransaction = {
          id: newId,
          ...transaction,
          createdAt: new Date().toISOString(),
          status: 'pending' as 'pending' | 'completed' | 'cancelled' | 'refunded'
        };
        set(state => ({
          transactions: [...state.transactions, newTransaction]
        }));

        // Clear the cart after transaction
        get().clearCart();

        return newTransaction;
      }
    } catch (error) {
      console.error('Error adding transaction:', error);
      // Fallback if API fails
      const newId = Math.max(0, ...get().transactions.map(t => t.id)) + 1;
      const newTransaction: AppTransaction = {
        id: newId,
        ...transaction,
        createdAt: new Date().toISOString(),
        status: 'pending' as 'pending' | 'completed' | 'cancelled' | 'refunded'
      };
      set(state => ({
        transactions: [...state.transactions, newTransaction]
      }));

      // Clear the cart after transaction
      get().clearCart();

      return newTransaction;
    }
  },

  completeTransaction: async (id: number) => {
    return updateTransactionStatus(
      id,
      () => apiService.transactions.completeTransaction(id),
      'completed',
      'completing',
      get(),
      set
    );
  },

  cancelTransaction: async (id: number, reason = 'Cancelled by user') => {
    return updateTransactionStatus(
      id,
      () => apiService.transactions.cancelTransaction(id, reason),
      'cancelled',
      'cancelling',
      get(),
      set
    );
  },

  cart: [],
  addToCart: (item) => {
    const { cart } = get();
    const existingItem = cart.find(i => i.id === item.id);
    if (!existingItem) {
      set(state => ({ cart: [...state.cart, { ...item }] }));
    }
  },
  removeFromCart: (id) => {
    set(state => ({ cart: state.cart.filter(item => !(item.id === id)) }));
  },
  clearCart: () => set({ cart: [] }),
  cartTotal: () => {
    const { cart, getActiveSession } = get();
    return cart.reduce((total, item) => {
      const session = getActiveSession(item.id);
      if (session) {
        // Calculate resource time cost
        const durationInMinutes = (Date.now() - new Date(session.start_time).getTime()) / (1000 * 60);
        const roundedMinutes = Math.ceil(durationInMinutes / 10) * 10;
        const durationInHours = roundedMinutes / 60;
        const resourceCost = durationInHours * item.price;

        // Calculate products cost with proper type handling
        const productsCost = session.products.reduce((sum, product) => {
          return sum + (product.price * product.quantity);
        }, 0);

        // Calculate services cost with proper type handling
        const servicesCost = session.services.reduce((sum, service) => {
          return sum + (service.price * service.quantity);
        }, 0);

        // Return the sum of resource cost, products cost, and services cost
        return total + resourceCost + productsCost + servicesCost;
      }
      return total;
    }, 0);
  },

  selectedMember: null,
  setSelectedMember: (member: Member | null) => set({ selectedMember: member }),

  activeSessions: [],
  fetchActiveSessions: async () => {
    try {
      const response = await apiService.sessions.getSessions();

      if (response.success && response.data.items) {
        // Map API response to our Session type, filtering only active sessions
        const sessions = response.data.items
          .filter(apiSession => apiSession.status === 'active' || apiSession.status === 'open')
          .map(apiSession => {
          // Process products from the API response
          const products = Array.isArray(apiSession.products)
            ? apiSession.products.map(product => ({
                id: product.id,
                type: 'product' as const,
                name: product.name,
                price: product.price,
                quantity: product.quantity,
              }))
            : [];

          // Process services from the API response
          const services = Array.isArray(apiSession.services)
            ? apiSession.services.map(service => ({
                id: service.id,
                type: 'service' as const,
                name: service.name,
                price: service.price,
                quantity: service.quantity,
              }))
            : [];

          // Convert API response to match our Session interface
          const session: Session = {
            id: apiSession.id,
            resource_id: apiSession.resource_id || 0,
            user_id: apiSession.user_id,
            member_id: apiSession.member_id,
            start_time: apiSession.start_time || new Date().toISOString(),
            end_time: undefined,
            status: apiSession.status as 'active' | 'completed' | 'cancelled' | 'open' | 'closed',
            products: products,
            services: services,
            notes: apiSession.notes
          };

          return session;
        });

        set({ activeSessions: sessions });
      } else {
        console.warn('API returned no active sessions');
      }
    } catch (error) {
      console.error('Error fetching active sessions:', error);
    }
  },

  startSession: async (resource_id, memberId, notes) => {
    try {
      // Create a session via API
      const sessionData = {
        resource_id: resource_id,
        member_id: memberId,
        notes: notes || `Session for resource ${resource_id}`
      };

      const response = await apiService.sessions.createSession(sessionData);

      if (response.success && response.data.session) {
        // Create a session with the API response data
        const sessionId = response.data.session.id;

        // Create a session object with API data
        const session: Session = {
          id: sessionId,
          resource_id,
          user_id: response.data.session.user_id,
          member_id: memberId,
          start_time: response.data.session.start_time || new Date().toISOString(),
          status: 'open',
          products: [],  // Start with empty products
          services: [],  // Start with empty services
          notes: notes
        };

        // Update resource status to 'in-use'
        await get().updateResourceStatus(resource_id, 'in-use');

        // Add session to active sessions
        set(state => ({ activeSessions: [...state.activeSessions, session] }));
        return sessionId;
      } else {
        // Fallback to local session creation if API fails
        console.warn('API session creation failed, using local session');
        const sessionId = Date.now();

        const session: Session = {
          id: sessionId,
          resource_id,
          member_id: memberId,
          start_time: new Date().toISOString(),
          status: 'active',
          products: [],  // Start with empty products
          services: [],  // Start with empty services
          notes: notes
        };

        // Update resource status to 'in-use'
        await get().updateResourceStatus(resource_id, 'in-use');

        set(state => ({ activeSessions: [...state.activeSessions, session] }));
        return sessionId;
      }
    } catch (error) {
      console.error('Error starting session:', error);
      // Fallback to local session creation if API fails
      const sessionId = Date.now();

      const session: Session = {
        id: sessionId,
        resource_id,
        member_id: memberId,
        start_time: new Date().toISOString(),
        status: 'active',
        products: [],  // Start with empty products
        services: [],  // Start with empty services
        notes: notes
      };

      // Update resource status to 'in-use'
      try {
        await get().updateResourceStatus(resource_id, 'in-use');
      } catch (err) {
        console.error('Error updating resource status:', err);
      }

      set(state => ({ activeSessions: [...state.activeSessions, session] }));
      return sessionId;
    }
  },

  endSession: async (sessionId, notes) => {
    try {
      // Find the session in the local state
      const session = get().activeSessions.find(s => s.id === sessionId);
      if (!session) {
        console.error(`Session with ID ${sessionId} not found`);
        return;
      }

      // Try to close the session via API
      const response = await apiService.sessions.closeSession(sessionId, {
        status: 'closed',
        notes: notes || `Session for resource ${session.resource_id} ended`
      });

      if (response.success && response.data.session) {
        // Remove the session from activeSessions since it's now closed
        set(state => ({
          activeSessions: state.activeSessions.filter(s => s.id !== sessionId)
        }));

        // Update resource status back to 'available'
        await get().updateResourceStatus(session.resource_id, 'available');
      } else {
        // Fallback to local update if API fails - remove the session
        set(state => ({
          activeSessions: state.activeSessions.filter(s => s.id !== sessionId)
        }));

        // Update resource status back to 'available'
        await get().updateResourceStatus(session.resource_id, 'available');
      }
    } catch (error) {
      console.error(`Error ending session ${sessionId}:`, error);
      // Fallback to local update if API fails - remove the session
      set(state => ({
        activeSessions: state.activeSessions.filter(s => s.id !== sessionId)
      }));

      // Try to update resource status back to 'available'
      try {
        const session = get().activeSessions.find(s => s.id === sessionId);
        if (session) {
          await get().updateResourceStatus(session.resource_id, 'available');
        }
      } catch (err) {
        console.error('Error updating resource status:', err);
      }
    }
  },

  getActiveSession: (resource_id) => {
    return get().activeSessions.find(session =>
      session.resource_id === resource_id &&
      (session.status === 'active' || session.status === 'open')
    );
  },

  addServiceToActiveSession: async (resource_id, serviceData) => {
    // Create a wrapper function with compatible signature
    const addServiceWrapper = (sessionId: number, data: unknown) => {
      // Define the expected shape of the data
      interface ServiceData {
        service_id: number;
        unit: string;
        quantity: number;
        price: number;
      }

      // Cast to the expected type
      const typedData = data as ServiceData;

      return apiService.sessions.addServiceToSession(sessionId, {
        service_id: typedData.service_id,
        unit: typedData.unit,
        quantity: typedData.quantity,
        price: typedData.price
      });
    };

    return addItemToActiveSession(
      resource_id,
      serviceData,
      'service',
      get().getActiveSession,
      addServiceWrapper,
      set
    );
  },
  addProductToActiveSession: async (resource_id, productData) => {
    // Create a wrapper function with compatible signature
    const addProductWrapper = (sessionId: number, data: unknown) => {
      // Define the expected shape of the data
      interface ProductData {
        product_id: number;
        quantity: number;
        price: number;
      }

      // Cast to the expected type
      const typedData = data as ProductData;

      return apiService.sessions.addProductToSession(sessionId, {
        product_id: typedData.product_id,
        quantity: typedData.quantity,
        price: typedData.price
      });
    };

    return addItemToActiveSession(
      resource_id,
      productData,
      'product',
      get().getActiveSession,
      addProductWrapper,
      set
    );
  }
}));

export default usePosStore;

