"use client";

import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  InputAdornment,
  MenuItem,
  Stack,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Chip,
  FormControl,
  InputLabel,
  Select,
  FormHelperText
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Save as SaveIcon,
  ShoppingCart,
  Inventory,
  Image
} from '@mui/icons-material';
import usePosStore from '@/lib/store';
import { useTheme } from '@/lib/theme';
import ConfirmDialog from '@/components/ConfirmDialog';

interface ProductFormData {
  id?: number;
  name: string;
  category: string;
  price: string;
  stock: string;
  image: string;
}

export default function ProductsPage() {
  const { products, categories, addProduct, updateProduct, deleteProduct, fetchProducts, authUser } = usePosStore();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [openDialog, setOpenDialog] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    category: '',
    price: '',
    stock: '',
    image: ''
  });
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<number | null>(null);
  const { theme } = useTheme();

  useEffect(() => {
    if (authUser.token) {
      fetchProducts();
    }
  }, [authUser.token, fetchProducts]);

  // Filter products based on search query and category
  const filteredProducts = products.filter(product => 
    (product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.category.toLowerCase().includes(searchQuery.toLowerCase())) &&
    (selectedCategory === 'all' || product.category === selectedCategory)
  );

  const handleOpenDialog = (edit = false, product?: typeof products[0]) => {
    if (edit && product) {
      setFormData({
        id: product.id,
        name: product.name,
        category: product.category,
        price: product.price.toString(),
        stock: product.stock.toString(),
        image: product.image || ''
      });
      setEditMode(true);
    } else {
      setFormData({
        name: '',
        category: categories[0]?.name || '',
        price: '',
        stock: '',
        image: ''
      });
      setEditMode(false);
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async () => {
    const productData = {
      name: formData.name,
      category: formData.category,
      price: parseFloat(formData.price),
      stock: parseInt(formData.stock),
      image: formData.image
    };

    try {
      if (editMode && formData.id !== undefined) {
        await updateProduct(formData.id, productData);
      } else {
        await addProduct(productData);
      }
      handleCloseDialog();
    } catch (error) {
      console.error("Failed to save product:", error);
      // Optionally, show an error message to the user
    }
  };

  const handleDeleteClick = (productId: number) => {
    setProductToDelete(productId);
    setDeleteConfirmOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (productToDelete !== null) {
      try {
        await deleteProduct(productToDelete);
        setDeleteConfirmOpen(false);
        setProductToDelete(null);
      } catch (error) {
        console.error("Failed to delete product:", error);
        // Optionally, show an error message to the user
      }
    }
  };

  const handleCancelDelete = () => {
    setDeleteConfirmOpen(false);
    setProductToDelete(null);
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Products</Typography>
        <Button 
          variant="contained" 
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Add Product
        </Button>
      </Box>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
          <Box sx={{ display: 'flex', gap: 2, flexGrow: 1, maxWidth: { xs: '100%', sm: '70%' } }}>
            <TextField
              placeholder="Search products..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              fullWidth
            />
            
            <FormControl sx={{ minWidth: '200px' }}>
              <InputLabel>Category</InputLabel>
              <Select
                value={selectedCategory}
                label="Category"
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                <MenuItem value="all">All Categories</MenuItem>
                {Array.from(new Set(products.map(p => p.category))).sort().map(category => (
                  <MenuItem key={category} value={category}>{category}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </Box>
      </Paper>

      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
        {filteredProducts.length > 0 ? (
          filteredProducts.map((product) => (
            <Box key={product.id} sx={{ flexBasis: { xs: '100%', sm: '45%', md: '30%', lg: '22%' } }}>
              <Card 
                elevation={2}
                sx={{ 
                  height: '100%', 
                  display: 'flex', 
                  flexDirection: 'column',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 6
                  }
                }}
              >
                <CardMedia
                  sx={{
                    height: 160,
                    backgroundColor: product.image ? 'grey.200' : theme === 'dark' ? 'primary.dark' : 'primary.main',
                    color: 'white',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative'
                  }}
                >
                  {product.image ? (
                    <Box
                      component="img"
                      sx={{
                        height: 140,
                        width: '100%',
                        objectFit: 'cover',
                        borderRadius: 1,
                      }}
                      src={product.image || '/placeholder.jpg'}
                      alt={product.name}
                    />
                  ) : (
                    <Box sx={{ 
                      textAlign: 'center', 
                      p: 2,
                      width: '100%',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center'
                    }}>
                      <Typography 
                        variant="h5" 
                        component="div"
                        sx={{
                          fontWeight: 'bold',
                          textShadow: '1px 1px 3px rgba(0,0,0,0.5)',
                          mb: 1
                        }}
                      >
                        {product.name}
                      </Typography>
                    </Box>
                  )}
                </CardMedia>
                <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography gutterBottom variant="h6" component="div">
                      {product.name}
                    </Typography>
                    <Stack direction="row" spacing={1} sx={{ mb: 2, flexWrap: 'wrap', gap: 1 }}>
                      <Chip label={product.category} size="small" />
                      <Chip 
                        label={`Stock: ${product.stock}`} 
                        size="small"
                        color={product.stock > 10 ? "success" : product.stock > 0 ? "warning" : "error"}
                      />
                    </Stack>
                  </Box>
                  <Typography variant="h6" color="primary">
                    ${Number(product.price).toFixed(2)}
                  </Typography>
                </CardContent>
                <CardActions>
                  <Button 
                    size="small" 
                    startIcon={<EditIcon />}
                    onClick={() => handleOpenDialog(true, product)}
                  >
                    Edit
                  </Button>
                  <Button 
                    size="small" 
                    color="error" 
                    startIcon={<DeleteIcon />}
                    onClick={() => handleDeleteClick(product.id)}
                  >
                    Delete
                  </Button>
                </CardActions>
              </Card>
            </Box>
          ))
        ) : (
          <Box sx={{ width: '100%', p: 4, textAlign: 'center' }}>
            <Paper sx={{ p: 4, borderRadius: 2 }}>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No products found
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Try adjusting your search or category filter, or add a new product.
              </Typography>
              <Button 
                variant="contained" 
                startIcon={<AddIcon />} 
                sx={{ mt: 2 }}
                onClick={() => handleOpenDialog()}
              >
                Add Product
              </Button>
            </Paper>
          </Box>
        )}
      </Box>

      {/* Add/Edit Product Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {editMode ? (
              <EditIcon color="primary" />
            ) : (
              <AddIcon color="primary" />
            )}
            <Typography variant="h6">
              {editMode ? 'Edit Product' : 'Add New Product'}
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent dividers>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <TextField
              label="Product Name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              fullWidth
              required
              autoFocus
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <ShoppingCart fontSize="small" />
                  </InputAdornment>
                ),
              }}
            />
            <FormControl fullWidth required>
              <InputLabel>Category</InputLabel>
              <Select
                name="category"
                value={formData.category}
                label="Category"
                onChange={(e) => handleInputChange(e as React.ChangeEvent<HTMLInputElement>)}
              >
                {categories.map((category) => (
                  <MenuItem key={category.id} value={category.name}>
                    {category.name}
                  </MenuItem>
                ))}
              </Select>
              <FormHelperText>Select a product category</FormHelperText>
            </FormControl>
            <TextField
              label="Price"
              name="price"
              type="number"
              value={formData.price}
              onChange={handleInputChange}
              fullWidth
              required
              InputProps={{
                startAdornment: <InputAdornment position="start">$</InputAdornment>,
              }}
            />
            <TextField
              label="Stock"
              name="stock"
              type="number"
              value={formData.stock}
              onChange={handleInputChange}
              fullWidth
              required
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Inventory fontSize="small" />
                  </InputAdornment>
                ),
              }}
            />
            <TextField
              label="Image URL"
              name="image"
              value={formData.image}
              onChange={handleInputChange}
              fullWidth
              placeholder="https://example.com/image.jpg"
              helperText="Leave empty to use product name as display"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Image fontSize="small" aria-label="Product image URL" />
                  </InputAdornment>
                ),
              }}
            />
            {formData.image && (
              <Box sx={{ width: '100%', textAlign: 'center' }}>
                <Box
                  component="img"
                  src={formData.image}
                  alt={`Preview of ${formData.name || 'product'}`}
                  sx={{
                    maxWidth: '100%',
                    maxHeight: '150px',
                    objectFit: 'contain',
                    borderRadius: 1,
                    border: '1px solid',
                    borderColor: 'divider'
                  }}
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = '/placeholder.jpg';
                  }}
                />
                <Typography variant="caption" color="text.secondary" display="block">
                  Image Preview
                </Typography>
              </Box>
            )}
          </Stack>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button onClick={handleCloseDialog} color="inherit">Cancel</Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained"
            disabled={!formData.name || !formData.category || !formData.price || !formData.stock}
            startIcon={editMode ? <SaveIcon /> : <AddIcon />}
          >
            {editMode ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={deleteConfirmOpen}
        title="Confirm Delete"
        message="Are you sure you want to delete this product? This action cannot be undone."
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        confirmText="Delete"
        confirmButtonColor="error"
      />
    </Box>
  );
}
