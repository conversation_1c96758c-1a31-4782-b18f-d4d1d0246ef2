# backend project
- ./evospace-admin is backesnd platform management that include api service

Pear Admin Flask (master)
├─applications
│  ├─api # for evospace-pos
│  ├─common # public utils&functionalities
│  ├─extensions # app extensions
│  ├─schemas # public input/output schemas
│  ├─models # public models
│  ├─views # platform admin view controller
│  ├─config.py 
│  └─__init__.py
├─docs
├─static
├─templates # platform admin web applicaiton
└─app.py

# client project
- ./evospace-pos is client management
- **Dashboard**: View key metrics and sales data with interactive charts
- **POS Interface**: Intuitive interface for processing sales and transactions
- **Product Management**: Add, edit, and manage products with inventory tracking
- **Resource Management**: Manage rooms, desks, and equipment with real-time status
- **Service Management**: Configure and sell various services
- **Member Management**: Track member information and purchase history
- **Session Management**: Dynamically manage active resource usage sessions
- **Backend API Integration**: Connects to a backend API (default: `http://localhost:5000/api`) for live data, with mock data fallback.
- **Reports**: Generate sales and performance reports with visualizations