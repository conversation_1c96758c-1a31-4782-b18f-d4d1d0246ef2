from applications.extensions import ma
from applications.models.pos_service import PosService

class PosServiceSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = PosService
        load_instance = True  # Optional: deserialize to model instances
        # fields = ('id', 'name', 'description', 'price', 'duration', 'unit', 'category', 'created_at', 'updated_at') # Or specify fields explicitly
        # dump_only = ('id', 'created_at', 'updated_at') # Fields to include only during serialization

    id = ma.auto_field(dump_only=True)
    name = ma.auto_field(required=True)
    description = ma.auto_field(required=True)
    price = ma.auto_field(required=True)
    duration = ma.auto_field()
    unit = ma.auto_field()
    category = ma.auto_field()
    created_at = ma.auto_field(dump_only=True)
    updated_at = ma.auto_field(dump_only=True)