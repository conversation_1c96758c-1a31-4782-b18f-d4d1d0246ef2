<!DOCTYPE html>
<html>
<head>
    <title>部门管理</title>
    {% include 'system/common/header.html' %}
</head>
<body>
<form class="layui-form" action="">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item">
                    <label class="layui-form-label">上级部门</label>
                    <div class="layui-input-block">
                        <ul id="selectParent" name="parentId" class="dtree" data-id="-1"></ul>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="deptName" lay-verify="required" autocomplete="off" placeholder="请输入公司名称"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">负责人</label>
                    <div class="layui-input-block">
                        <input type="text" name="leader" lay-verify="required" autocomplete="off" placeholder="请输入负责人姓名"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">邮箱</label>
                    <div class="layui-input-block">
                        <input type="text" name="email" lay-verify="required" autocomplete="off" placeholder="请输入邮箱"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">联系方式</label>
                    <div class="layui-input-block">
                        <input type="text" name="phone" lay-verify="required" autocomplete="off" placeholder="请输入联系方式"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-block">
                        <input type="radio" name="status" value="1" title="开启">
                        <input type="radio" name="status" value="0" title="关闭" checked>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">排序</label>
                    <div class="layui-input-inline" style="width: 100px !important;">
                        <input type="text" name="sort" value="0" lay-verify="required" autocomplete="off"
                               placeholder="排序" min="0" step="1" lay-affix="number" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">详细地址</label>
                    <div class="layui-input-block">
                        <textarea placeholder="请输入描述" name="address" class="layui-textarea"></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button type="submit" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="dept-save">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
</form>
{% include 'system/common/footer.html' %}
<script>
  layui.use(['form', 'jquery', 'dtree'], function () {
    let form = layui.form
    let $ = layui.jquery
    let dtree = layui.dtree

    dtree.renderSelect({
      elem: '#selectParent',
      url: '/system/dept/tree',
      method: 'get',
      selectInputName: { nodeId: 'parentId', context: 'parentName' },
      skin: 'layui',
      dataFormat: 'list',
      response: { treeId: 'id', parentId: 'parent_id', title: 'dept_name' },
      selectInitVal: '0'
    })

    form.on('submit(dept-save)', function (data) {
      $.ajax({
        url: '/system/dept/save',
        data: JSON.stringify(data.field),
        dataType: 'json',
        contentType: 'application/json',
        type: 'post',
        success: function (result) {
          if (result.success) {
            layer.msg(result.msg, { icon: 1, time: 1000 }, function () {
              parent.layer.close(parent.layer.getFrameIndex(window.name))
              parent.render()
            })
          } else {
            layer.msg(result.msg, { icon: 2, time: 1000 })
          }
        }
      })
      return false
    })
  })
</script>
<script>
</script>
</body>
</html>