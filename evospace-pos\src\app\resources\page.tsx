"use client";

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Snackbar,
  Alert,
} from '@mui/material';
import {
  MeetingRoom as RoomIcon,
  DesktopWindows as DeskIcon,
  Devices as EquipmentIcon,
  TableChart as TableChartIcon,
} from '@mui/icons-material';
import usePosStore from '@/lib/store';
import { Resource, ResourceStatus } from '@/lib/types';
import { useTheme as useCustomThemeHook } from '@/lib/theme'; // Renamed to avoid conflict
import { useTheme as useMuiTheme } from '@mui/material/styles'; // MUI theme hook
import ConfirmDialog from '@/components/ConfirmDialog';
import {
  ResourceControls,
  ResourceDialog,
  ResourceFilters,
  ResourceViews,
  ResourceFormData
} from '@/components/resources';

export default function ResourcesPage() {
  const {
    resources,
    addResource,
    updateResource,
    deleteResource,
    updateResourceStatus,
    fetchResources,
    setResources,
    authUser
  } = usePosStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [resourceToDelete, setResourceToDelete] = useState<number | null>(null);

  const initialFormData: ResourceFormData = useMemo(() => ({
    id: 0,
    name: '',
    type: 'table',
    status: 'available',
    hourly_rate: '',
    capacity: '',
    floor: '1',
    zone: '',
    x: 1, // Default X for new items
    y: 1, // Default Y for new items
    width: 1,
    height: 1,
  }), []);

  const [formData, setFormData] = useState<ResourceFormData>(initialFormData);
  const [tabValue, setTabValue] = useState(0);
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [resourceToUpdate, setResourceToUpdate] = useState<number | null>(null);
  const [newStatus, setNewStatus] = useState<ResourceStatus>('available');
  const [viewMode, setViewMode] = useState<'list' | 'grid' | 'floorPlan'>('grid');
  const [selectedFloor, setSelectedFloor] = useState<number | null>(1);
  const [selectedZone, setSelectedZone] = useState<string>('all');
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('success');
  const [filteredResources, setFilteredResources] = useState<Resource[]>([]);
  const [formSubmitted, setFormSubmitted] = useState(false);
  const { mode: customThemeMode } = useCustomThemeHook(); // Using the custom hook for mode
  const muiTheme = useMuiTheme(); // MUI theme object
  const isDarkMode = customThemeMode === 'dark';
  const [loading, setLoading] = useState(false);
  const [pageError, setPageError] = useState<string | null>(null); // For general page errors
  const [dialogError, setDialogError] = useState<string | null>(null); // For dialog specific errors


  useEffect(() => {
    if (authUser.token) {
      setLoading(true);
      setPageError(null);
      fetchResources()
        .catch((err: Error) => {
          console.error("Failed to fetch resources:", err);
          setPageError("Could not load resources. Please try again later.");
          setSnackbarMessage("Could not load resources.");
          setSnackbarSeverity('error');
          setSnackbarOpen(true);
        })
        .finally(() => setLoading(false));
    }
  }, [fetchResources, authUser.token]);

  const uniqueFloors = useMemo(() => {
    const floors = Array.from(new Set((Array.isArray(resources) ? resources : []).map(r => r.floor).filter(Boolean).map(f => parseInt(f.toString(),10)))) as number[];
    return floors.sort((a,b) => a-b);
  }, [resources]);

  const uniqueZones = useMemo(() => {
    return Array.from(new Set((Array.isArray(resources) ? resources : []).map(r => r.zone).filter(Boolean))) as string[];
  }, [resources]);

  const resourceTypes = useMemo(() => ['all', 'room', 'desk', 'equipment', 'table'], []);

  useEffect(() => {
    if (loading && !resources.length) return; // Don't filter if initial load is happening

    const currentResources = Array.isArray(resources) ? resources : [];
    // Deduplication logic (simplified for brevity, assuming IDs are mostly unique from backend)
    const uniqueByIdMap = new Map<number, Resource>();
    currentResources.forEach(resource => {
        if (!uniqueByIdMap.has(resource.id)) {
            uniqueByIdMap.set(resource.id, resource);
        }
    });
    const trulyUniqueResources = Array.from(uniqueByIdMap.values());

    const filtered = trulyUniqueResources.filter(resource => {
      const nameMatch = resource.name.toLowerCase().includes(searchQuery.toLowerCase());
      const currentTabType = tabValue > 0 && tabValue < resourceTypes.length ? resourceTypes[tabValue] : 'all';
      const typeMatch = currentTabType === 'all' || resource.type.toLowerCase() === currentTabType;
      const floorMatch = selectedFloor === null || resource.floor?.toString() === selectedFloor.toString();
      const zoneMatch = selectedZone === 'all' || resource.zone === selectedZone;
      return nameMatch && typeMatch && floorMatch && zoneMatch;
    });
    setFilteredResources(filtered);
  }, [resources, searchQuery, tabValue, selectedFloor, selectedZone, resourceTypes, loading]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  const handleViewModeChange = (mode: 'list' | 'grid' | 'floorPlan') => {
    setViewMode(mode);
  };
  
  // Adjusted to match expected type in ResourceControls if necessary,
  // but SelectChangeEvent<string | number> is generally correct for MUI Select.
  // If ResourceControls expects a more generic ChangeEvent, that component's props should be updated.
  const handleFloorChange = (event: SelectChangeEvent<string | number>) => {
    const value = event.target.value as string | number; // Value from Select is string or number based on MenuItem values
    setSelectedFloor(value === "" ? null : Number(value));
  };

  const handleZoneChange = (event: SelectChangeEvent<string>) => {
    setSelectedZone(event.target.value as string);
  };

  const prepareFormData = useCallback((resource?: Resource): ResourceFormData => {
    if (resource) {
      return {
        ...initialFormData,
        ...resource,
        floor: resource.floor?.toString() || '1',
        hourly_rate: resource.hourly_rate?.toString() || '',
        capacity: resource.capacity?.toString() || '',
        x: resource.x ?? initialFormData.x,
        y: resource.y ?? initialFormData.y,
        width: resource.width ?? initialFormData.width,
        height: resource.height ?? initialFormData.height,
      };
    }
    const newId = resources && resources.length > 0 ? Math.max(...resources.map(r => r.id)) + 1 : 1;
    return { ...initialFormData, id: newId }; // Ensure new items get default x,y,width,height
  }, [resources, initialFormData]);

  const handleOpenDialog = useCallback((edit = false, resource?: Resource) => {
    setEditMode(edit);
    setFormData(prepareFormData(resource));
    setOpenDialog(true);
    setDialogError(null);
    setFormSubmitted(false);
  }, [prepareFormData]);

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setDialogError(null);
    setFormData(initialFormData);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    const numFields = ['hourly_rate', 'capacity', 'x', 'y', 'width', 'height'];
    setFormData(prev => ({
      ...prev,
      [name]: numFields.includes(name) && value !== '' ? parseFloat(value) : value
    }));
  };

  const handleSelectChange = (e: SelectChangeEvent<string>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async () => {
    setFormSubmitted(true);
    if (!formData.name) {
      setDialogError('Resource name is required.');
      setSnackbarMessage('Resource name is required.');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }
    setLoading(true);
    setDialogError(null);

    const resourceDataPayload = {
      name: formData.name,
      type: formData.type,
      status: formData.status,
      hourly_rate: parseFloat(formData.hourly_rate.toString()) || 0,
      capacity: formData.capacity ? parseInt(formData.capacity.toString()) : 0,
      floor: parseInt(formData.floor.toString()),
      zone: formData.zone ? formData.zone.toString() : '',
      x: parseInt(formData.x.toString()),
      y: parseInt(formData.y.toString()),
      width: parseInt(formData.width.toString()),
      height: parseInt(formData.height.toString()),
    };

    try {
      if (editMode && formData.id) {
        await updateResource(formData.id, resourceDataPayload);
        setSnackbarMessage('Resource updated successfully');
      } else {
        await addResource(resourceDataPayload);
        setSnackbarMessage('Resource added successfully');
      }
      setSnackbarSeverity('success');
      handleCloseDialog();
      fetchResources();
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred.';
      setDialogError(errorMessage);
      setSnackbarMessage(errorMessage);
      setSnackbarSeverity('error');
    } finally {
      setLoading(false);
      setSnackbarOpen(true);
    }
  };

  const handleStatusClick = (resource_id: number, currentStatus: ResourceStatus) => {
    setResourceToUpdate(resource_id);
    setNewStatus(currentStatus);
    setStatusDialogOpen(true);
  };
  
  const handleStatusDialogClose = () => {
    setStatusDialogOpen(false);
    setResourceToUpdate(null);
  };

  const handleNewStatusChange = (e: SelectChangeEvent<string>) => {
    setNewStatus(e.target.value as ResourceStatus);
  };

  const handleUpdateStatus = async () => {
    if (resourceToUpdate === null) return;
    setLoading(true);
    try {
      await updateResourceStatus(resourceToUpdate, newStatus);
      setSnackbarMessage('Resource status updated.');
      setSnackbarSeverity('success');
      handleStatusDialogClose();
      fetchResources();
    } catch (errorCaught) {
      setSnackbarMessage(errorCaught instanceof Error ? errorCaught.message : 'Failed to update status.');
      setSnackbarSeverity('error');
    } finally {
      setLoading(false);
      setSnackbarOpen(true);
    }
  };

  const handleDeleteClick = (resource_id: number) => {
    setResourceToDelete(resource_id);
    setDeleteConfirmOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (resourceToDelete === null) return;
    setLoading(true);
    try {
      await deleteResource(resourceToDelete);
      setSnackbarMessage('Resource deleted.');
      setSnackbarSeverity('success');
      fetchResources();
    } catch (err) {
      setSnackbarMessage(err instanceof Error ? err.message : 'Failed to delete resource.');
      setSnackbarSeverity('error');
    } finally {
      setLoading(false);
      setDeleteConfirmOpen(false);
      setResourceToDelete(null);
      setSnackbarOpen(true);
    }
  };

  const handleCancelDelete = () => {
    setDeleteConfirmOpen(false);
    setResourceToDelete(null);
  };

  const getResourceIcon = useCallback((type: string): React.ReactElement => {
    switch (type) {
      case 'room': return <RoomIcon />;
      case 'desk': return <DeskIcon />;
      case 'equipment': return <EquipmentIcon />;
      case 'table': return <TableChartIcon />;
      default: return <RoomIcon />;
    }
  }, []);

  const getStatusChipColor = useCallback((status: ResourceStatus): "success" | "error" | "warning" | "info" | "default" => {
    const colors: Record<ResourceStatus, "success" | "error" | "warning" | "info" | "default"> = {
      available: 'success',
      booked: 'error',
      maintenance: 'warning',
      'in-use': 'info',
      // Add other statuses if they exist and map them or ensure 'default' covers them
    };
    return colors[status] || 'default';
  }, []);

  const getResourceColor = useCallback((status: ResourceStatus) => {
    // Ensure muiTheme is available
    if (!muiTheme) {
      // Fallback or default colors if muiTheme is not yet available (should be rare)
      const defaultColors = { bg: 'rgba(158, 158, 158, 0.7)', border: '#9e9e9e' }; // grey 500
      return defaultColors;
    }

    const palette = {
      available: { bg: isDarkMode ? 'rgba(76, 175, 80, 0.8)' : 'rgba(76, 175, 80, 0.7)', border: muiTheme.palette.success.main },
      booked: { bg: isDarkMode ? 'rgba(244, 67, 54, 0.8)' : 'rgba(244, 67, 54, 0.7)', border: muiTheme.palette.error.main },
      maintenance: { bg: isDarkMode ? 'rgba(255, 152, 0, 0.8)' : 'rgba(255, 152, 0, 0.7)', border: muiTheme.palette.warning.main },
      'in-use': { bg: isDarkMode ? 'rgba(33, 150, 243, 0.8)' : 'rgba(33, 150, 243, 0.7)', border: muiTheme.palette.info.main },
    };
    const defaultStatusColors = {
      bg: isDarkMode ? 'rgba(158, 158, 158, 0.8)' : 'rgba(158, 158, 158, 0.7)',
      border: muiTheme.palette.grey[500]
    };
    return palette[status] || defaultStatusColors;
  }, [isDarkMode, muiTheme]);

  const handleResourcePositionUpdate = async (resource_id: number, x: number, y: number) => {
    const resource = resources.find(r => r.id === resource_id);
    if (!resource) return;
    const optimisticUpdate = { ...resource, x, y };
    const originalResources = [...resources];
    setResources(resources.map(r => r.id === resource_id ? optimisticUpdate : r)); // Optimistic UI update

    try {
      await updateResource(resource_id, { x, y }); // Only send changed fields
      setSnackbarMessage(`Position updated for '${resource.name}'.`);
      setSnackbarSeverity('success');
    } catch (e) {
      setResources(originalResources); // Revert on error
      setSnackbarMessage(`Failed to update position for '${resource.name}'. ${e instanceof Error ? e.message : ''}`);
      setSnackbarSeverity('error');
    } finally {
      setSnackbarOpen(true);
    }
  };
  
  const handleSnackbarClose = (event?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') return;
    setSnackbarOpen(false);
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <ResourceControls
        onAddResource={() => handleOpenDialog(false)}
        searchQuery={searchQuery}
        onSearchChange={handleSearchChange}
        viewMode={viewMode}
        onViewModeChange={handleViewModeChange}
        selectedFloor={selectedFloor}
        onFloorChange={handleFloorChange}
        floors={uniqueFloors}
        selectedZone={selectedZone}
        onZoneChange={handleZoneChange}
        zones={uniqueZones}
        resourceTypes={resourceTypes}
        tabValue={tabValue}
        onTabChange={handleTabChange}
      />

      {pageError && (
         <Alert severity="error" sx={{ my: 2 }}>{pageError}</Alert>
      )}
      
      <ResourceFilters
        selectedFloor={selectedFloor}
        onFloorChange={handleFloorChange}
        floors={uniqueFloors}
        selectedZone={selectedZone}
        onZoneChange={handleZoneChange}
        zones={uniqueZones}
        viewMode={viewMode}
      />

      <Box sx={{ mt: viewMode === 'floorPlan' && uniqueFloors.length > 0 ? 0 : 2 }}>
        <ResourceViews
          viewMode={viewMode}
          resources={filteredResources}
          onEditResource={resource => handleOpenDialog(true, resource)}
          onDeleteResource={handleDeleteClick}
          onStatusClick={handleStatusClick}
          onResourcePositionUpdate={handleResourcePositionUpdate}
          selectedFloor={selectedFloor}
          isDarkMode={isDarkMode}
          getResourceIcon={getResourceIcon}
          getStatusChipColor={getStatusChipColor}
          getResourceColor={getResourceColor}
          loading={loading && filteredResources.length === 0 && !pageError}
          error={(pageError && filteredResources.length === 0) ? pageError : null}
        />
      </Box>

      <ResourceDialog
        open={openDialog}
        editMode={editMode}
        formData={formData}
        onClose={handleCloseDialog}
        onInputChange={handleInputChange}
        onSelectChange={handleSelectChange}
        onSubmit={handleSubmit}
        error={dialogError}
        loading={loading}
        formSubmitted={formSubmitted}
      />

      <ConfirmDialog
        open={deleteConfirmOpen}
        title="Confirm Delete"
        message="Are you sure you want to delete this resource? This action cannot be undone."
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        confirmButtonColor="error"
      />

      <Dialog open={statusDialogOpen} onClose={handleStatusDialogClose}>
        <DialogTitle>Update Resource Status</DialogTitle>
        <DialogContent>
          <FormControl fullWidth sx={{ mt: 1 }}>
            <InputLabel id="new-status-label">New Status</InputLabel>
            <Select
              labelId="new-status-label"
              value={newStatus}
              onChange={handleNewStatusChange}
              label="New Status"
            >
              <MenuItem value="available">Available</MenuItem>
              <MenuItem value="booked">Booked</MenuItem>
              <MenuItem value="maintenance">Maintenance</MenuItem>
              <MenuItem value="in-use">In Use</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleStatusDialogClose}>Cancel</Button>
          <Button onClick={handleUpdateStatus} variant="contained" disabled={loading}>
            Update Status
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }} variant="filled">
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
}
