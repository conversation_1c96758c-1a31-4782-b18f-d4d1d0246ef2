import datetime
import jwt
from flask import Blueprint, request, jsonify, current_app, g
from functools import wraps
from sqlalchemy.exc import SQLAlchemyError

from applications.models import User
from applications.models.admin_dept import Dept
from applications.extensions import db
from sqlalchemy import text

auth_api_bp = Blueprint('auth_api', __name__, url_prefix='/api/auth')

def get_user_accessible_stores(user_dept_id):
    """
    Get stores accessible to a user based on their dept_id.
    Returns the user's assigned store plus all its sub-branches recursively.
    """
    if not user_dept_id:
        return []
    
    try:
        # Use recursive CTE to get user's store and all its branches
        query = text("""
            WITH RECURSIVE store_hierarchy AS (
                -- Base case: user's primary store
                SELECT id, dept_name, parent_id, address, phone, status, 0 as level
                FROM admin_dept
                WHERE id = :user_dept_id AND status = 1
                
                UNION ALL
                
                -- Recursive case: all child stores
                SELECT d.id, d.dept_name, d.parent_id, d.address, d.phone, d.status, sh.level + 1
                FROM admin_dept d
                INNER JOIN store_hierarchy sh ON d.parent_id = sh.id
                WHERE d.status = 1
            )
            SELECT * FROM store_hierarchy
            ORDER BY level, id
        """)
        
        result = db.session.execute(query, {'user_dept_id': user_dept_id})
        stores = []
        
        for row in result:
            stores.append({
                'id': row.id,
                'name': row.dept_name,
                'address': row.address,
                'phone': row.phone,
                'parent_id': row.parent_id,
                'level': row.level
            })
        
        return stores
        
    except Exception as e:
        current_app.logger.error(f'Error getting user accessible stores: {e}')
        # Fallback: return only user's primary store if recursive query fails
        try:
            primary_store = Dept.query.filter_by(id=user_dept_id, status=1).first()
            if primary_store:
                return [{
                    'id': primary_store.id,
                    'name': primary_store.dept_name,
                    'address': primary_store.address,
                    'phone': primary_store.phone,
                    'parent_id': primary_store.parent_id,
                    'level': 0
                }]
        except Exception as fallback_error:
            current_app.logger.error(f'Fallback store query failed: {fallback_error}')
        
        return []

def validate_user_store_access(user, store_id):
    """
    Validate if a user has access to a specific store.
    Returns True if the user can access the store, False otherwise.
    """
    if not user or not user.dept_id or not store_id:
        return False
    
    accessible_stores = get_user_accessible_stores(user.dept_id)
    accessible_store_ids = [store['id'] for store in accessible_stores]
    
    return store_id in accessible_store_ids

def token_required(f):
    """Decorator to require JWT token for API endpoints"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = None
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            if auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]

        if not token:
            return jsonify({
                'success': False,
                'message': 'Token is missing!'
            }), 401

        try:
            data = jwt.decode(
                token, 
                current_app.config['JWT_SECRET_KEY'], 
                algorithms=[current_app.config['JWT_ALGORITHM']]
            )
            # Store user data in g for access in the route if needed
            current_user = User.query.filter_by(id=data.get('user_id')).first()
            if not current_user:
                return jsonify({
                    'success': False,
                    'message': 'Token is invalid, user not found!'
                }), 401
            g.current_user = current_user
            g.store_id = data.get('store_id')
        except jwt.ExpiredSignatureError:
            return jsonify({
                'success': False,
                'message': 'Token has expired!'
            }), 401
        except jwt.InvalidTokenError:
            return jsonify({
                'success': False,
                'message': 'Token is invalid!'
            }), 401
        except Exception as e:
            current_app.logger.error(f'Token validation error: {e}')
            return jsonify({
                'success': False,
                'message': 'Token processing error!'
            }), 401

        return f(*args, **kwargs)
    return decorated_function

@auth_api_bp.route('/login', methods=['POST'])
def login():
    """Authenticate user and generate JWT token"""
    try:
        # Validate request data
        json_data = request.get_json()
        if not json_data:
            return jsonify({
                'success': False,
                'message': 'No input data provided'
            }), 400
            
        if not json_data.get('username') or not json_data.get('password'):
            return jsonify({
                'success': False,
                'message': 'Username and password are required'
            }), 400
            
        if not json_data.get('store_id'):
            return jsonify({
                'success': False,
                'message': 'Store selection is required'
            }), 400

        username = json_data.get('username')
        password = json_data.get('password')
        store_id = json_data.get('store_id')

        # Find user by username
        user = User.query.filter_by(username=username).first()

        if not user or not user.validate_password(password):
            return jsonify({
                'success': False,
                'message': 'Invalid username or password'
            }), 401
            
        # Validate store exists and user has access to it
        store = Dept.query.filter_by(id=store_id).first()
        if not store:
            return jsonify({
                'success': False,
                'message': 'Selected store does not exist'
            }), 400

        # Check if user has permission to access this store
        if not validate_user_store_access(user, store_id):
            return jsonify({
                'success': False,
                'message': 'You do not have permission to access this store'
            }), 403

        # Generate JWT token
        payload = {
            'user_id': user.id,
            'username': user.username,
            'store_id': store_id,
            'store_name': store.dept_name,
            'exp': datetime.datetime.utcnow() + current_app.config['JWT_EXPIRATION_DELTA']
        }
        token = jwt.encode(
            payload,
            current_app.config['JWT_SECRET_KEY'],
            algorithm=current_app.config['JWT_ALGORITHM']
        )
        
        # Prepare user data for response
        user_data = {
            'id': user.id,
            'username': user.username,
            'name': user.realname,  # Mapping 'name' to 'realname'
            'store_id': store_id,
            'store_name': store.dept_name,
        }
        
        # Get user role if available
        first_role = user.role.first()
        if first_role and hasattr(first_role, 'name'):
            user_data['role'] = first_role.name
        elif first_role and hasattr(first_role, 'rolename'):
            user_data['role'] = first_role.rolename
        else:
            user_data['role'] = None

        return jsonify({
            'success': True,
            'message': 'Login successful',
            'data': {
                'token': token,
                'user': user_data
            }
        }), 200
        
    except SQLAlchemyError as e:
        return jsonify({
            'success': False,
            'message': 'Database error', 
            'error': str(e)
        }), 500
    except Exception as e:
        current_app.logger.error(f'Login error: {e}')
        return jsonify({
            'success': False,
            'message': 'An error occurred during login'
        }), 500

@auth_api_bp.route('/stores', methods=['GET', 'POST'])
def get_available_stores():
    """Get list of available stores/departments for login selection
    
    GET: Returns all enabled stores (public endpoint)
    POST: Returns stores available to a specific user (with credentials validation)
    """
    try:
        if request.method == 'POST':
            # Validate user credentials and return their accessible stores
            json_data = request.get_json()
            if not json_data:
                return jsonify({'success': False, 'message': 'No credentials provided'}), 400
            
            username = json_data.get('username')
            password = json_data.get('password')
            
            if not username or not password:
                return jsonify({'success': False, 'message': 'Username and password required'}), 400
            
            # Authenticate user
            user = User.query.filter_by(username=username).first()
            if not user or not user.validate_password(password):
                return jsonify({'success': False, 'message': 'Invalid credentials'}), 401
            
            # Get stores available to this user based on their dept_id and hierarchy
            stores_data = get_user_accessible_stores(user.dept_id)
            
            # If no stores found, return error
            if not stores_data:
                return jsonify({
                    'success': False,
                    'message': 'No accessible stores found for this user'
                }), 403
        else:
            # GET method - return all active stores (public endpoint)
            stores = Dept.query.filter_by(status=1).all()
            stores_data = []
            for store in stores:
                stores_data.append({
                    'id': store.id,
                    'name': store.dept_name,
                    'address': store.address,
                    'phone': store.phone
                })
        
        return jsonify({
            'success': True,
            'message': 'Stores retrieved successfully',
            'data': {
                'items': stores_data
            }
        }), 200
        
    except SQLAlchemyError as e:
        return jsonify({
            'success': False,
            'message': 'Database error',
            'error': str(e)
        }), 500
    except Exception as e:
        current_app.logger.error(f'Get stores error: {e}')
        return jsonify({
            'success': False,
            'message': 'An error occurred while fetching stores'
        }), 500

def store_context_required(f):
    """Decorator to require store context for API endpoints"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not hasattr(g, 'store_id') or not g.store_id:
            return jsonify({
                'success': False,
                'message': 'Store context required'
            }), 400
        return f(*args, **kwargs)
    return decorated_function
