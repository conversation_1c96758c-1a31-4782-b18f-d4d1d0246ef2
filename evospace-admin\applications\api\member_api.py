from flask import Blueprint, request, jsonify, g, url_for
from applications.extensions import db, ma
from applications.models.pos_member import PosMember
from applications.schemas.pos_member_schema import PosMemberSchema
from applications.api.auth_api import token_required, store_context_required
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import joinedload
from marshmallow import ValidationError
from datetime import datetime
import logging

# Set up logger
logger = logging.getLogger(__name__)

member_api_bp = Blueprint('member_api', __name__, url_prefix='/api/members')

# Create schemas
member_schema = PosMemberSchema()
members_schema = PosMemberSchema(many=True)

@member_api_bp.route('', methods=['POST'])
@token_required
@store_context_required
def create_member():
    """Create a new member
    
    Request Body:
    - name: Required, member name
    - email: Optional, member email (must be unique)
    - phone: Optional, member phone (must be unique)
    - birthday: Optional, member birthday (format: YYYY-MM-DD)
    - address: Optional, member address
    - membership_level: Optional, member's membership level
    - and other fields as defined in the PosMember model
    """
    try:
        # Validate request data
        json_data = request.get_json()
        if not json_data:
            return jsonify({
                'success': False,
                'message': 'No input data provided'
            }), 400
        
        # Validate and deserialize input
        try:
            data = member_schema.load(json_data)
        except ValidationError as err:
            return jsonify({
                'success': False,
                'message': 'Validation error',
                'errors': err.messages
            }), 400
        
        # Create new member and assign store_id
        member = data
        member.store_id = g.store_id  # Set store context from JWT token
        db.session.add(member)
        db.session.commit()
        
        # Log the member creation
        logger.info(f"Member created: ID={member.id}, Name={member.name}")
        
        return jsonify({
            'success': True,
            'message': 'Member created successfully',
            'data': {
                'member': member_schema.dump(member)
            }
        }), 201
        
    except ValidationError as err:
        return jsonify({'message': 'Validation error', 'errors': err.messages}), 400
    except SQLAlchemyError as e:
        db.session.rollback()
        return jsonify({'message': 'Database error', 'error': str(e)}), 500

@member_api_bp.route('/', methods=['GET'])
@token_required
@store_context_required
def get_members():
    """Get all members with optional filtering and pagination
    
    Query Parameters:
    - name: Filter by member name (partial match)
    - phone: Filter by phone number (partial match)
    - email: Filter by email address (partial match)
    - membership_level: Filter by membership level
    - status: Filter by status
    - city: Filter by city
    - state: Filter by state
    - country: Filter by country
    - min_total_spent: Filter by minimum total spent
    - min_visits: Filter by minimum number of visits
    - page: Page number (default: 1)
    - per_page: Items per page (default: 20, max: 100)
    - sort_by: Field to sort by (default: name)
    - sort_order: Sort order ('asc' or 'desc', default: 'asc')
    """
    try:
        # Get query parameters for filtering
        name = request.args.get('name')
        phone = request.args.get('phone')
        email = request.args.get('email')
        membership_level = request.args.get('membership_level')
        status = request.args.get('status')
        city = request.args.get('city')
        state = request.args.get('state')
        country = request.args.get('country')
        min_total_spent = request.args.get('min_total_spent', type=float)
        min_visits = request.args.get('min_visits', type=int)
        
        # Pagination parameters
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)  # Limit max per_page to 100
        
        # Sorting parameters
        sort_by = request.args.get('sort_by', 'name')
        sort_order = request.args.get('sort_order', 'asc')
        
        # Validate sort parameters
        valid_sort_fields = ['id', 'name', 'email', 'phone', 'membership_level', 'total_spent', 'visits', 'created_at', 'updated_at']
        if sort_by not in valid_sort_fields:
            return jsonify({
                'success': False,
                'message': f'Invalid sort_by parameter. Valid options are: {valid_sort_fields}'
            }), 400
            
        if sort_order not in ['asc', 'desc']:
            return jsonify({
                'success': False,
                'message': 'Invalid sort_order parameter. Valid options are: asc, desc'
            }), 400
        
        # Build query with filters (filter by store first)
        query = PosMember.query.filter(PosMember.store_id == g.store_id)
        
        if name:
            query = query.filter(PosMember.name.ilike(f'%{name}%'))
        if phone:
            query = query.filter(PosMember.phone.ilike(f'%{phone}%'))
        if email:
            query = query.filter(PosMember.email.ilike(f'%{email}%'))
        if membership_level:
            query = query.filter(PosMember.membership_level == membership_level)
        if status:
            query = query.filter(PosMember.status == status)
        if city:
            query = query.filter(PosMember.city.ilike(f'%{city}%'))
        if state:
            query = query.filter(PosMember.state.ilike(f'%{state}%'))
        if country:
            query = query.filter(PosMember.country.ilike(f'%{country}%'))
        if min_total_spent is not None:
            query = query.filter(PosMember.total_spent >= min_total_spent)
        if min_visits is not None:
            query = query.filter(PosMember.visits >= min_visits)
        
        # Apply sorting
        sort_column = getattr(PosMember, sort_by)
        if sort_order == 'desc':
            query = query.order_by(sort_column.desc())
        else:
            query = query.order_by(sort_column.asc())
        
        # Get paginated results
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        members = pagination.items
        
        # Log the query for monitoring
        logger.info(f"Members query: page={page}, per_page={per_page}, filters applied: {request.args}")
        
        # Build pagination metadata
        meta = {
            'page': page,
            'per_page': per_page,
            'total_pages': pagination.pages,
            'total_items': pagination.total
        }
        
        # Add pagination links
        links = {}
        if pagination.has_prev:
            links['prev'] = url_for('member_api.get_members', page=page-1, per_page=per_page, **request.args)
        if pagination.has_next:
            links['next'] = url_for('member_api.get_members', page=page+1, per_page=per_page, **request.args)
        links['first'] = url_for('member_api.get_members', page=1, per_page=per_page, **request.args)
        links['last'] = url_for('member_api.get_members', page=pagination.pages, per_page=per_page, **request.args)
        
        return jsonify({
            'success': True,
            'data': {
                'items': members_schema.dump(members),
                'meta': meta,
                'links': links
            }
        }), 200
        
    except SQLAlchemyError as e:
        return jsonify({'message': 'Database error', 'error': str(e)}), 500

@member_api_bp.route('/<int:id>', methods=['GET'])
@token_required
@store_context_required
def get_member(id):
    """Get a specific member by ID"""
    try:
        # Use joinedload for better performance when loading relationships
        member = PosMember.query.options(joinedload('transactions')).filter(
            PosMember.id == id,
            PosMember.store_id == g.store_id
        ).first()
        
        if not member:
            return jsonify({
                'success': False,
                'message': 'Member not found'
            }), 404
        
        return jsonify({
            'success': True,
            'data': {
                'member': member_schema.dump(member)
            }
        }), 200
        
    except SQLAlchemyError as e:
        return jsonify({'message': 'Database error', 'error': str(e)}), 500

@member_api_bp.route('/<int:id>', methods=['PUT'])
@token_required
@store_context_required
def update_member(id):
    """Update a specific member"""
    try:
        # Find the member (within the current store context)
        member = PosMember.query.filter(
            PosMember.id == id,
            PosMember.store_id == g.store_id
        ).first()
        
        if not member:
            return jsonify({
                'success': False,
                'message': 'Member not found'
            }), 404
        
        # Validate request data
        json_data = request.get_json()
        if not json_data:
            return jsonify({
                'success': False,
                'message': 'No input data provided'
            }), 400
        
        # Validate and deserialize input
        try:
            # Load the data into a new PosMember instance
            updated_member = member_schema.load(json_data, partial=True)
            
            # Get the fields that were provided in the request
            provided_fields = json_data.keys()
            
            # Update only the fields that were provided in the request
            for field in provided_fields:
                if hasattr(updated_member, field):
                    setattr(member, field, getattr(updated_member, field))
        except ValidationError as err:
            return jsonify({
                'success': False,
                'message': 'Validation error',
                'errors': err.messages
            }), 400
        
        # Set updated_at timestamp
        member.updated_at = datetime.now()
        
        db.session.commit()
        
        # Log the member update
        logger.info(f"Member updated: ID={member.id}, Name={member.name}")
        
        return jsonify({
            'success': True,
            'message': 'Member updated successfully',
            'data': {
                'member': member_schema.dump(member)
            }
        }), 200
        
    except ValidationError as err:
        return jsonify({'message': 'Validation error', 'errors': err.messages}), 400
    except SQLAlchemyError as e:
        db.session.rollback()
        return jsonify({'message': 'Database error', 'error': str(e)}), 500

@member_api_bp.route('/<int:id>', methods=['DELETE'])
@token_required
@store_context_required
def delete_member(id):
    """Delete a member"""
    try:
        # Find the member (within the current store context)
        member = PosMember.query.filter(
            PosMember.id == id,
            PosMember.store_id == g.store_id
        ).first()
        
        if not member:
            return jsonify({
                'success': False,
                'message': 'Member not found'
            }), 404
        
        # Store member info for logging before deletion
        member_id = member.id
        member_name = member.name
        
        # Delete the member
        db.session.delete(member)
        db.session.commit()
        
        # Log the member deletion
        logger.info(f"Member deleted: ID={member_id}, Name={member_name}")
        
        return jsonify({
            'success': True,
            'message': 'Member deleted successfully'
        }), 200
        
    except SQLAlchemyError as e:
        db.session.rollback()
        return jsonify({'message': 'Database error', 'error': str(e)}), 500
