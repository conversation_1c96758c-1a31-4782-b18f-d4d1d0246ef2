from flask import Blueprint, request, jsonify, url_for, g
from applications.extensions import db
from applications.models.pos_transaction import PosTransaction, PosTransactionSession
from applications.models.pos_session import PosSession, PosSessionProduct, PosSessionService
from applications.models.pos_product import PosProduct
from applications.models.pos_member import PosMember
from .auth_api import token_required, store_context_required
from sqlalchemy import func, extract, desc
from sqlalchemy.orm import joinedload
import datetime
import decimal
import logging

# Set up logger
logger = logging.getLogger(__name__)

reports_api_bp = Blueprint('reports_api', __name__, url_prefix='/api/reports')

# Helper function to parse date parameters
def parse_date_param(param_name):
    date_str = request.args.get(param_name)
    if date_str:
        try:
            return datetime.datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            # This error should be caught by the caller and returned via fail_api
            raise ValueError(f"Invalid date format for {param_name}. Use YYYY-MM-DD.")
    return None

# Helper function to convert Decimal to float for JSON serialization
def decimal_to_float(value):
    if value is None:
        return 0.0
    return float(value)

@reports_api_bp.route('/sales_summary', methods=['GET'])
@token_required
@store_context_required
def sales_summary_report():
    """Generate a sales summary report
    
    Query Parameters:
    - start_date: Optional, filter by start date (format: YYYY-MM-DD)
    - end_date: Optional, filter by end date (format: YYYY-MM-DD)
    """
    try:
        start_date = parse_date_param('start_date')
        end_date = parse_date_param('end_date')
    except ValueError as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 400

    # Get transaction summary stats filtered by store
    query = db.session.query(
        func.sum(PosTransaction.actual_amount).label('total_sales_revenue'),
        func.count(PosTransaction.id).label('number_of_transactions')
    ).filter(
        PosTransaction.status == 'completed',
        PosTransaction.store_id == g.store_id
    )

    if start_date:
        query = query.filter(func.date(PosTransaction.created_at) >= start_date)
    if end_date:
        query = query.filter(func.date(PosTransaction.created_at) <= end_date)

    summary_stats = query.one_or_none()

    total_sales_revenue = summary_stats.total_sales_revenue if summary_stats and summary_stats.total_sales_revenue else decimal.Decimal('0.00')
    number_of_transactions = summary_stats.number_of_transactions if summary_stats and summary_stats.number_of_transactions else 0
    average_transaction_value = (total_sales_revenue / number_of_transactions) if number_of_transactions > 0 else decimal.Decimal('0.00')

    # Top 5 selling products by quantity from session products (filtered by store)
    top_products_query = db.session.query(
        PosProduct.name,
        func.sum(PosSessionProduct.quantity).label('total_quantity_sold')
    ).join(
        PosSessionProduct, PosProduct.id == PosSessionProduct.product_id
    ).join(
        PosSession, PosSessionProduct.session_id == PosSession.id
    ).join(
        PosTransactionSession, PosSession.id == PosTransactionSession.session_id
    ).join(
        PosTransaction, PosTransactionSession.transaction_id == PosTransaction.id
    ).filter(
        PosTransaction.status == 'completed',
        PosTransaction.store_id == g.store_id,
        PosProduct.store_id == g.store_id
    )
    
    if start_date:
        top_products_query = top_products_query.filter(func.date(PosTransaction.created_at) >= start_date)
    if end_date:
        top_products_query = top_products_query.filter(func.date(PosTransaction.created_at) <= end_date)

    top_products = top_products_query.group_by(PosProduct.name)\
                                     .order_by(func.sum(PosSessionProduct.quantity).desc())\
                                     .limit(5)\
                                     .all()

    report_data = {
        'total_sales_revenue': decimal_to_float(total_sales_revenue),
        'number_of_transactions': number_of_transactions,
        'average_transaction_value': decimal_to_float(average_transaction_value),
        'top_selling_products': [{'name': p_name, 'total_quantity_sold': int(p_qty)} for p_name, p_qty in top_products],
        'filters_applied': {
            'start_date': start_date.isoformat() if start_date else None,
            'end_date': end_date.isoformat() if end_date else None
        }
    }

    # Log the report generation
    logger.info(f"Sales summary report generated: start_date={start_date}, end_date={end_date}")
    
    return jsonify({
        'success': True,
        'message': 'Sales summary generated successfully',
        'data': report_data
    }), 200

@reports_api_bp.route('/sales_by_payment_method', methods=['GET'])
@token_required
@store_context_required
def sales_by_payment_method():
    """Generate a report of sales by payment method
    
    Query Parameters:
    - start_date: Optional, filter by start date (format: YYYY-MM-DD)
    - end_date: Optional, filter by end date (format: YYYY-MM-DD)
    """
    try:
        start_date = parse_date_param('start_date')
        end_date = parse_date_param('end_date')
    except ValueError as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 400

    query = db.session.query(
        PosTransaction.payment_method,
        func.sum(PosTransaction.actual_amount).label('total_amount'),
        func.count(PosTransaction.id).label('transaction_count')
    ).filter(
        PosTransaction.status == 'completed',
        PosTransaction.store_id == g.store_id
    )

    if start_date:
        query = query.filter(func.date(PosTransaction.created_at) >= start_date)
    if end_date:
        query = query.filter(func.date(PosTransaction.created_at) <= end_date)

    results = query.group_by(PosTransaction.payment_method).all()

    payment_data = [{
        'payment_method': method,
        'total_amount': decimal_to_float(amount),
        'transaction_count': count
    } for method, amount, count in results]

    # Log the report generation
    logger.info(f"Sales by payment method report generated: start_date={start_date}, end_date={end_date}")
    
    return jsonify({
        'success': True,
        'message': 'Sales by payment method generated successfully',
        'data': {
            'payment_methods': payment_data,
            'filters_applied': {
                'start_date': start_date.isoformat() if start_date else None,
                'end_date': end_date.isoformat() if end_date else None
            }
        }
    }), 200

@reports_api_bp.route('/member_activity', methods=['GET'])
@token_required
@store_context_required
def member_activity_report():
    """Generate a report of member activity
    
    Query Parameters:
    - start_date: Optional, filter by start date (format: YYYY-MM-DD)
    - end_date: Optional, filter by end date (format: YYYY-MM-DD)
    - limit: Optional, limit the number of results (default: 10)
    - page: Optional, page number for pagination (default: 1)
    - per_page: Optional, items per page (default: 10, max: 100)
    """
    try:
        start_date = parse_date_param('start_date')
        end_date = parse_date_param('end_date')
        limit = request.args.get('limit', default=10, type=int)
        
        # Pagination parameters
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 10, type=int), 100)  # Limit max per_page to 100
    except ValueError as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 400

    # Get top members by transaction amount (filtered by store)
    query = db.session.query(
        PosMember.id,
        PosMember.name,
        func.sum(PosTransaction.actual_amount).label('total_spent'),
        func.count(PosTransaction.id).label('transaction_count')
    ).join(
        PosTransaction, PosMember.id == PosTransaction.member_id
    ).filter(
        PosTransaction.status == 'completed',
        PosTransaction.member_id.isnot(None),
        PosTransaction.store_id == g.store_id,
        PosMember.store_id == g.store_id
    )

    if start_date:
        query = query.filter(func.date(PosTransaction.created_at) >= start_date)
    if end_date:
        query = query.filter(func.date(PosTransaction.created_at) <= end_date)

    # Apply pagination if requested
    if page and per_page:
        total_count = query.group_by(PosMember.id, PosMember.name).count()
        query = query.group_by(PosMember.id, PosMember.name)\
                    .order_by(func.sum(PosTransaction.actual_amount).desc())
        
        # Calculate pagination offsets
        offset = (page - 1) * per_page
        query = query.offset(offset).limit(per_page)
    else:
        # Use limit if pagination is not used
        query = query.group_by(PosMember.id, PosMember.name)\
                    .order_by(func.sum(PosTransaction.actual_amount).desc())\
                    .limit(limit)
        total_count = limit  # Approximate for non-paginated results
    
    top_members = query.all()

    member_data = [{
        'id': member_id,
        'name': name,
        'total_spent': decimal_to_float(total),
        'transaction_count': count
    } for member_id, name, total, count in top_members]

    # Build pagination metadata if using pagination
    if page and per_page:
        total_pages = (total_count + per_page - 1) // per_page  # Ceiling division
        meta = {
            'page': page,
            'per_page': per_page,
            'total_pages': total_pages,
            'total_items': total_count
        }
        
        # Add pagination links
        links = {}
        if page > 1:
            links['prev'] = url_for('reports_api.member_activity_report', page=page-1, per_page=per_page, **request.args)
        if page < total_pages:
            links['next'] = url_for('reports_api.member_activity_report', page=page+1, per_page=per_page, **request.args)
        links['first'] = url_for('reports_api.member_activity_report', page=1, per_page=per_page, **request.args)
        links['last'] = url_for('reports_api.member_activity_report', page=total_pages, per_page=per_page, **request.args)
    else:
        meta = None
        links = None
    
    # Log the report generation
    logger.info(f"Member activity report generated: start_date={start_date}, end_date={end_date}, limit={limit}")
    
    return jsonify({
        'success': True,
        'message': 'Member activity report generated successfully',
        'data': {
            'top_members': member_data,
            'filters_applied': {
                'start_date': start_date.isoformat() if start_date else None,
                'end_date': end_date.isoformat() if end_date else None,
                'limit': limit if not (page and per_page) else None,
                'page': page if page and per_page else None,
                'per_page': per_page if page and per_page else None
            },
            'meta': meta,
            'links': links
        }
    }), 200

@reports_api_bp.route('/sales_by_category', methods=['GET'])
@token_required
@store_context_required
def sales_by_category_report():
    """Generate a report of sales by product category
    
    Query Parameters:
    - start_date: Optional, filter by start date (format: YYYY-MM-DD)
    - end_date: Optional, filter by end date (format: YYYY-MM-DD)
    """
    try:
        start_date = parse_date_param('start_date')
        end_date = parse_date_param('end_date')
    except ValueError as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 400

    # Query to get sales by category
    query = db.session.query(
        PosProduct.category,
        func.sum(PosTransaction.actual_amount).label('total_amount'),
        func.count(PosTransaction.id).label('transaction_count')
    ).join(
        PosSessionProduct, PosProduct.id == PosSessionProduct.product_id
    ).join(
        PosSession, PosSessionProduct.session_id == PosSession.id
    ).join(
        PosTransactionSession, PosSession.id == PosTransactionSession.session_id
    ).join(
        PosTransaction, PosTransactionSession.transaction_id == PosTransaction.id
    ).filter(
        PosTransaction.status == 'completed',
        PosTransaction.store_id == g.store_id,
        PosProduct.store_id == g.store_id,
        PosProduct.category.isnot(None)
    )

    if start_date:
        query = query.filter(func.date(PosTransaction.created_at) >= start_date)
    if end_date:
        query = query.filter(func.date(PosTransaction.created_at) <= end_date)

    results = query.group_by(PosProduct.category).all()

    # Calculate total for percentage calculation
    total_amount = sum(decimal_to_float(amount) for _, amount, _ in results)

    category_data = [{
        'category': category or 'Uncategorized',
        'total_amount': decimal_to_float(amount),
        'transaction_count': count,
        'percentage': round((decimal_to_float(amount) / total_amount * 100), 2) if total_amount > 0 else 0
    } for category, amount, count in results]

    # Sort by total amount descending
    category_data.sort(key=lambda x: x['total_amount'], reverse=True)

    # Log the report generation
    logger.info(f"Sales by category report generated: start_date={start_date}, end_date={end_date}")
    
    return jsonify({
        'success': True,
        'message': 'Sales by category report generated successfully',
        'data': {
            'categories': category_data,
            'filters_applied': {
                'start_date': start_date.isoformat() if start_date else None,
                'end_date': end_date.isoformat() if end_date else None
            }
        }
    }), 200

@reports_api_bp.route('/product_performance', methods=['GET'])
@token_required
@store_context_required
def product_performance_report():
    """Generate a product performance report
    
    Query Parameters:
    - start_date: Optional, filter by start date (format: YYYY-MM-DD)
    - end_date: Optional, filter by end date (format: YYYY-MM-DD)
    - category: Optional, filter by product category
    - limit: Optional, limit the number of top products (default: 10)
    """
    try:
        start_date = parse_date_param('start_date')
        end_date = parse_date_param('end_date')
        category = request.args.get('category')
        limit = request.args.get('limit', default=10, type=int)
    except ValueError as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 400

    # Query for top performing products
    top_products_query = db.session.query(
        PosProduct.id,
        PosProduct.name,
        PosProduct.category,
        func.sum(PosSessionProduct.quantity).label('quantity_sold'),
        func.sum(PosSessionProduct.quantity * PosSessionProduct.price).label('revenue'),
        PosProduct.stock,
        PosProduct.status
    ).join(
        PosSessionProduct, PosProduct.id == PosSessionProduct.product_id
    ).join(
        PosSession, PosSessionProduct.session_id == PosSession.id
    ).join(
        PosTransactionSession, PosSession.id == PosTransactionSession.session_id
    ).join(
        PosTransaction, PosTransactionSession.transaction_id == PosTransaction.id
    ).filter(
        PosTransaction.status == 'completed',
        PosTransaction.store_id == g.store_id,
        PosProduct.store_id == g.store_id
    )

    if start_date:
        top_products_query = top_products_query.filter(func.date(PosTransaction.created_at) >= start_date)
    if end_date:
        top_products_query = top_products_query.filter(func.date(PosTransaction.created_at) <= end_date)
    if category:
        top_products_query = top_products_query.filter(PosProduct.category == category)

    top_products = top_products_query.group_by(
        PosProduct.id, PosProduct.name, PosProduct.category, PosProduct.stock, PosProduct.status
    ).order_by(func.sum(PosSessionProduct.quantity).desc()).limit(limit).all()

    # Query for low stock products (stock < 20)
    low_stock_query = db.session.query(PosProduct).filter(
        PosProduct.store_id == g.store_id,
        PosProduct.stock < 20,
        PosProduct.status == 'active'
    ).order_by(PosProduct.stock.asc()).limit(10)

    low_stock_products = low_stock_query.all()

    # Format top products data
    top_products_data = [{
        'id': product_id,
        'name': name,
        'category': category,
        'quantity_sold': int(quantity_sold) if quantity_sold else 0,
        'revenue': decimal_to_float(revenue),
        'stock_level': stock,
        'stock_status': 'in_stock' if stock > 20 else 'low_stock' if stock > 0 else 'out_of_stock'
    } for product_id, name, category, quantity_sold, revenue, stock, status in top_products]

    # Format low stock products data
    low_stock_data = [{
        'id': product.id,
        'name': product.name,
        'category': product.category,
        'stock_level': product.stock,
        'price': decimal_to_float(product.price),
        'status': product.status
    } for product in low_stock_products]

    # Log the report generation
    logger.info(f"Product performance report generated: start_date={start_date}, end_date={end_date}, category={category}")
    
    return jsonify({
        'success': True,
        'message': 'Product performance report generated successfully',
        'data': {
            'top_products': top_products_data,
            'low_stock_products': low_stock_data,
            'filters_applied': {
                'start_date': start_date.isoformat() if start_date else None,
                'end_date': end_date.isoformat() if end_date else None,
                'category': category,
                'limit': limit
            }
        }
    }), 200

@reports_api_bp.route('/resource_utilization', methods=['GET'])
@token_required
@store_context_required
def resource_utilization_report():
    """Generate a resource utilization report
    
    Query Parameters:
    - start_date: Optional, filter by start date (format: YYYY-MM-DD)
    - end_date: Optional, filter by end date (format: YYYY-MM-DD)
    - resource_type: Optional, filter by resource type
    """
    try:
        start_date = parse_date_param('start_date')
        end_date = parse_date_param('end_date')
        resource_type = request.args.get('resource_type')
    except ValueError as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 400

    # Import Resource model (assuming it exists)
    from applications.models.pos_resource import PosResource

    # Query for resource utilization stats
    resource_stats_query = db.session.query(
        PosResource.id,
        PosResource.name,
        PosResource.type,
        func.count(PosSession.id).label('total_sessions'),
        func.sum(
            func.extract('epoch', PosSession.ended_at - PosSession.started_at) / 3600
        ).label('total_hours'),
        func.sum(PosSession.total_amount).label('revenue')
    ).join(
        PosSession, PosResource.id == PosSession.resource_id
    ).join(
        PosTransactionSession, PosSession.id == PosTransactionSession.session_id
    ).join(
        PosTransaction, PosTransactionSession.transaction_id == PosTransaction.id
    ).filter(
        PosTransaction.status == 'completed',
        PosTransaction.store_id == g.store_id,
        PosResource.store_id == g.store_id,
        PosSession.ended_at.isnot(None)
    )

    if start_date:
        resource_stats_query = resource_stats_query.filter(func.date(PosSession.started_at) >= start_date)
    if end_date:
        resource_stats_query = resource_stats_query.filter(func.date(PosSession.started_at) <= end_date)
    if resource_type:
        resource_stats_query = resource_stats_query.filter(PosResource.type == resource_type)

    resource_stats = resource_stats_query.group_by(
        PosResource.id, PosResource.name, PosResource.type
    ).order_by(func.count(PosSession.id).desc()).all()

    # Calculate usage patterns
    peak_hours_query = db.session.query(
        func.extract('hour', PosSession.started_at).label('hour'),
        func.count(PosSession.id).label('session_count')
    ).join(
        PosTransactionSession, PosSession.id == PosTransactionSession.session_id
    ).join(
        PosTransaction, PosTransactionSession.transaction_id == PosTransaction.id
    ).filter(
        PosTransaction.status == 'completed',
        PosTransaction.store_id == g.store_id,
        PosSession.ended_at.isnot(None)
    )

    if start_date:
        peak_hours_query = peak_hours_query.filter(func.date(PosSession.started_at) >= start_date)
    if end_date:
        peak_hours_query = peak_hours_query.filter(func.date(PosSession.started_at) <= end_date)

    peak_hours = peak_hours_query.group_by(
        func.extract('hour', PosSession.started_at)
    ).order_by(func.count(PosSession.id).desc()).limit(3).all()

    # Format resource stats data
    resource_stats_data = [{
        'resource_id': resource_id,
        'resource_name': name,
        'resource_type': resource_type,
        'total_sessions': int(total_sessions) if total_sessions else 0,
        'total_hours': decimal_to_float(total_hours) if total_hours else 0.0,
        'occupancy_rate': min(100.0, (decimal_to_float(total_hours) / (24 * 30)) * 100) if total_hours else 0.0,  # Approximate monthly occupancy
        'revenue': decimal_to_float(revenue) if revenue else 0.0
    } for resource_id, name, resource_type, total_sessions, total_hours, revenue in resource_stats]

    # Format usage patterns
    peak_hours_formatted = [f"{int(hour):02d}:00" for hour, _ in peak_hours]

    usage_patterns = {
        'peak_hours': peak_hours_formatted,
        'popular_days': ['Monday', 'Wednesday', 'Friday']  # Could be calculated from data
    }

    # Log the report generation
    logger.info(f"Resource utilization report generated: start_date={start_date}, end_date={end_date}, resource_type={resource_type}")
    
    return jsonify({
        'success': True,
        'message': 'Resource utilization report generated successfully',
        'data': {
            'resource_stats': resource_stats_data,
            'usage_patterns': usage_patterns,
            'filters_applied': {
                'start_date': start_date.isoformat() if start_date else None,
                'end_date': end_date.isoformat() if end_date else None,
                'resource_type': resource_type
            }
        }
    }), 200

@reports_api_bp.route('/dashboard_summary', methods=['GET'])
@token_required
@store_context_required
def dashboard_summary_report():
    """Generate a dashboard summary report with key metrics
    
    Query Parameters:
    - date: Optional, specific date for metrics (format: YYYY-MM-DD, default: today)
    """
    try:
        target_date = parse_date_param('date')
        if not target_date:
            target_date = datetime.date.today()
    except ValueError as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 400

    # Daily sales for the target date
    daily_sales_query = db.session.query(
        func.sum(PosTransaction.actual_amount).label('daily_sales')
    ).filter(
        PosTransaction.status == 'completed',
        PosTransaction.store_id == g.store_id,
        func.date(PosTransaction.created_at) == target_date
    )

    daily_sales_result = daily_sales_query.one_or_none()
    daily_sales = decimal_to_float(daily_sales_result.daily_sales) if daily_sales_result and daily_sales_result.daily_sales else 0.0

    # Monthly revenue (current month)
    start_of_month = target_date.replace(day=1)
    monthly_revenue_query = db.session.query(
        func.sum(PosTransaction.actual_amount).label('monthly_revenue')
    ).filter(
        PosTransaction.status == 'completed',
        PosTransaction.store_id == g.store_id,
        func.date(PosTransaction.created_at) >= start_of_month,
        func.date(PosTransaction.created_at) <= target_date
    )

    monthly_revenue_result = monthly_revenue_query.one_or_none()
    monthly_revenue = decimal_to_float(monthly_revenue_result.monthly_revenue) if monthly_revenue_result and monthly_revenue_result.monthly_revenue else 0.0

    # Calculate sales growth (compare with previous month)
    previous_month_start = (start_of_month - datetime.timedelta(days=1)).replace(day=1)
    previous_month_end = start_of_month - datetime.timedelta(days=1)
    
    prev_monthly_revenue_query = db.session.query(
        func.sum(PosTransaction.actual_amount).label('prev_monthly_revenue')
    ).filter(
        PosTransaction.status == 'completed',
        PosTransaction.store_id == g.store_id,
        func.date(PosTransaction.created_at) >= previous_month_start,
        func.date(PosTransaction.created_at) <= previous_month_end
    )

    prev_monthly_result = prev_monthly_revenue_query.one_or_none()
    prev_monthly_revenue = decimal_to_float(prev_monthly_result.prev_monthly_revenue) if prev_monthly_result and prev_monthly_result.prev_monthly_revenue else 0.0
    
    sales_growth = 0.0
    if prev_monthly_revenue > 0:
        sales_growth = round(((monthly_revenue - prev_monthly_revenue) / prev_monthly_revenue) * 100, 2)

    # Orders today
    orders_today_query = db.session.query(
        func.count(PosTransaction.id).label('orders_today')
    ).filter(
        PosTransaction.status == 'completed',
        PosTransaction.store_id == g.store_id,
        func.date(PosTransaction.created_at) == target_date
    )

    orders_today_result = orders_today_query.one_or_none()
    orders_today = orders_today_result.orders_today if orders_today_result else 0

    # New members today
    members_today_query = db.session.query(
        func.count(PosMember.id).label('members_today')
    ).filter(
        PosMember.store_id == g.store_id,
        func.date(PosMember.created_at) == target_date
    )

    members_today_result = members_today_query.one_or_none()
    members_today = members_today_result.members_today if members_today_result else 0

    # Resource utilization (percentage of resources in use today)
    from applications.models.pos_resource import PosResource
    
    total_resources_query = db.session.query(
        func.count(PosResource.id).label('total_resources')
    ).filter(
        PosResource.store_id == g.store_id,
        PosResource.status == 'active'
    )

    active_sessions_query = db.session.query(
        func.count(func.distinct(PosSession.resource_id)).label('resources_in_use')
    ).filter(
        PosSession.status == 'active'
    ).join(
        PosResource, PosSession.resource_id == PosResource.id
    ).filter(
        PosResource.store_id == g.store_id
    )

    total_resources_result = total_resources_query.one_or_none()
    active_sessions_result = active_sessions_query.one_or_none()
    
    total_resources = total_resources_result.total_resources if total_resources_result else 0
    resources_in_use = active_sessions_result.resources_in_use if active_sessions_result else 0
    
    resource_utilization = round((resources_in_use / total_resources) * 100, 1) if total_resources > 0 else 0.0

    # Recent transactions (last 5 for today)
    recent_transactions_query = db.session.query(PosTransaction).options(
        joinedload(PosTransaction.member)
    ).filter(
        PosTransaction.status == 'completed',
        PosTransaction.store_id == g.store_id,
        func.date(PosTransaction.created_at) == target_date
    ).order_by(desc(PosTransaction.created_at)).limit(5)

    recent_transactions = recent_transactions_query.all()

    recent_transactions_data = [{
        'id': transaction.id,
        'member': transaction.member.name if transaction.member else 'Walk-in Customer',
        'amount': decimal_to_float(transaction.actual_amount),
        'time': transaction.created_at.strftime('%H:%M')
    } for transaction in recent_transactions]

    # Log the report generation
    logger.info(f"Dashboard summary report generated for date: {target_date}")
    
    return jsonify({
        'success': True,
        'message': 'Dashboard summary generated successfully',
        'data': {
            'daily_sales': daily_sales,
            'monthly_revenue': monthly_revenue,
            'sales_growth': sales_growth,
            'orders_today': orders_today,
            'members_today': members_today,
            'resource_utilization': resource_utilization,
            'recent_transactions': recent_transactions_data,
            'date': target_date.isoformat()
        }
    }), 200

# Additional report endpoints can be added here for future enhancements
