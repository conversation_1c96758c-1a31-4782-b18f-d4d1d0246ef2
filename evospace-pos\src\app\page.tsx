"use client";

import React, { useState, useEffect } from 'react';
import {
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemText,
  Avatar,
  ListItemAvatar,
  Stack,
  Button,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  Skeleton
} from '@mui/material';
import {
  AttachMoney,
  TrendingUp,
  People,
  Room,
  ArrowUpward,
  ArrowDownward,
  AddShoppingCart,
  PersonAdd,
  Inventory,
  MoreVert,
  Warning,
  Refresh
} from '@mui/icons-material';
import { Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  ArcElement
} from 'chart.js';
import { useTheme } from '@/lib/theme';
import usePosStore from '@/lib/store';
import ProtectedPage from '@/components/ProtectedPage';
import { reportService } from '@/lib/apiService';
import { format, subDays, subDays, startOfMonth, endOfMonth } from 'date-fns';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  ChartTooltip,
  Legend,
  ArcElement
);

(,  b  5  ' g,a(54, 162, 235, 1)',2, 192, 1)',
          ' gb(255,99, 132, 1)',
          ' gbt(255,,206, 86, 1)',s
  r       'dgbt(75, 192, 192, 1)',lr(p => p.ock<20
     ],
 ////Gl  o wtha,ckpodcesht 
 con  },lwSckPldecyspodct.filr(p => p.ock<20
  
 // Sls y caSesry cht st[
 con{
    lbls: dSt  s.sal/sByCelabe.mp(Ptem =ciaed.catsgohy),oaddSe.  {.map(item =>item.amount)
    tst[   backgr  ndCololts old',
 {
       /  bel:/'S rpb Amrunp'a(5,cha1t d2ta35, 0.7 ',   data: dashboardStats.popularProducts.map(product => product.sales),
 cor     atsb da'hboaddSe.  {.map(igem =>btem.amouat)55ol99,132,0.7',
        b ckgr  ndCo oltg'old',5, 206, 86, 0.7)', 0.7)',
          ' gba(54, 162, 235, 0.7 ',   da a:  'shboardStats.poprgarProd,16s.,ap1pr duc  =>rgb(255,.9al),
    (5,   'gCba,255ol99,132,0.7',)',orderColor: [
   ,191 g, 3gb,(255, 206, 86, 0.7),, 0.7)',192, 1)',
    g   ,8'iba75d192,19,t.7',
         ,1'      'rgb255, 99, 132, 0.7',
  ,    '(13,Clo: [ 2550.7)',
        '(54,162,235, 1)',
        'rgb255,99,132,1',
r5'       ' g(255206, 86, 1)',rClo: [
  };      ''gb75,192192,1',92,192,1)'
  //d   ]c, co' gb5(54,r162d 235, 1)',ucsDa = {
       gedrb25Wiathb 1r, 86, 1)',dStat,.popularProducs.ap(pruct>pruct.nme,
     },
r5,,)',
  };

  // P pu{ddrprbduc(s:c1, 10data
, con 2   lbe55Un'sSo'Da ={
    labela:kdalob ardStat.popularProduc.ap(pruc>puct.nm,
  dts: [
     3{33b rde'Wih: 1,
      lbe:'UttuSol',
    ],  daa:rb.p(pu5s.p(p,206 >0)',s),
};bagundColo: [
          '75,192,19, .7'
  'b35, 37, 39, 0.7)',        'gb25599, 132, 0.7'
 <rrourn'('g3(255,206, 86,20.7)',50.7),
      B1}'gb25599,132,0.7'
    <Pro e'gb153, 102, 2550.7),x:'',Coljr: [
      <B]  x={{ fl xGugw: 1 }}>92, 192, 1)',ntent: 'space-between', alignItems: 'center', mb: 3 }}>
        bx:'',Coljr: [    'pgba(54, 162, 235, 1)',
          'ugbtC75, 192, 192, 1)',nt'nr:g'space-betwe2n',5,lig It0m6: 'center', mb: 3 }}>, 861)
      g 93'pgb154, 162, 235, 1',
         r'rgb,1255,0206,,861
        pr'gb255,99,1321
          'rgba(153, 102,g25r, 1)',  borderWidth: 1,<Box>
     t ],phy>
     ],eWidth: 1<x>
      }  <Bt 
    ,
};
  };vaiant="cnaind" 
  n 
    <   n= "IoP{in><AdddhoppingC"rt />} 
      <B x  x={{ f xG w 1}}>
    retu<Boxrsx={{n (p  'ex', jufifyCen="'sppce-"eween' ignItm:center mb:3}}>
    <   te<Typ<gdhp>/i="4">
       <BlD x={{ flexG sw 1}}>
          </Tpoaoy>
        <Bxx>
        >   <Bn
             { n="  neii"ntent="'sppce-"eween', ignItm: 'center', mb:3}}>
              <>pIc={<ArSppigC/>}
             lx={{1}}
             phf="/p">
          >ox>
        {/    NuwcSil/       vin="contin"
        < r{</Br>ppingC/>}
        la</B x> ={i Da kMx1e ? 2 1}
       </B x>
        
p       {/* QxkAion*/}
       <ap 
          i={iDkM?2 1} 
          x={
         2 : 2, 
            bil4 
            gco 4isD kMe  'bckgrunpp':'#fff'
         c  b<P  <Radiu/: 2rriDkM ? 'backgun.pper':'#fff'
           }    /ordBoRadiux: 2
        >    }}
          <>ypngrpiDvin="M6" g2tBtto>Quik Acion</Typgrph>
        <S a kdip={{ x'cln', s: 'oow'a}} spcing=p2}>h viat="h6" gueBottom>Quick A</Typography>
          <ut  vrint="<uSint" skIcdn={<Pis Aod />} mf="/memne ">As: M}be}</Button>ing2}>
        {/  <Bu Q<n vkAnant="outninv "rsiar*I}n={<Inven=ryu/>} n f="/prosucts">MIn<erInvenson</Butto>>ef="/membes">Add Member</Button>
     B   <a <B n v rBnn="u ind"   rtIcon={< oo />} hr</="/Starces">Resource Bookk></Buton>
        en</Stack>    DkMe?2 : 1} 
        </Pa er>      {/* Stats C{ds */}
  x{    iplay'flx', lexWp: 'wrp', gp: 3, mb: 4 }}>
        {/* S <B Csx=s */}: '1 1 250x', minWih: { xs: '100%', : 'clc(50% - 12x)', md: 'calc25% - 18x)' }}}
        <Box  x={{elispley:t'flix', fl2xWr p 2'w=a,', gp:i3Dab: 4 }}> ? 2 : 1} 
          <B x  x={{ fb:x4 '1 1 250x', m  W h: { xs:{'100%',m: 'cc50% - 12px)', md: 'lc(25% - 18px ' }c}}>or 4isDa kMe ? 'bckgrun.paper' : '#fff',
            <C      }    borderRadius: 2
               levi}n={iskMe?2:} 
     Tp       sx=y{             b rd kL{f :c'4px u'lisw'acing=p2}>hy variant="h6" gutterBottom>Quick Actions</Typography>
                b<BaerLnf : '4px s=li<',
u               td s{rColor:i'poimaAy.mo/  ,ef="/memne s">As: M}mbe}</Button>acing={2}>
     B     B   bbgooror: i D rkM <o ? 'r b p0, 123, 255, 0.1)' : 'S b. 0, 123, 255  0.05 ',       bgcolor: i DarkMod< ? 'rgba 0,{123, 255, 0.1)' : 'rgxfl0, 123, 255, 0.05e',: '1 1 250px', m  Wid h: { xs:{'100%',m: 'cc50% - 12px)', md: 'lc(25% - 18px)' } }}>
               evrioDikood: '?r  :'o&m 0.2h',} 
           { v  '&:ho r4':
     i'          a  form: '  anbretnY(-4pxs',orm: 'lr ns'amaY(-4pxa', ',
          vr      xS   ow: 3orm: '  an latnY(-4pxs',orm: 'translateY(-4px)',
                } w: 3ow: 3
                            }            }ow: 3
            >            }}            }             }
           <CConen>
              <B x  x={{ di p  y  'fi x',t  :gnI  c : 'nnn}dn'}}>
                  <Av   r  x={{  gco or  'pxim: x', in', Ir  2t}}m   <Avat r sx={{  gco or  'pxim:ex', in', Ir: 2t}}m
                   A<AtvanhM >r} /><Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  </Av   r>   <Av   r  x={{ <gcolBr: 'pximn', r: 2}}
                v<Bx>
                    <Typo   p y c lBx="xSc  a"ov ="bcoy2">xScnda" vr="body2">
           ip     o=  D i<yoSplls
<pp  o=       <<s{ia</Typography> y:<'flex' salignItems:{'centerp }}>ay: 'flex', alignItems: 'center' }}>
           ya         <Typography variant="h5">           ${dashboardStats.dailySales.toFixed(2)}
                      </Typography>
                      <Box sx={{ 
                        display: 'flex', 
                        alignItems: 'center', 
                        ml: 1,
                        color: dashboardStats.salesGrowth > 0 ? 'success.main' : 'error.main' 
                      }}>
                        {dashboardStats.salesGrowth > 0 ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />}
                        <Typography variant="body2" sx={{ ml: 0.5 }}>
                          {Math.abs(dashboardStats.salesGrowth)}%
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Box>
          
          <Box sx={{ flex: '1 1 250px', minWidth: { xs: '100%', sm: 'calc(50% - 12px)', md: 'calc(25% - 18px)' } }}>
            <Card 
              elevation={isDarkMode ? 2 : 1} 
              sx={{ 
                borderLeft: '4px solid',
                borderColor: 'secondary.main',
                bgcolor: isDarkMode ? 'rgba(108, 117, 125, 0.1)' : 'rgba(108, 117, 125, 0.05)',
                transition: 'transform 0.2s',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 3
                }
              }}
            >
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                    <TrendingUp />
                  </Avatar>
                  <Box>
                    <Typography color="textSecondary" variant="body2">
                      Monthly Revenue
                    </Typography>
                    <Typography variant="h5">
                      ${dashboardStats.monthlyRevenue.toFixed(2)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Box>
          
          <Box sx={{ flex: '1 1 250px', minWidth: { xs: '100%', sm: 'calc(50% - 12px)', md: 'calc(25% - 18px)' } }}>
            <Card 
              elevation={isDarkMode ? 2 : 1} 
              sx={{ 
                borderLeft: '4px solid',
                borderColor: 'success.main',
                bgcolor: isDarkMode ? 'rgba(40, 167, 69, 0.1)' : 'rgba(40, 167, 69, 0.05)',
                transition: 'transform 0.2s',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 3
                }
              }}
            >
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                    <People />
                  </Avatar>
                  <Box>
                    <Typography color="textSecondary" variant="body2">
                      Members Today
                    </Typography>
                    <Typography variant="h5">
                      {dashboardStats.membersToday}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Box>
          
          <Box sx={{ flex: '1 1 250px', minWidth: { xs: '100%', sm: 'calc(50% - 12px)', md: 'calc(25% - 18px)' } }}>
            <Card 
              elevation={isDarkMode ? 2 : 1} 
              sx={{ 
                borderLeft: '4px solid',
                borderColor: 'warning.main',
                bgcolor: isDarkMode ? 'rgba(255, 193, 7, 0.1)' : 'rgba(255, 193, 7, 0.05)',
                transition: 'transform 0.2s',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 3
                }
              }}
            >
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                    <Room />
                  </Avatar>
                  <Box>
                    <Typography color="textSecondary" variant="body2">
                      Orders Today
                    </Typography>
                    <Typography variant="h5">
                      {dashboardStats.ordersToday}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Box>
        </Box>
        
        {/* Charts */}
        <Stack spacing={3}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
            <Box sx={{ flex: '1 1 auto', minWidth: { xs: '100%', md: 'calc(66.666% - 12px)' } }}>
              <Paper 
                elevation={isDarkMode ? 2 : 1} 
                sx={{ 
                  p: 3, 
                  borderRadius: 2,
                  bgcolor: isDarkMode ? 'background.paper' : '#fff'
                }}
              >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="h6">
                    Sales by Category
                  </Typography>
                  <Tooltip title="More options">
                    <IconButton size="small">
                      <MoreVert fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>
                <Box sx={{ height: 300 }}>
                  <Bar 
                    data={salesByCategoryData} 
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: 'top',
                          labels: {
                            color: isDarkMode ? '#fff' : '#666'
                          }
                        },
                        title: {
                          display: false,
                        },
                      },
                      scales: {
                        x: {
                          grid: {
                            color: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                          },
                          ticks: {
                            color: isDarkMode ? '#fff' : '#666'
                          }
                        },
                        y: {
                          grid: {
                            color: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                          },
                          ticks: {
                            color: isDarkMode ? '#fff' : '#666'
                          }
                        }
                      }
                    }}
                  />
                </Box>
              </Paper>
            </Box>
            
            <Box sx={{ flex: '1 1 auto', minWidth: { xs: '100%', md: 'calc(33.333% - 12px)' } }}>
              <Paper 
                elevation={isDarkMode ? 2 : 1} 
                sx={{ 
                  p: 3, 
                  borderRadius: 2,
                  bgcolor: isDarkMode ? 'background.paper' : '#fff'
                }}
              >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="h6">
                    Popular Products
                  </Typography>
                  <Tooltip title="More options">
                    <IconButton size="small">
                      <MoreVert fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>
                <Box sx={{ height: 300 }}>
                  <Doughnut 
                    data={popularProductsData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: 'bottom',
                          labels: {
                            color: isDarkMode ? '#fff' : '#666'
                          }
                        },
                      },
                    }}
                  />
                </Box>
              </Paper>
            </Box>
          </Box>
          
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
            <Box sx={{ flex: '1 1 auto', minWidth: { xs: '100%', md: 'calc(50% - 12px)' } }}>
              <Paper 
                elevation={isDarkMode ? 2 : 1} 
                sx={{ 
                  p: 3, 
                  borderRadius: 2,
                  bgcolor: isDarkMode ? 'background.paper' : '#fff'
                }}
              >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="h6">
                    Recent Transactions
                  </Typography>
                  <Button size="small" href="/reports">View All</Button>
                </Box>
                <List>
                  {dashboardStats.recentTransactions.map((transaction) => (
                    <React.Fragment key={transaction.id}>
                      <ListItem>
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: 'primary.main' }}>
                            <AttachMoney />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={transaction.member}
                          secondary={transaction.time}
                        />
                        <Typography variant="body1" fontWeight="bold">
                          ${transaction.amount.toFixed(2)}
                        </Typography>
                      </ListItem>
                      <Divider variant="inset" component="li" />
                    </React.Fragment>
                  ))}
                </List>
              </Paper>
            </Box>
            
            <Box sx={{ flex: '1 1 auto', minWidth: { xs: '100%', md: 'calc(50% - 12px)' } }}>
              <Paper 
                elevation={isDarkMode ? 2 : 1} 
                sx={{ 
                  p: 3, 
                  borderRadius: 2,
                  bgcolor: isDarkMode ? 'background.paper' : '#fff'
                }}
              >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="h6">
                    Low Stock Items
                  </Typography>
                  <Button size="small" href="/products">Manage Inventory</Button>
                </Box>
                {lowStockProducts.length > 0 ? (
                  <List>
                    {lowStockProducts.slice(0, 5).map((product) => (
                      <ListItem key={product.id}>
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: product.stock < 10 ? 'error.main' : 'warning.main' }}>
                            <Warning />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={product.name}
                          secondary={`Category: ${product.category}`}
                        />
                        <Chip 
                          label={`${product.stock} left`} 
                          color={product.stock < 10 ? "error" : "warning"} 
                          size="small" 
                        />
                      </ListItem>
                    ))}
                  </List>
                ) : (
                  <Box sx={{ textAlign: 'center', py: 4 }}>
                    <Typography variant="body1" color="text.secondary">
                      All products are well-stocked
                    </Typography>
                  </Box>
                )}
              </Paper>
            </Box>
          </Box>
        </Stack>
      </Box>
    </ProtectedPage>
  );
}
