'use client';

import { useEffect, useState, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import usePosStore from '@/lib/store';

interface ProtectedPageProps {
  children: ReactNode;
}

export default function ProtectedPage({ children }: ProtectedPageProps) {
  const router = useRouter();
  const { authUser, loadAuthToken } = usePosStore();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAuthAndRedirect = async () => {
      setIsLoading(true); // Ensure loading state is active during check

      // Attempt to load token if not already in authUser from the store.
      // This handles initial mount or cases where token might be in localStorage but not yet in store.
      if (!authUser.token) {
        await loadAuthToken();
      }

      // After attempting to load, get the definitive current state from the store.
      const currentTokenInStore = usePosStore.getState().authUser.token;

      if (!currentTokenInStore) {
        router.replace('/auth/login');
        // isLoading remains true, so loading screen persists during redirect
      } else {
        setIsLoading(false); // Token exists, allow content
      }
    };

    checkAuthAndRedirect();
  }, [authUser.token, loadAuthToken, router]); // Re-run when token status might change

  // Render logic:
  if (isLoading) {
    // If isLoading is true, it means we are either:
    // 1. Initially checking auth.
    // 2. Re-validating auth after a token change (e.g., logout).
    // 3. Redirecting to login.
    // In all these cases, show a loading indicator.
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>;
  }

  // If not loading, it implies auth check completed and token was found.
  // Render children only if token is confirmed.
  if (authUser.token) {
    return <>{children}</>;
  }

  // Fallback: This state might be briefly hit if authUser.token becomes null
  // immediately after isLoading was set to false but before a re-render/effect cycle
  // that would set isLoading back to true and redirect.
  // The effect should quickly handle this.
  return <div className="flex items-center justify-center min-h-screen">Verifying session...</div>;
}
