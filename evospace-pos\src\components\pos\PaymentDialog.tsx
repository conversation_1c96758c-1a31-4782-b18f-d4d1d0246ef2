"use client";

import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Stack,
  TextField,
  InputAdornment
} from '@mui/material';
import type { CartItem, Session, SessionItem } from '@/lib/types';

interface PaymentDialogProps {
  open: boolean;
  onClose: () => void;
  cart: CartItem[];
  cartTotal: () => number;
  getActiveSession: (resource_id: number) => Session | undefined;
  paymentMethod: 'cash' | 'card' | 'mobile';
  setPaymentMethod: (method: 'cash' | 'card' | 'mobile') => void;
  paymentAmount: string;
  setPaymentAmount: (amount: string) => void;
  onCompleteTransaction: () => void;
}

const PaymentDialog: React.FC<PaymentDialogProps> = ({
  open,
  onClose,
  cart,
  cartTotal,
  getActiveSession,
  paymentMethod,
  setPaymentMethod,
  paymentAmount,
  setPaymentAmount,
  onCompleteTransaction
}) => {
  const currentCartTotal = cartTotal(); // Calculate once per render

  return (
    <Dialog open={open} onClose={onClose} maxWidth="xs" fullWidth>
      <DialogTitle>Complete Payment</DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <Typography variant="body1" gutterBottom>
            Total Amount:
          </Typography>
          <Box>
            <Typography variant="h4" color="primary">
              ${currentCartTotal.toFixed(2)}
            </Typography>
            {
              <Box sx={{ mt: 2, maxHeight: '200px', overflowY: 'auto' }}> {/* Added scroll for long session details */}
                <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                  Session Details:
                </Typography>
                {cart.map((item) => {
                  const session = getActiveSession(item.id);
                  if (session) {
                    const durationInMinutes = Math.round(
                      (Date.now() - new Date(session.start_time).getTime()) / (1000 * 60)
                    );
                    const billableMinutes = Math.ceil(durationInMinutes / 10) * 10;
                    const billableHours = billableMinutes / 60;
                    const resourceTotal = billableHours * item.price;
                    
                    return (
                      <Box key={item.id} sx={{ mb: 2, fontSize: '0.875rem' }}> {/* Adjusted font size for compactness */}
                        <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 'bold' }}>
                          {item.name}
                        </Typography>
                        <Typography variant="caption" display="block" color="text.secondary" sx={{ pl: 2 }}>
                          • Duration: {durationInMinutes} min
                        </Typography>
                        <Typography variant="caption" display="block" color="text.secondary" sx={{ pl: 2 }}>
                          • Billed: {billableMinutes} min ({billableHours.toFixed(2)} hrs)
                        </Typography>
                        <Typography variant="caption" display="block" color="primary" sx={{ pl: 2, mb: 0.5 }}>
                          Resource Total: ${resourceTotal.toFixed(2)}
                        </Typography>

                        {session.services.length > 0 && (
                          <Box sx={{ mt: 0.5 }}>
                            <Typography variant="caption" display="block" color="text.secondary" sx={{ fontWeight: 'bold', pl: 2 }}>
                              Services for {item.name}:
                            </Typography>
                            {session.services.map((service: SessionItem) => {
                              const price = Number(service.price);
                              const quantity = Number(service.quantity);
                              const itemTotal = price * quantity;
                              return (
                                <Typography key={`service-${service.id}`} variant="caption" display="block" color="text.secondary" sx={{ pl: 3 }}>
                                  • {service.name}: ${!isNaN(price) ? price.toFixed(2) : '0.00'} x {!isNaN(quantity) ? quantity : 1} = ${!isNaN(itemTotal) ? itemTotal.toFixed(2) : '0.00'}
                                </Typography>
                              );
                            })}
                          </Box>
                        )}

                        {session.products.length > 0 && (
                          <Box sx={{ mt: 0.5 }}>
                            <Typography variant="caption" display="block" color="text.secondary" sx={{ fontWeight: 'bold', pl: 2 }}>
                              Products for {item.name}:
                            </Typography>
                            {session.products.map((product: SessionItem) => {
                              const price = Number(product.price);
                              const quantity = Number(product.quantity);
                              const itemTotal = price * quantity;
                              return (
                                <Typography key={`product-${product.id}`} variant="caption" display="block" color="text.secondary" sx={{ pl: 3 }}>
                                  • {product.name}: ${!isNaN(price) ? price.toFixed(2) : '0.00'} x {!isNaN(quantity) ? quantity : 1} = ${!isNaN(itemTotal) ? itemTotal.toFixed(2) : '0.00'}
                                </Typography>
                              );
                            })}
                          </Box>
                        )}
                      </Box>
                    );
                  }
                  return null;
                })}
              </Box>
            }
          </Box>
        </Box>

        <Typography variant="body1" gutterBottom>
          Payment Method:
        </Typography>
        <Stack direction="row" spacing={1} sx={{ mb: 3 }}>
          <Button 
            variant={paymentMethod === 'cash' ? 'contained' : 'outlined'}
            onClick={() => setPaymentMethod('cash')}
          >
            Cash
          </Button>
          <Button 
            variant={paymentMethod === 'card' ? 'contained' : 'outlined'}
            onClick={() => setPaymentMethod('card')}
          >
            Card
          </Button>
          <Button 
            variant={paymentMethod === 'mobile' ? 'contained' : 'outlined'}
            onClick={() => setPaymentMethod('mobile')}
          >
            Mobile
          </Button>
        </Stack>

        {paymentMethod === 'cash' && (
          <TextField
            label="Amount Received"
            type="number"
            fullWidth
            value={paymentAmount}
            onChange={(e) => setPaymentAmount(e.target.value)}
            InputProps={{
              startAdornment: <InputAdornment position="start">$</InputAdornment>,
              inputProps: { min: 0 } // Ensure non-negative input
            }}
            sx={{ mb: 2 }}
          />
        )}

        {paymentMethod === 'cash' && parseFloat(paymentAmount) >= currentCartTotal && paymentAmount !== '' && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2">
              Change: ${(parseFloat(paymentAmount) - currentCartTotal).toFixed(2)}
            </Typography>
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button 
          onClick={onCompleteTransaction} 
          variant="contained"
          disabled={
            (paymentMethod === 'cash' && (parseFloat(paymentAmount) < currentCartTotal || paymentAmount === ''))
          }
        >
          Complete Transaction
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PaymentDialog;
