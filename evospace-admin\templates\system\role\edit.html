<!DOCTYPE html>
<html>
<head>
    <title>角色编辑</title>
    {% include 'system/common/header.html' %}</head>
<body>
<form class="layui-form" action="">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item layui-hide">
                    <label class="layui-form-label">编号</label>
                    <div class="layui-input-block">
                        <input type="text" value="{{ role.id }}" name="roleId" lay-verify="required"
                               autocomplete="off" placeholder="角色编号" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">名称</label>
                    <div class="layui-input-block">
                        <input type="text" value="{{ role.name }}" name="roleName" lay-verify="required"
                               autocomplete="off" placeholder="角色名称" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标识</label>
                    <div class="layui-input-block">
                        <input type="text" value="{{ role.code }}" readonly name="roleCode" lay-verify="required"
                               autocomplete="off" placeholder="角色标识" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-block">
                        <input type="radio" name="enable" {% if role.enable == 1 %}checked {% endif %}value="1"
                               title="开启">
                        <input type="radio" name="enable" {% if role.enable == 0 %}checked {% endif %} value="0"
                               title="关闭">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">排序</label>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" name="sort" value="{{ role.sort }}" lay-verify="required" autocomplete="off"
                               placeholder="排序" min="0" step="1" lay-affix="number" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">描述</label>
                    <div class="layui-input-block">
                        <textarea placeholder="角色描述" name="details"
                                  class="layui-textarea">{{ role.details }}</textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button type="submit" class="layui-btn layui-btn-sm" lay-submit=""
                    lay-filter="role-update">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
</form>
{% include 'system/common/footer.html' %}
<script>
  layui.use(['form', 'jquery'], function () {
    let form = layui.form
    let $ = layui.jquery

    form.on('submit(role-update)', function (data) {
      $.ajax({
        url: '/system/role/update',
        data: JSON.stringify(data.field),
        dataType: 'json',
        contentType: 'application/json',
        type: 'put',
        success: function (result) {
          if (result.success) {
            layer.msg(result.msg, { icon: 1, time: 1000 }, function () {
              parent.layer.close(parent.layer.getFrameIndex(window.name))//关闭当前页
              parent.layui.table.reload('role-table')
            })
          } else {
            layer.msg(result.msg, { icon: 2, time: 1000 })
          }
        }
      })
      return false
    })
  })
</script>
<script>
</script>
</body>
</html>