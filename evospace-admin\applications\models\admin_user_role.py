from applications.extensions import db

# 创建中间表
user_role = db.<PERSON>(
    "admin_user_role",  # 中间表名称
    db.<PERSON>umn("id", db.Integer, primary_key=True, autoincrement=True, comment='标识'),  # 主键
    db.<PERSON>umn("user_id", db.<PERSON><PERSON>, db.<PERSON>("admin_user.id"), comment='用户编号'),  # 属性 外键
    db.<PERSON>umn("role_id", db.<PERSON><PERSON>, db.<PERSON>("admin_role.id"), comment='角色编号'),  # 属性 外键
)
