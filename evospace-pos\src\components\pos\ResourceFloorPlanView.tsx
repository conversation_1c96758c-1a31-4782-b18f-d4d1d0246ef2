"use client";

import React, { useState, useEffect, useRef } from 'react';
import { Box, Typography } from '@mui/material';
import { Resource, ResourceStatus, Session } from '@/lib/types';
import SessionDuration from './SessionDuration';

interface ResourceFloorPlanViewProps {
  resources: Resource[];
  selectedResource: number | null;
  onResourceSelect: (resource_id: number) => void;
  getActiveSession: (resource_id: number) => Session | undefined;
  onBackgroundClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
  currentResourceStatusFilter: ResourceStatus | 'all';
  currentSearchQuery: string;
  currentSelectedFloor: number | string;
  currentSelectedZone: string | null | 'all';
  onShowInfoAlert: (title: string, message: string) => void;
}

const ResourceFloorPlanView: React.FC<ResourceFloorPlanViewProps> = (props) => {
  // Calculate grid dimensions based on resources and their sizes
  const maxWidth = Math.max(...props.resources.map(r => {
    return (r.x || 0) + (r.width || 1);
  }), 0);
  
  const maxHeight = Math.max(...props.resources.map(r => {
    return (r.y || 0) + (r.height || 1);
  }), 0);
  
  // Constants for grid layout
  const cellSize = 50; // Base size for a grid cell in pixels (1x1 = 50x50)
  const gridGap = 10; // Gap between cells in pixels
  const gridWidth = maxWidth * cellSize + (maxWidth - 1) * gridGap;
  const gridHeight = maxHeight * cellSize + (maxHeight - 1) * gridGap;
  
  // State for pan functionality
  const [isDragging, setIsDragging] = useState(false);
  const [startPoint, setStartPoint] = useState({ x: 0, y: 0 });
  const [translate, setTranslate] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Helper function to check if a resource matches the current filters (status, search, zone)
  // This is now applied to resources already filtered by floor.
  const isResourceActive = (resource: Resource): boolean => {
    const statusMatch = props.currentResourceStatusFilter === 'all' || resource.status === props.currentResourceStatusFilter;
    const searchMatch = props.currentSearchQuery === '' || resource.name.toLowerCase().includes(props.currentSearchQuery.toLowerCase());
    // Floor match is handled by pre-filtering the resources array before mapping.
    const zoneMatch = props.currentSelectedZone === 'all' || props.currentSelectedZone === null || resource.zone === props.currentSelectedZone;
    return statusMatch && searchMatch && zoneMatch; // Removed floorMatch from here
  };

  // Function to calculate resource size based on size field
  const getResourceSize = (resource: Resource) => {
    const sizeWidth = resource.width || 1;
    const sizeHeight = resource.height || 1;
    
    return {
      width: sizeWidth * cellSize + (sizeWidth - 1) * gridGap,
      height: sizeHeight * cellSize + (sizeHeight - 1) * gridGap
    };
  };
  
  // Function to get position based on x and y coordinates
  const getResourcePosition = (resource: Resource) => {
    const x = resource.x || 0;
    const y = resource.y || 0;
    
    return {
      top: y * (cellSize + gridGap),
      left: x * (cellSize + gridGap)
    };
  };
  
  // Function to get resource color based on status and active state
  const getResourceColor = (status: string, hasActiveSession: boolean, isActive: boolean) => {
    if (!isActive) {
      return {
        bg: 'rgba(189, 189, 189, 0.4)',
        border: 'grey.400',
        glow: 'none',
        textColor: 'grey.700'
      };
    }

    if (hasActiveSession) {
      return {
        bg: 'rgba(33, 150, 243, 0.9)',
        border: 'primary.main',
        glow: '0 0 15px rgba(33, 150, 243, 0.5)',
        textColor: 'white'
      };
    }

    switch (status) {
      case 'available':
        return {
          bg: 'rgba(76, 175, 80, 0.7)',
          border: 'success.main',
          glow: 'none',
          textColor: 'white'
        };
      case 'booked':
        return {
          bg: 'rgba(244, 67, 54, 0.7)',
          border: 'error.main',
          glow: 'none',
          textColor: 'white'
        };
      case 'maintenance':
        return {
          bg: 'rgba(255, 152, 0, 0.7)',
          border: 'warning.main',
          glow: 'none',
          textColor: 'white'
        };
      case 'in-use':
        return {
          bg: 'rgba(33, 150, 243, 0.7)',
          border: 'primary.main',
          glow: 'none',
          textColor: 'white'
        };
      default:
        return {
          bg: 'rgba(158, 158, 158, 0.7)',
          border: 'text.secondary',
          glow: 'none',
          textColor: 'white'
        };
    }
  };
  
  // Handle mouse down for dragging
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    const targetElement = e.target as HTMLElement;
    if (targetElement.classList.contains('floor-plan-container') || 
        targetElement.classList.contains('grid-background')) {
      
      if (props.onBackgroundClick) {
        props.onBackgroundClick(e);
      }

      setIsDragging(true);
      setStartPoint({ x: e.clientX - translate.x, y: e.clientY - translate.y });
    }
  };
  
  // Handle mouse move for dragging
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (isDragging) {
      const newX = e.clientX - startPoint.x;
      const newY = e.clientY - startPoint.y;
      setTranslate({ x: newX, y: newY });
    }
  };
  
  // Handle mouse up to stop dragging
  const handleMouseUp = () => {
    setIsDragging(false);
  };
  
  // Add event listeners for mouse up outside the component
  useEffect(() => {
    const handleGlobalMouseUp = () => {
      if (isDragging) {
        setIsDragging(false);
      }
    };
    
    window.addEventListener('mouseup', handleGlobalMouseUp);
    return () => {
      window.removeEventListener('mouseup', handleGlobalMouseUp);
    };
  }, [isDragging]);
  
  return (
    <Box 
      className="floor-plan-container"
      sx={{
        position: 'relative', 
        width: '100%', 
        minHeight: `${Math.max(200, gridHeight + 100)}px`, 
        border: '1px solid #ccc', 
        borderRadius: 1, 
        overflow: 'hidden', 
        backgroundColor: '#f5f5f5', 
        mb: 3,
        p: 2,
        cursor: isDragging ? 'grabbing' : 'grab'
      }}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      ref={containerRef}
    >
      {/* Instructions */}
      <Box sx={{
        position: 'absolute', 
        top: 10, 
        right: 10, 
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
        padding: 1,
        borderRadius: 1,
        zIndex: 10,
        fontSize: '0.75rem',
        boxShadow: 1
      }}>
        <Typography variant="caption" display="block">
          • Click and drag to move the canvas
        </Typography>
      </Box>
      
      {/* Floor Plan Grid with transform for panning */}
      <Box sx={{
        position: 'relative', 
        width: `${gridWidth}px`, 
        height: `${gridHeight}px`,
        margin: '0 auto',
        transform: `translate(${translate.x}px, ${translate.y}px)`,
        transformOrigin: '0 0',
        transition: isDragging ? 'none' : 'transform 0.1s ease-out'
      }}>
        {/* Grid lines for reference */}
        <Box 
          className="grid-background"
          sx={{ 
            position: 'absolute', 
            top: 0, 
            left: 0, 
            width: '100%', 
            height: '100%', 
            backgroundImage: 'linear-gradient(#ddd 1px, transparent 1px), linear-gradient(90deg, #ddd 1px, transparent 1px)',
            backgroundSize: `${cellSize}px ${cellSize}px`,
            opacity: 0.5
          }} 
        />
        
        {/* Resources */}
        {props.resources
          .filter(resource => {
            if (props.currentSelectedFloor === 'all') {
              return true; // Show all resources if 'all' floors selected
            }
            return resource.floor?.toString() === props.currentSelectedFloor.toString();
          })
          .map(resource => {
            const size = getResourceSize(resource);
            const position = getResourcePosition(resource);
            const hasActiveSession = props.getActiveSession(resource.id) !== undefined;
            const isActive = isResourceActive(resource);
            const color = getResourceColor(resource.status, hasActiveSession, isActive);
            const resource_id = `T-${resource.id.toString().padStart(2, '0')}`;
            const activeSessionDetails = props.getActiveSession(resource.id);

            return (
              <Box
                key={resource.id}
                className={`resource-item ${props.selectedResource === resource.id ? 'selected' : ''}`}
                onClick={() => {
                  if (!isActive) return;

                  const currentActiveSession = props.getActiveSession(resource.id);
                  if (currentActiveSession) {
                    props.onResourceSelect(resource.id);
                  } else if (resource.status === 'available') {
                    props.onResourceSelect(resource.id);
                  } else {
                    props.onShowInfoAlert(
                      'Resource Unavailable',
                      `Resource ${resource.name} is currently ${resource.status} and cannot be selected.`
                    );
                  }
                }}
                sx={{ 
                  position: 'absolute',
                  top: position.top,
                  left: position.left,
                  width: size.width,
                  height: size.height,
                  backgroundColor: color.bg,
                  border: '2px solid',
                  borderColor: props.selectedResource === resource.id && isActive ? 'primary.dark' : color.border,
                  borderRadius: 2,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: isActive ? 'pointer' : 'default',
                  boxShadow: props.selectedResource === resource.id && isActive ? color.glow : (isActive ? '2px 2px 5px rgba(0,0,0,0.1)' : 'none'),
                  transition: 'all 0.2s ease',
                  opacity: isActive ? 1 : 0.6,
                  '&:hover': isActive ? {
                    transform: 'translateY(-2px)',
                    boxShadow: `0 4px 12px ${color.border === 'text.secondary' ? 'rgba(0,0,0,0.2)' : color.border }`,
                  } : {},
                  p: 1,
                  textAlign: 'center'
                }}
              >
                <Typography variant="body2" sx={{ color: color.textColor, fontWeight: 'bold', fontSize: '0.8rem', mb: 0.5 }}>
                  {resource_id}
                </Typography>
                
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                  {size.width >= 100 && size.height >= 40 && (
                    <Typography variant="body2" sx={{
                      color: color.textColor,
                      textAlign: 'center',
                      fontSize: '0.7rem',
                      mt: 0.25
                    }}>
                      {resource.name}
                    </Typography>
                  )}
                  {activeSessionDetails && isActive && (
                    <Typography component="div" variant="body2" sx={{
                      color: color.textColor,
                      textAlign: 'center',
                      fontSize: '0.7rem',
                      mt: 0.5,
                      fontWeight: 'bold'
                    }}>
                      <SessionDuration resource_id={resource.id} />
                    </Typography>
                  )}
                </Box>

                {(resource.status !== 'available' || hasActiveSession) && isActive && (
                  <Box sx={{ 
                    position: 'absolute',
                    top: 5,
                    right: 5,
                    width: 10,
                    height: 10,
                    borderRadius: '50%',
                    backgroundColor: hasActiveSession ? 'primary.main' :
                                      resource.status === 'in-use' ? 'primary.main' :
                                      resource.status === 'booked' ? 'error.main' : 'warning.main',
                    boxShadow: hasActiveSession ?
                              '0 0 10px rgba(33, 150, 243, 0.7)' :
                              '0 0 5px rgba(0,0,0,0.3)',
                    animation: hasActiveSession ?
                              'pulse 2s infinite' :
                              'none',
                    '@keyframes pulse': {
                      '0%': {
                        boxShadow: '0 0 0 0 rgba(33, 150, 243, 0.7)'
                      },
                      '70%': {
                        boxShadow: '0 0 0 10px rgba(33, 150, 243, 0)'
                      },
                      '100%': {
                        boxShadow: '0 0 0 0 rgba(33, 150, 243, 0)'
                      }
                    }
                  }} />
                )}
              </Box>
            );
          })}
      </Box>
    </Box>
  );
};

export default ResourceFloorPlanView;
